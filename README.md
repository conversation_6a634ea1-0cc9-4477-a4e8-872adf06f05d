# property_dealers_directory

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.dev/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.dev/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.dev/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

======================================================================================
# Flutter Commands

Flutter Custom Commands
- fclean for flutter clean.
- fapk64 for build apk file.
- fopen for open apk file contain folder.
- fbundle for make bundle file.
- fbopen for open bundle file contain folder.

## For Flutter Web Build & Deploy
- Build Command: flutter build web --release
- Deploy Command: firebase deploy --only hosting:ilaaqa

## Tool Details

- Flutter Channel: Stable
- Flutter Version: 3.0.2
- Dart SDK version: 2.17.3 (stable)
- Android targetSdkVersion: 33
- Android minSdkVersion: 23
- IOS Platform Support: 12.3 or above

## Git Tag for Upload Build

- bahria_v6.2.6 for Bahria Plus
- dha_v6.2.6 for DHA Plus