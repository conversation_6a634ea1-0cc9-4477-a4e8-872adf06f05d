# This file contains the fastlane.tools configuration
# You can find the documentation at https://docs.fastlane.tools
#
# For a list of all available actions, check out
#
#     https://docs.fastlane.tools/actions
#
# For a list of all available plugins, check out
#
#     https://docs.fastlane.tools/plugins/available-plugins
#

# Uncomment the line if you want fastlane to automatically update itself
# update_fastlane

default_platform(:android)

platform :android do
  desc "Runs all the tests"
  lane :test do
    gradle(task: "test")
  end

  desc "Submit a new beta build to Google Play"
  lane :beta do
    sh "flutter build appbundle -v --no-deferred-components"
    upload_to_play_store(
      track: 'beta',
      aab: '../build/app/outputs/bundle/release/app-release.aab',
      json_key_data: ENV['PLAY_STORE_CONFIG_JSON'],
      )
  end

  desc "Promote beta track to prod"
  lane :promote_to_production do
    upload_to_play_store(
      track: 'beta',
      track_promote_to: 'production',
      skip_upload_changelogs: true,
      json_key_data: ENV['PLAY_STORE_CONFIG_JSON'],
      )
  end

  desc "Submit a new production build to Google Play"
  lane :production do
    sh "flutter build appbundle -v --no-deferred-components"
    upload_to_play_store(
      track: 'production',
      aab: '../build/app/outputs/bundle/release/app-release.aab',
      json_key_data: ENV['PLAY_STORE_CONFIG_JSON'],
    )
  end
end