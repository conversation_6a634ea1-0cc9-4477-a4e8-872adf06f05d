import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/pages/properties_list/properties_list_page.dart';
import 'package:property_dealers_directory/pages/splash_page.dart';
import 'package:property_dealers_directory/providers/adbanner_provider.dart';
import 'package:property_dealers_directory/widgets/notification_popup.dart';
import 'package:provider/provider.dart';

import 'controllers/admob_controller.dart';
import 'controllers/agency_request_controller.dart';
import 'controllers/city_selected.dart';
import 'controllers/favourite_property_controller.dart';
import 'controllers/feedback_controller.dart';
import 'controllers/locationData_controller.dart';
import 'controllers/login_controller.dart';
import 'controllers/user_plan_controller.dart';
import 'nav.dart';

class MyApp extends StatefulWidget {
  static late FirebaseAnalytics analytics;
  static FirebaseAnalyticsObserver? observer;
  static late FirebaseMessaging fcm;
  final LoginController controller = Get.put<LoginController>(LoginController());
  final UserPlanController userPlan = Get.put<UserPlanController>(UserPlanController());
  final LocationDataController location = Get.put<LocationDataController>(LocationDataController());
  final AdmobController admobController = Get.put<AdmobController>(AdmobController());
  final AgencyRequestController submitAgencyController = Get.put<AgencyRequestController>(AgencyRequestController());
  final FavouritePropertyController favController = Get.put<FavouritePropertyController>(FavouritePropertyController());
  final FeedbackController feedbackController = Get.put<FeedbackController>(FeedbackController());

  MyApp() {
//    if (!kIsWeb) {
    analytics = FirebaseAnalytics.instance;
    observer = FirebaseAnalyticsObserver(analytics: analytics);
    fcm = FirebaseMessaging.instance;
//    }
  }

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {

  @override
  void initState() {
    super.initState();
    // if (kIsWeb) return;
    adProvider = AdBannerProvider();
    fcmListeners();
  }

  @override
  Widget build(BuildContext context) {
    return SplashPage();
  }

  fcmListeners() async {
    RemoteMessage? initialMessage = await FirebaseMessaging.instance.getInitialMessage();
    if (initialMessage != null) {
      Nav.homePage(context);

      parseNotification(initialMessage);

    }
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      print('A new onMessageOpenedApp event was published!');
      parseNotification(message);
    });

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      print('A new onMessage event was published!');
      if(message.data.containsKey('ad_id'))
        return;
      parseNotification(message);
    });
  }

  void parseNotification(RemoteMessage initialMessage) {
    if(initialMessage.data.containsKey('ad_id')){
      Get.to(ChangeNotifierProvider(
          create: (_) => FiltersProvider(isDeepLink: true),
          child: PropertiesListPage(currentAd: '${initialMessage.data['ad_id']}')));
    } else if(initialMessage.data.containsKey('type') && initialMessage.data['type'] == 'popup'){
      print(initialMessage.data);
      _showItemDialog(initialMessage.data);
    }
  }

  void _showItemDialog(Map<String, dynamic> message) {
    // if (message['data']['staff_id'] != null && widget.controller.staff?.id.toString() == message['data']['staff_id'])
    //   return;
    Get.dialog(
        NotificationPopup(
      context: Get.context!,
      message: message,
    ),
      barrierDismissible: false,
    );
  }

  void _showItemDialog1(Map<String, dynamic> message) {
    if (message['data']['staff_id'] != null && widget.controller.staff?.id.toString() == message['data']['staff_id'])
      return;
    Get.dialog(AlertDialog(
      title: Text(message['notification']['title']),
      actions: <Widget>[
        TextButton(
          child: Text('View'),
          onPressed: () {
            Get.back();
            Get.to(PropertiesListPage());
          },
        ),
        TextButton(
          child: Text('Dismiss'),
          onPressed: () {
            Get.back();
          },
        )
      ],
    ));
  }
}
