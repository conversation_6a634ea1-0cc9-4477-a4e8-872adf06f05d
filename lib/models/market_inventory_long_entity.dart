import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/market_inventory_long_entity.g.dart';
import 'dart:convert';

@JsonSerializable()
class MarketInventoryLongEntity {

	int id = 0;
	String price = '';
	@JSONField(name: "extra_info")
	 String? extraInfo;
	int status = 0;
	@JSONField(name: "is_hot")
	bool isHot = false;
	@JSONField(name: "created_at")
	String createdAt = '';
	String category = '';
	String type = '';
	@JSONField(name: "staff_id")
	int staffId = 0;
	@JSONField(name: "city_id")
	int cityId = 0;
	@JSONField(name: "society_id")
	int societyId = 0;
	@JSONField(name: "block_id")
	int blockId = 0;
	@JSONField(name: "plot_id")
	int plotId = 0;
	@JSONField(name: "staff_name")
	String staffName = '';
	@JSONField(name: "staff_phone")
	String staffPhone = '';
	@JSONField(name: "agency_name")
	String agencyName = '';
	@JSONField(name: "staff_image")
	String staffImage = '';
	@JSONField(name: "city_name")
	String cityName = '';
	@JSONField(name: "society_name")
	String societyName = '';
	@JSONField(name: "society_short")
	String societyShort = '';
	@JSONField(name: "block_name")
	String blockName = '';
	@JSONField(name: "block_short")
	String blockShort = '';
	@JSONField(name: "plot_name")
	String plotName = '';
	@JSONField(name: "plot_size")
	String plotSize = '';
	double lat = 0.0;
	double long = 0.0;
	@JSONField(name: "map_url")
	String mapUrl = '';
	@JSONField(name: "min_zoom")
	int minZoom = 0;
	@JSONField(name: "max_zoom")
	int maxZoom = 0;
  
  MarketInventoryLongEntity();

  factory MarketInventoryLongEntity.fromJson(Map<String, dynamic> json) => $MarketInventoryLongEntityFromJson(json);

  Map<String, dynamic> toJson() => $MarketInventoryLongEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}