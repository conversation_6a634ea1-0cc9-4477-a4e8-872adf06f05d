import 'dart:convert';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/explore_short_entity.g.dart';

@JsonSerializable()
class ExploreShortEntity {

	late int id;
	late ExploreShortCategory category;
  late String city;
  late String society;
  String? block;
	@JSONField(name: "business_name")
	late String businessName;
  @JSONField(name: "business_logo")
  String? businessLogo;
	@JSONField(name: "telephone_1")
	late String telephone1;
	String? email;
	late String address;
	List<ExploreShortHours>? hours;
  
  ExploreShortEntity();

  factory ExploreShortEntity.fromJson(Map<String, dynamic> json) => $ExploreShortEntityFromJson(json);

  Map<String, dynamic> toJson() => $ExploreShortEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ExploreShortCategory {

	late int id;
	String? image;
	late String name;
  
  ExploreShortCategory();

  factory ExploreShortCategory.fromJson(Map<String, dynamic> json) => $ExploreShortCategoryFromJson(json);

  Map<String, dynamic> toJson() => $ExploreShortCategoryToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ExploreShortHours {

	late String day;
	late String opening;
	late String closing;
  
  ExploreShortHours();

  factory ExploreShortHours.fromJson(Map<String, dynamic> json) => $ExploreShortHoursFromJson(json);

  Map<String, dynamic> toJson() => $ExploreShortHoursToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}