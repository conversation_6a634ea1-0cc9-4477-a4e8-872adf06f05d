import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/property_entity.g.dart';
import 'package:property_dealers_directory/config.dart';
import 'package:property_dealers_directory/models/block_entity.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import 'package:property_dealers_directory/models/society_entity.dart';
import 'package:property_dealers_directory/services/remote_config.dart';

@JsonSerializable()
class PropertyEntity {

	PropertyEntity();

	factory PropertyEntity.fromJson(Map<String, dynamic> json) => $PropertyEntityFromJson(json);

	Map<String, dynamic> toJson() => $PropertyEntityToJson(this);

  int id = 0;
  @JSONField(name: "agency_id")
  String agencyId = '';
  @JSONField(name: "city_id")
  String cityId = '';
  @JSONField(name: "society_id")
  String societyId = '';
  @JSONField(name: "design_id")
  int designId = 0;
  String type = '';
  String note = '';
  String purpose = '';
  String category = '';
  String status = '';
  String title = '';
  int price = 0;
  int? size;
  @JSONField(name: "size_unit")
  String? sizeUnit;
  @JSONField(name: "type_v2")
  String typeV2 = '';
  @JSONField(name: "created_at")
  String? createdAt;
  @JSONField(name: "created_ago")
  String? createdAgo;
  @JSONField(name: "updated_at")
  String? updatedAt;
  @JSONField(name: "society_name")
  String societyName = '';
  @JSONField(name: "city_name")
  String cityName = '';
  @JSONField(name: "agency_name")
  String agencyName = '';
  @JSONField(name: "staff_name")
  String staffName = '';
  @JSONField(name: "staff_phone")
  String? staffPhone;
  @JSONField(name: "staff_image")
  String? staffImage;
  @JSONField(name: "is_featured")
  int? isFeatured;
  @JSONField(name: "is_hot")
  int? isHot;
  int impressions = 0;
  int clicks = 0;
  @JSONField(name: "phone_clicks")
  int phoneClicks = 0;
  @JSONField(name: "whatsapp_clicks")
  int whatsappClicks = 0;
  @JSONField(name: "staff_id")
  int staffId = 0;
  @JSONField(name: "keywords")
  List<String>? keywords;
  CityEntity? city;
  SocietyEntity? society;
  BlockEntity? block;

  bool? hotList = false;

  bool get banner => isFeatured == 1;

  String get noteCopy {
    return note+ "\n$staffName\n$staffPhone\n\n${Config.COPY_EXTRA_TEXT}";
  }

  String get slug =>
      '$type-in-${societyName.toLowerCase().replaceAll(' ', '-')}-${cityName.toLowerCase().replaceAll(' ', '-')}-$id';

  String get url => "${RemoteConfigService.webUrl}/property/$slug";

  String get image {
    return staffImage == null || staffImage!.isEmpty ? '' : RemoteConfigService.storageUrl + staffImage!;
  }

  highlight(String value) {
    if (keywords == null || keywords!.isEmpty) return value;
    String word = '';
    keywords!.forEach((element) {
      if (this.note.toLowerCase().contains(element.toLowerCase())) {
        word = element;
      }
    });
    return word;
  }
}
