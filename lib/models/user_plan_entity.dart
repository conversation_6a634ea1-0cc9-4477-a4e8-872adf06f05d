import 'package:property_dealers_directory/generated/json/user_plan_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class UserPlanEntity {

	UserPlanEntity();

	factory UserPlanEntity.fromJson(Map<String, dynamic> json) => $UserPlanEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserPlanEntityToJson(this);

	String name = '';
	@JSONField(name: "expired_at")
	String expiredAt = '';
	bool expired = false;
	List<UserPlanFeatures>? features;
}

@JsonSerializable()
class UserPlanFeatures {

	UserPlanFeatures();

	factory UserPlanFeatures.fromJson(Map<String, dynamic> json) => $UserPlanFeaturesFromJson(json);

	Map<String, dynamic> toJson() => $UserPlanFeaturesToJson(this);

	String name = '';
	String limit = '';
	int consumed = 0;
	@JSONField(name: "plan_id")
	int planId = 0;
}
