import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/home_entity.g.dart';

import 'package:property_dealers_directory/models/agency_short_entity.dart';
import 'package:property_dealers_directory/models/blog_entity.dart';
import 'package:property_dealers_directory/models/classified_short_entity.dart';
import 'package:property_dealers_directory/models/project_Short_entity.dart';


@JsonSerializable()
class HomeEntity {

	HomeEntity();

	factory HomeEntity.fromJson(Map<String, dynamic> json) => $HomeEntityFromJson(json);

	Map<String, dynamic> toJson() => $HomeEntityToJson(this);

	List<AgencyShortEntity>? agencies;
	List<ProjectShortEntity>? projects;
	List<ClassifiedShortEntity>? classifieds;
	List<BlogEntity>? blogs;

}
