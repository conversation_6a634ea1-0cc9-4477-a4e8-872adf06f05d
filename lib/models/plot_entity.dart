import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';
import 'package:property_dealers_directory/generated/json/plot_entity.g.dart';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/models/classified_entity.dart';

@JsonSerializable()
class PlotEntity extends DropDownAble{

	PlotEntity();

	factory PlotEntity.fromJson(Map<String, dynamic> json) => $PlotEntityFromJson(json);

  PlotEntity.fromClassifiedPlot(ClassifiedPlot p){
    id = p.id;
    plotNo = p.plotNo;
    lat = p.lat;
    long = p.long;
    streetNo = p.streetNo;
    streetType = p.streetType;
    isVilla = false;
  }

	Map<String, dynamic> toJson() => $PlotEntityToJson(this);

  int? id;
  @JSONField(name: "plot_no")
  String plotNo = '';
  double lat = 0.0;
  double long = 0.0;
  @JSONField(name: "street_no")
  String? streetNo = '';
  @JSONField(name: "street_type")
  String? streetType;
  String category = '';
  @JSONField(name: "plot_size")
  int plotSize = 0;
  @JSONField(name: "size_type")
  String sizeType = '';
  @JSONField(name: "block_id")
  double blockId = 0.0;
  @JSONField(name: "created_at")
  String? createdAt;
  @JSONField(name: "updated_at")
  String? updatedAt;
  @JSONField(name: "society_name")
  String societyName = '';
  @JSONField(name: "road_width")
  String roadWidth = '';
  String? note;
  List? features;
  @JSONField(name: "is_villa")
  bool isVilla = false;

  String get title => (isVilla ? 'Villa' : 'Plot') + ' - $plotNo ${streetType != 'None' ? '- $streetType $streetNo' : ''}';

  String get plotName => isVilla == false ? 'Plot - ' + plotNo: 'Villa - ' + plotNo;

  List<ClassifiedMarker>? classifieds;

  bool get hasClassifieds => classifieds!.isNotEmpty;
  @override
  String get dropDownText => title;


  @override
  int? get itemId => id;

  @override
  String toString() {
    return title;
  }
}

@JsonSerializable()
class ClassifiedMarker {

  ClassifiedMarker();

  factory ClassifiedMarker.fromJson(Map<String, dynamic> json) => $ClassifiedMarkerFromJson(json);

  Map<String, dynamic> toJson() => $ClassifiedMarkerToJson(this);

  int id = 0;
  @JSONField(name: "plot_id")
  int plotId = 0;
  double lat = 0.0;
  double long = 0.0;
  @JSONField(name: "is_hot")
  bool isHot = false;
  String category = '';
  String title = '';
}
