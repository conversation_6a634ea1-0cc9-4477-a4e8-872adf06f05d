import 'package:property_dealers_directory/generated/json/project_Short_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class ProjectShortEntity {

	ProjectShortEntity();

	factory ProjectShortEntity.fromJson(Map<String, dynamic> json) => $ProjectShortEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProjectShortEntityToJson(this);

	int id = 0;
	String logo = '';
	String title = '';
	String location = '';
	String type = '';
	@JSONField(name: "is_featured")
	bool isFeatured = false;
}
