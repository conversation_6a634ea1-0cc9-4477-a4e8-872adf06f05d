import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/society_file_entity.g.dart';
import 'dart:convert';
export 'package:property_dealers_directory/generated/json/society_file_entity.g.dart';

@JsonSerializable()
class SocietyFileEntity {
	int id = 0;
	String url = '';
	@JSONField(name: "file_name")
	String fileName = '';

	SocietyFileEntity();

	factory SocietyFileEntity.fromJson(Map<String, dynamic> json) => $SocietyFileEntityFromJson(json);

	Map<String, dynamic> toJson() => $SocietyFileEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}