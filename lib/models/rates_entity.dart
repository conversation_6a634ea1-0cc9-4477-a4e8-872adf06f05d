import 'package:property_dealers_directory/generated/json/rates_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class RateEntity {

	RateEntity();

	factory RateEntity.fromJson(Map<String, dynamic> json) => $RateEntityFromJson(json);

	Map<String, dynamic> toJson() => $RateEntityToJson(this);

	int id = 0;
	@JSONField(name: "block_id")
	int blockId = 0;
	String title = '';
	@JSONField(name: "updated_at")
	String? updatedAt;
	List<RateRates>? rates;
}

@JsonSerializable()
class RateRates {

	RateRates();

	factory RateRates.fromJson(Map<String, dynamic> json) => $RateRatesFromJson(json);

	Map<String, dynamic> toJson() => $RateRatesToJson(this);

	String max = '';
	String min = '';
	String size = '';
	@JSONField(name: "size_unit")
	String sizeUnit = '';
}
