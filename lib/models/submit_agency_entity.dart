import 'dart:convert';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/submit_agency_entity.g.dart';
import 'package:property_dealers_directory/models/society_entity.dart';

@JsonSerializable()
class SubmitAgencyEntity {

	@JSONField(name: "form_key")
	String formKey = '';
	String name = '';
	String designation = '';
	String phone = '';
	@JSONField(name: "agency_name")
	String agencyName = '';
	@JSONField(name: "ceo_name")
	String? ceoName;
	@JSONField(name: "ceo_phone_1")
	String? ceoPhone1;
	@JSONField(name: "ceo_phone_2")
	String? ceoPhone2;
	@JSONField(name: "landline_1")
	String? landline1;
	@JSONField(name: "landline_2")
	String? landline2;
	String? whatsapp;
	String? email;
	String? fax;
	@JSONField(name: "city_id")
	String cityId = '';
	@JSONField(name: "society_id")
	String societyId = '';
	String address = '';
	String? facebook;
	String? youtube;
	String? twitter;
	String? instagram;
	String? message;
	String? website;
	String? about;
	String source = '';
	@JSONField(name: "fcm_id")
	String fcmId = '';
	SocietyEntity? society;
  
  SubmitAgencyEntity();

  factory SubmitAgencyEntity.fromJson(Map<String, dynamic> json) => $SubmitAgencyEntityFromJson(json);

  Map<String, dynamic> toJson() => $SubmitAgencyEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}