import 'dart:convert';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/explore_entity.g.dart';
import 'package:property_dealers_directory/models/explore_short_entity.dart';

@JsonSerializable()
class ExploreEntity {

	late int id;
  late ExploreShortCategory category;
	late String city;
	late String society;
	String? block;
	@JSONField(name: "business_name")
	late String businessName;
	@JSONField(name: "business_logo")
	String? businessLogo;
	@JSONField(name: "business_description")
	String? businessDescription;
	@J<PERSON>NField(name: "telephone_1")
	late String telephone1;
	@<PERSON><PERSON><PERSON><PERSON>(name: "telephone_2")
	String? telephone2;
	String? email;
	String? facebook;
	String? instagram;
	String? website;
	late String address;
	@JSONField(name: "google_address")
	String? googleAddress;
	double? latitude;
	double? longitude;
	List<ExploreShortHours>? hours;
	List<ExploreShortCategory>? services;
	List<ExploreShortCategory>? features;
	List<ExploreCollections>? collections;
  
  ExploreEntity();

  factory ExploreEntity.fromJson(Map<String, dynamic> json) => $ExploreEntityFromJson(json);

  Map<String, dynamic> toJson() => $ExploreEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ExploreCollections {

	late int id;
	late String name;
	List<String>? images;
  
  ExploreCollections();

  factory ExploreCollections.fromJson(Map<String, dynamic> json) => $ExploreCollectionsFromJson(json);

  Map<String, dynamic> toJson() => $ExploreCollectionsToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}