import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/login_entity.g.dart';

import 'package:property_dealers_directory/models/agency_entity.dart';
import 'package:property_dealers_directory/models/staff_entity.dart';


@JsonSerializable()
class LoginEntity {

	LoginEntity();

	factory LoginEntity.fromJson(Map<String, dynamic> json) => $LoginEntityFromJson(json);

	Map<String, dynamic> toJson() => $LoginEntityToJson(this);

  AgencyEntity? agency;
  StaffEntity? staff;
  String token = '';
}
