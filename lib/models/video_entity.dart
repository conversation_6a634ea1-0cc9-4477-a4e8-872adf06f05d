import 'package:property_dealers_directory/generated/json/video_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class VideoEntity {

	VideoEntity();

	factory VideoEntity.fromJson(Map<String, dynamic> json) => $VideoEntityFromJson(json);

	Map<String, dynamic> toJson() => $VideoEntityToJson(this);

	int id = 0;
	String title = '';
	String description = '';
	@JSONField(name: "video_id")
	String videoId = '';
	@JSONField(name: "created_at")
	String? createdAt;
	@JSONField(name: "updated_at")
	String? updatedAt;
}
