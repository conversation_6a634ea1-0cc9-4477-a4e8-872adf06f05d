import 'package:property_dealers_directory/generated/json/project_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

import 'package:property_dealers_directory/models/classified_entity.dart';

@JsonSerializable()
class ProjectEntity {

	ProjectEntity();

	factory ProjectEntity.fromJson(Map<String, dynamic> json) => $ProjectEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProjectEntityToJson(this);

	int id = 0;
	String? logo;
	String title = '';
	List<ClassifiedMedia> media = [];
	@JSONField(name: "developer_name")
	String developerName = '';
	@JSONField(name: "developer_logo")
	String? developerLogo;
	@JSONField(name: "marketer_name")
	String marketerName = '';
	@JSONField(name: "marketer_logo")
	String? marketerLogo;
	String type = '';
	@JSONField(name: "is_active")
	bool isActive = false;
	@JSONField(name: "is_featured")
	bool isFeatured = false;
	String location = '';
	String address = '';
	String? lat;
	String? lng;
	String about = '';
	@JSONField(name: "contact_number")
	String contactNumber = '';
	List<ProjectItems?>? items;
}

@JsonSerializable()
class ProjectItems {

	ProjectItems();

	factory ProjectItems.fromJson(Map<String, dynamic> json) => $ProjectItemsFromJson(json);

	Map<String, dynamic> toJson() => $ProjectItemsToJson(this);

	int id = 0;
	String title = '';
	List<String>? photos;
	double size = 0.0;
	@JSONField(name: "size_unit")
	String sizeUnit = '';
	String type = '';
	@JSONField(name: "total_payment")
	int totalPayment = 0;
	List<ProjectItemsPlans?>? plans;
}

@JsonSerializable()
class ProjectItemsPlans {

	ProjectItemsPlans();

	factory ProjectItemsPlans.fromJson(Map<String, dynamic> json) => $ProjectItemsPlansFromJson(json);

	Map<String, dynamic> toJson() => $ProjectItemsPlansToJson(this);

	String title = '';
	String amount = '';
}
