import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/market_inventory_entity.g.dart';
import 'dart:convert';

@JsonSerializable()
class MarketInventoryEntity {

	int id = 0;
	String price = '';
	@JSONField(name: "extra_info")
	String extraInfo = '';
	int status = 0;
	@JSONField(name: "is_hot")
	int isHot = 0;
	@JSONField(name: "created_at")
	String createdAt = '';
	@JSONField(name: "staff_id")
	int staffId = 0;
	@JSONField(name: "city_id")
	int cityId = 0;
	@JSONField(name: "society_id")
	int societyId = 0;
	@JSONField(name: "block_id")
	int blockId = 0;
	@JSONField(name: "plot_id")
	int plotId = 0;
	@JSONField(name: "staff_name")
	String staffName = '';
	@JSONField(name: "type")
	String type = '';
	@JSONField(name: "category")
	String category = '';
	@JSONField(name: "agency_name")
	String agencyName = '';
	@JSONField(name: "staff_phone")
	String staffPhone = '';
	@JSONField(name: "city_name")
	String cityName = '';
	@JSONField(name: "society_name")
	String societyName = '';
	@JSONField(name: "society_short")
	String societyShort = '';
	@JSONField(name: "block_name")
	String blockName = '';
	@JSONField(name: "block_short")
	String blockShort = '';
	@JSONField(name: "plot_size")
	String plotSize = '';
	@JSONField(name: "plot_name")
	String plotName = '';
  
  MarketInventoryEntity();

  factory MarketInventoryEntity.fromJson(Map<String, dynamic> json) => $MarketInventoryEntityFromJson(json);

  Map<String, dynamic> toJson() => $MarketInventoryEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}