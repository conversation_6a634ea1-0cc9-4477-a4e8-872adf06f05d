import 'package:property_dealers_directory/generated/json/inventory_design_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class InventoryDesignEntity {

	InventoryDesignEntity();

	factory InventoryDesignEntity.fromJson(Map<String, dynamic> json) => $InventoryDesignEntityFromJson(json);

	Map<String, dynamic> toJson() => $InventoryDesignEntityToJson(this);

	int id = 0;
	String link = '';
	String image = '';
	@JSONField(name: "for_titanium")
	bool forTitanium = false;
}
