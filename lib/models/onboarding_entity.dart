import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/onboarding_entity.g.dart';
import 'dart:convert';

import 'package:property_dealers_directory/models/city_entity.dart';
export 'package:property_dealers_directory/generated/json/onboarding_entity.g.dart';

@JsonSerializable()
class OnboardingEntity {
	bool notificationHot = false;
	String notificationSocieties = '';
	CityEntity city = CityEntity();
	CityEntity society = CityEntity();
	String userType = '';
	bool tutorialDone = false;
	bool tutorialSkip = false;

	OnboardingEntity();

	factory OnboardingEntity.fromJson(Map<String, dynamic> json) => $OnboardingEntityFromJson(json);

	Map<String, dynamic> toJson() => $OnboardingEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}