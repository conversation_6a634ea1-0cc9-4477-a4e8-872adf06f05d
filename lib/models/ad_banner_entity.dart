import 'dart:convert';

import 'package:property_dealers_directory/generated/json/ad_banner_entity.g.dart';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';

export 'package:property_dealers_directory/generated/json/ad_banner_entity.g.dart';

@JsonSerializable()
class AdBannerEntity {
  int id = 0;
  String type = '';
  String image = '';
  String url = '';
  String position = '';

  AdBannerPosition get positionEnum => AdBannerPosition.fromString(position);

  bool get isValid => id != 0 && image.isNotEmpty;

  AdBannerEntity();

  factory AdBannerEntity.fromJson(Map<String, dynamic> json) =>
      $AdBannerEntityFromJson(json);

  Map<String, dynamic> toJson() => $AdBannerEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

enum AdBannerPosition {
  popup,
  inv_list,
  cf_list,
  inv_top,
  cf_top;

  static AdBannerPosition fromString(String value) {
    return switch (value) {
      'popup' => AdBannerPosition.popup,
      'inv_list' => AdBannerPosition.inv_list,
      'cf_list' => AdBannerPosition.cf_list,
      'inv_top' => AdBannerPosition.inv_top,
      'cf_top' => AdBannerPosition.cf_top,
      _ => AdBannerPosition.popup
    };
  }
}
