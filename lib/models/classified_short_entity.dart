import 'package:property_dealers_directory/generated/json/classified_short_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class ClassifiedShortEntity {

	ClassifiedShortEntity();

	factory ClassifiedShortEntity.fromJson(Map<String, dynamic> json) => $ClassifiedShortEntityFromJson(json);

	Map<String, dynamic> toJson() => $ClassifiedShortEntityToJson(this);

	int id = 0;
	String price = '';
	String type = '';
	String category = '';
	String purpose = '';
	String size = '';
	@JSONField(name: "size_unit")
	String sizeUnit = '';
	int? bedrooms;
	int? bathrooms;
	String? photo;
	String city = '';
	String society = '';
	String? block;
	String? status;
	@JSONField(name: "is_featured")
	bool? isFeatured;
	int? impressions;
	int? views;
	int? clicks;
	@JSONField(name: "created_at")
	String? createdAt;

	@JSONField(name: "agency_featured")
	bool agencyFeatured = false;
	@JSONField(name: "agency_logo")
	String agencyLogo = '';

	@JSONField(name: "title_auto")
	String titleAuto = '';
	String title = '';
	String description = '';

	String get titleLocal {
		String _title = size + ' ' + sizeUnit + ' ' + type + ' ' + category + ' ';
		if(purpose.toLowerCase() == 'require'){
			_title += 'Required';
		} else {
			_title += 'For ' + purpose;
		}
		_title = _title.replaceFirst('Residential ', '');
		return _title;
	}
}
