import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/agency_entity.g.dart';

import 'package:property_dealers_directory/config.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import 'package:property_dealers_directory/models/staff_entity.dart';
import 'package:property_dealers_directory/services/remote_config.dart';

@JsonSerializable()
class AgencyEntity {

	AgencyEntity();

	factory AgencyEntity.fromJson(Map<String, dynamic> json) => $AgencyEntityFromJson(json);

	Map<String, dynamic> toJson() => $AgencyEntityToJson(this);

	int id = 0;
	@JSONField(name: "city_id")
	String cityId = '';
	@JSONField(name: "society_id")
	String societyId = '';
	String? logo;
	String? background;
	String name = '';
	String ceo = '';
	@JSONField(name: "mobile_1")
	String mobile1 = '';
	@JSONField(name: "mobile_2")
	String? mobile2;
	String? whatsapp;
	String? imo;
	@JSONField(name: "landline_1")
	String? landline1;
	@JSONField(name: "landline_2")
	String? landline2;
	String? fax;
	String? email;
	String? website;
	String address = '';
	String? facebook;
	String? twitter;
	String? instagram;
	String? youtube;
	String? linkedin;
	String? latitude;
	String? longitude;
	String? description;
	@JSONField(name: "google_location")
	String? googleLocation;
	String? skills;
	@JSONField(name: "is_featured")
	bool isFeatured = false;
	CityEntity? society;
	CityEntity? city;
	List<StaffEntity?>? staff;

	String get image {
		return background == null ? logoUrl : RemoteConfigService.storageUrl + background!;
	}
	String get logoUrl{
		return logo == null ? '' : RemoteConfigService.storageUrl + logo!;
	}
}
