import 'package:property_dealers_directory/config.dart';
import 'package:property_dealers_directory/generated/json/society_entity.g.dart';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';

import 'package:property_dealers_directory/services/remote_config.dart';

import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';

@JsonSerializable()
class SocietyEntity extends DropDownAble{

	SocietyEntity();

	factory SocietyEntity.fromJson(Map<String, dynamic> json) => $SocietyEntityFromJson(json);

	Map<String, dynamic> toJson() => $SocietyEntityToJson(this);

  int id = 0;
  @JSONField(name: "city_id")
  int cityId = 0;
  String name = '';
  String? logo;
  @JSONField(name: "city_name")
  String cityName = '';

  String? get title => name == 'All' ? name : name+ " - " + cityName;

  String get image {
    return logo == null || logo!.isEmpty ? '' : RemoteConfigService.storageUrl + logo!;
  }

  @override
  String toString() {
    return this.name + " - " + this.cityName;
  }

  @override
  String get dropDownText => toString();

  @override
  int? get itemId => id;
}
