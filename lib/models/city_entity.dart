import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/city_entity.g.dart';
import 'package:property_dealers_directory/models/block_entity.dart';
import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';
import 'package:property_dealers_directory/services/remote_config.dart';


@JsonSerializable()
class CityEntity extends DropDownAble{

	CityEntity();

	factory CityEntity.fromJson(Map<String, dynamic> json) => $CityEntityFromJson(json);

	Map<String, dynamic> toJson() => $CityEntityToJson(this);

  int id = -1;
  String name = '';
  String? logo;
  List<CityEntity> societies = [];
  BlockEntity? block;

  @JSONField(name: 'short_name')
  String shortName = '';

  String get image {
    return logo == null || logo!.isEmpty ? '' : RemoteConfigService.storageUrl + logo!;
  }

  @override
  String toString() {
    return this.name;
  }

  bool get isEmpty => id == -1;

  CityEntity? get get => id != -1 ? this : null;

  @override
  String get dropDownText => this.name;

  @override
  int? get itemId => id;
  static bool compareFn(CityEntity a, CityEntity b) {
    return a.id == b.id;
  }
  static bool filterFn(CityEntity a, String b) {
    return a.name.toLowerCase().contains(b.toLowerCase());
  }


}
