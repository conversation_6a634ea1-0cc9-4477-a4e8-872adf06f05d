import 'dart:convert';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/explore_data_entity.g.dart';
import 'package:property_dealers_directory/models/explore_short_entity.dart';

@JsonSerializable()
class ExploreDataEntity {

	late List<ExploreShortCategory> categories;
	late List<ExploreShortCategory> services;
	late List<ExploreShortCategory> features;
  
  ExploreDataEntity();

  factory ExploreDataEntity.fromJson(Map<String, dynamic> json) => $ExploreDataEntityFromJson(json);

  Map<String, dynamic> toJson() => $ExploreDataEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}