import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/market_inventory_short_entity.g.dart';
import 'dart:convert';

@JsonSerializable()
class MarketInventoryShortEntity {

	int id = 0;
	String price = '';
	@JSONField(name: "is_hot")
	bool isHot = false;
	@JSONField(name: "society_short")
	String societyShort = '';
	@JSONField(name: "block_short")
	String blockShort = '';
	@JSONField(name: "plot_name")
	String plotName = '';
  
  MarketInventoryShortEntity();

  factory MarketInventoryShortEntity.fromJson(Map<String, dynamic> json) => $MarketInventoryShortEntityFromJson(json);

  Map<String, dynamic> toJson() => $MarketInventoryShortEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}