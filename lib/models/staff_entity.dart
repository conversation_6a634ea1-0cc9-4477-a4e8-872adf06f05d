import 'package:property_dealers_directory/config.dart';
import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/staff_entity.g.dart';

import 'package:property_dealers_directory/services/remote_config.dart';

@JsonSerializable()
class StaffEntity {

	StaffEntity();

	factory StaffEntity.fromJson(Map<String, dynamic> json) => $StaffEntityFromJson(json);

	Map<String, dynamic> toJson() => $StaffEntityToJson(this);

  int id = 0;
  @J<PERSON>NField(name: "agency_id")
  String agencyId = '';
  String? image;
  String name = '';
  String username = '';
  String designation = '';
  String phone = '';
  @JSONField(name: "created_at")
  String? createdAt;
  @JSONField(name: "updated_at")
  String? updatedAt;
  @J<PERSON><PERSON>ield(name: "expire_at")
  String? expireAt;

  String get imageUrl {
    return image == null ? '' : RemoteConfigService.storageUrl + image!;
  }
}
