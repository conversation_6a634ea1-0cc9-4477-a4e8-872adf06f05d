import 'package:property_dealers_directory/generated/json/classified_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class ClassifiedEntity {

	ClassifiedEntity();

	factory ClassifiedEntity.fromJson(Map<String, dynamic> json) => $ClassifiedEntityFromJson(json);

	Map<String, dynamic> toJson() => $ClassifiedEntityToJson(this);

  int id = 0;
	String url = '';
  String title = '';
  String description = '';
  String type = '';
  String category = '';
  String purpose = '';
  String price = '';
  @JSONField(name: "price_numeric")
  int priceNumeric = 0;
  String size = '';
  @JSONField(name: "size_unit")
  String sizeUnit = '';
  int bedrooms = 0;
  int bathrooms = 0;
  List<String> features = [];
  @JSONField(name: "is_featured")
  bool? isFeatured;
  String? status;
  @JSONField(name: "created_at")
  String? createdAt;
  ClassifiedStaff staff = ClassifiedStaff();
  ClassifiedAgency agency = ClassifiedAgency();
  String city = '';
  String society = '';
  @JSONField(name: "society_id")
  int societyId = 0;
  @JSONField(name: "city_id")
  int cityId = 0;
  @JSONField(name: "block_id")
  int blockId = 0;
  @JSONField(name: "plot_id")
  int plotId = 0;
  @JSONField(name: "featured_image_id")
  int featuredImageId = 0;
  ClassifiedBlock block = ClassifiedBlock();
  ClassifiedPlot plot = ClassifiedPlot();

  List<ClassifiedMedia> media = [];

  @JSONField(name: "society_features")
  List<String> societyFeatures = [];
  @JSONField(name: "yr_url")
  String ytUrl = '';
}

@JsonSerializable()
class ClassifiedStaff {

	ClassifiedStaff();

	factory ClassifiedStaff.fromJson(Map<String, dynamic> json) => $ClassifiedStaffFromJson(json);

	Map<String, dynamic> toJson() => $ClassifiedStaffToJson(this);

  int id = 0;
  String name = '';
  String designation = '';
  String phone = '';
  String? image;
}

@JsonSerializable()
class ClassifiedAgency {

	ClassifiedAgency();

	factory ClassifiedAgency.fromJson(Map<String, dynamic> json) => $ClassifiedAgencyFromJson(json);

	Map<String, dynamic> toJson() => $ClassifiedAgencyToJson(this);

  int id = 0;
  String name = '';
  String? logo;
  String city = '';
  String society = '';
}

@JsonSerializable()
class ClassifiedBlock {

	ClassifiedBlock();

	factory ClassifiedBlock.fromJson(Map<String, dynamic> json) => $ClassifiedBlockFromJson(json);

	Map<String, dynamic> toJson() => $ClassifiedBlockToJson(this);

  @JSONField(name: "map_url")
  String mapUrl = '';
  String name = '';
  @JSONField(name: "max_zoom")
  int maxZoom = 0;
  @JSONField(name: "min_zoom")
  int minZoom = 0;

}

@JsonSerializable()
class ClassifiedPlot {

	ClassifiedPlot();

	factory ClassifiedPlot.fromJson(Map<String, dynamic> json) => $ClassifiedPlotFromJson(json);

	Map<String, dynamic> toJson() => $ClassifiedPlotToJson(this);

  int id = 0;
  @JSONField(name: "plot_no")
  String plotNo = '';
  @JSONField(name: "street_no")
  String streetNo = '';
  @JSONField(name: "street_type")
  String streetType = 'None';
  double lat = 0;
  double long = 0;
}

@JsonSerializable()
class ClassifiedMedia {

  ClassifiedMedia();

  factory ClassifiedMedia.fromJson(Map<String, dynamic> json) => $ClassifiedMediaFromJson(json);

  Map<String, dynamic> toJson() => $ClassifiedMediaToJson(this);

  int id = 0;
  String url = '';
  String thumb = '';
  String medium = '';
}