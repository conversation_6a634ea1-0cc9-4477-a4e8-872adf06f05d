import 'package:property_dealers_directory/generated/json/block_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';

@JsonSerializable()
class BlockEntity extends DropDownAble{

	BlockEntity();

	factory BlockEntity.fromJson(Map<String, dynamic> json) => $BlockEntityFromJson(json);

	Map<String, dynamic> toJson() => $BlockEntityToJson(this);

	int id = 0;
	@JSONField(name: "society_id")
	int? societyId;
	String name= '';
	String? logo;
	int priority = 0;
	String description = '';
	@JSONField(name: "extent_x1")
	double extentX1 = 0;
	@JSONField(name: "extent_x2")
	double extentX2 = 0;
	@JSONField(name: "extent_y1")
	double extentY1 = 0;
	@JSONField(name: "extent_y2")
	double extentY2 = 0;
	@JSONField(name: "map_url")
	String mapUrl = '';
	@JSONField(name: "max_zoom")
	int maxZoom = 0;
	@JSONField(name: "min_zoom")
	int minZoom = 0;
	@JSONField(name: "has_map")
	int hasMap = 0;
	@JSONField(name: "is_active")
	int isActive = 1;
	String type = '';
	@JSONField(name: "plots_count")
	int plotsCount = 0;
	String descriptions = '';
	@JSONField(name: "short_name")
	String shortName = '';
	@JSONField(name: "center_latitude")
	double centerLatitude = 0.0;
	@JSONField(name: "center_longitude")
	double centerLongitude = 0.0;

	bool get isEmpty => id == 0;

	BlockEntity? get get => id != 0 ? this : null;

  @override
  String get dropDownText => name;

	@override
  String get selectedLabel => shortName;

  @override
  int? get itemId => id;

	@override
  String toString() {
    return this.name;
  }


}
