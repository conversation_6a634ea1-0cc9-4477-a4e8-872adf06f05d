import 'package:property_dealers_directory/generated/json/base/json_field.dart';
import 'package:property_dealers_directory/generated/json/agency_short_entity.g.dart';


import 'package:property_dealers_directory/services/remote_config.dart';


@JsonSerializable()
class AgencyShortEntity {

	AgencyShortEntity();

	factory AgencyShortEntity.fromJson(Map<String, dynamic> json) => $AgencyShortEntityFromJson(json);

	Map<String, dynamic> toJson() => $AgencyShortEntityToJson(this);

	int id = 0;
	String name = '';
	String ceo = '';
	String? logo;
	@JSONField(name: 'society_name')
	String societyName = '';
	@JSONField(name: 'is_featured')
	bool isFeatured = false;

	String get image {
		return logo == null || logo!.isEmpty ? '' : RemoteConfigService.storageUrl + logo!;
	}
}
