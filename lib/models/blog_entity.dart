import 'package:property_dealers_directory/generated/json/blog_entity.g.dart';

import 'package:property_dealers_directory/generated/json/base/json_field.dart';

@JsonSerializable()
class BlogEntity {

	BlogEntity();

	factory BlogEntity.fromJson(Map<String, dynamic> json) => $BlogEntityFromJson(json);

	Map<String, dynamic> toJson() => $BlogEntityToJson(this);

	int id = 0;
	String title = '';
	String date = '';
	String banner = '';
	String body = '';
	@JSONField(name: "is_popular")
	bool isPopular = false;
	@JSONField(name: "short_body")
	String shortBody = '';
	String slug = '';
}
