import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/staff_entity.dart';
import 'package:property_dealers_directory/config.dart';

import 'package:property_dealers_directory/services/remote_config.dart';


StaffEntity $StaffEntityFromJson(Map<String, dynamic> json) {
  final StaffEntity staffEntity = StaffEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    staffEntity.id = id;
  }
  final String? agencyId = jsonConvert.convert<String>(json['agency_id']);
  if (agencyId != null) {
    staffEntity.agencyId = agencyId;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    staffEntity.image = image;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    staffEntity.name = name;
  }
  final String? username = jsonConvert.convert<String>(json['username']);
  if (username != null) {
    staffEntity.username = username;
  }
  final String? designation = jsonConvert.convert<String>(json['designation']);
  if (designation != null) {
    staffEntity.designation = designation;
  }
  final String? phone = jsonConvert.convert<String>(json['phone']);
  if (phone != null) {
    staffEntity.phone = phone;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    staffEntity.createdAt = createdAt;
  }
  final String? updatedAt = jsonConvert.convert<String>(json['updated_at']);
  if (updatedAt != null) {
    staffEntity.updatedAt = updatedAt;
  }
  final String? expireAt = jsonConvert.convert<String>(json['expire_at']);
  if (expireAt != null) {
    staffEntity.expireAt = expireAt;
  }
  return staffEntity;
}

Map<String, dynamic> $StaffEntityToJson(StaffEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['agency_id'] = entity.agencyId;
  data['image'] = entity.image;
  data['name'] = entity.name;
  data['username'] = entity.username;
  data['designation'] = entity.designation;
  data['phone'] = entity.phone;
  data['created_at'] = entity.createdAt;
  data['updated_at'] = entity.updatedAt;
  data['expire_at'] = entity.expireAt;
  return data;
}

extension StaffEntityExtension on StaffEntity {
  StaffEntity copyWith({
    int? id,
    String? agencyId,
    String? image,
    String? name,
    String? username,
    String? designation,
    String? phone,
    String? createdAt,
    String? updatedAt,
    String? expireAt,
  }) {
    return StaffEntity()
      ..id = id ?? this.id
      ..agencyId = agencyId ?? this.agencyId
      ..image = image ?? this.image
      ..name = name ?? this.name
      ..username = username ?? this.username
      ..designation = designation ?? this.designation
      ..phone = phone ?? this.phone
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? this.updatedAt
      ..expireAt = expireAt ?? this.expireAt;
  }
}