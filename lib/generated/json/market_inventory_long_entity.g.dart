import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/market_inventory_long_entity.dart';

MarketInventoryLongEntity $MarketInventoryLongEntityFromJson(
    Map<String, dynamic> json) {
  final MarketInventoryLongEntity marketInventoryLongEntity = MarketInventoryLongEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    marketInventoryLongEntity.id = id;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    marketInventoryLongEntity.price = price;
  }
  final String? extraInfo = jsonConvert.convert<String>(json['extra_info']);
  if (extraInfo != null) {
    marketInventoryLongEntity.extraInfo = extraInfo;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    marketInventoryLongEntity.status = status;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['is_hot']);
  if (isHot != null) {
    marketInventoryLongEntity.isHot = isHot;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    marketInventoryLongEntity.createdAt = createdAt;
  }
  final String? category = jsonConvert.convert<String>(json['category']);
  if (category != null) {
    marketInventoryLongEntity.category = category;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    marketInventoryLongEntity.type = type;
  }
  final int? staffId = jsonConvert.convert<int>(json['staff_id']);
  if (staffId != null) {
    marketInventoryLongEntity.staffId = staffId;
  }
  final int? cityId = jsonConvert.convert<int>(json['city_id']);
  if (cityId != null) {
    marketInventoryLongEntity.cityId = cityId;
  }
  final int? societyId = jsonConvert.convert<int>(json['society_id']);
  if (societyId != null) {
    marketInventoryLongEntity.societyId = societyId;
  }
  final int? blockId = jsonConvert.convert<int>(json['block_id']);
  if (blockId != null) {
    marketInventoryLongEntity.blockId = blockId;
  }
  final int? plotId = jsonConvert.convert<int>(json['plot_id']);
  if (plotId != null) {
    marketInventoryLongEntity.plotId = plotId;
  }
  final String? staffName = jsonConvert.convert<String>(json['staff_name']);
  if (staffName != null) {
    marketInventoryLongEntity.staffName = staffName;
  }
  final String? staffPhone = jsonConvert.convert<String>(json['staff_phone']);
  if (staffPhone != null) {
    marketInventoryLongEntity.staffPhone = staffPhone;
  }
  final String? agencyName = jsonConvert.convert<String>(json['agency_name']);
  if (agencyName != null) {
    marketInventoryLongEntity.agencyName = agencyName;
  }
  final String? staffImage = jsonConvert.convert<String>(json['staff_image']);
  if (staffImage != null) {
    marketInventoryLongEntity.staffImage = staffImage;
  }
  final String? cityName = jsonConvert.convert<String>(json['city_name']);
  if (cityName != null) {
    marketInventoryLongEntity.cityName = cityName;
  }
  final String? societyName = jsonConvert.convert<String>(json['society_name']);
  if (societyName != null) {
    marketInventoryLongEntity.societyName = societyName;
  }
  final String? societyShort = jsonConvert.convert<String>(
      json['society_short']);
  if (societyShort != null) {
    marketInventoryLongEntity.societyShort = societyShort;
  }
  final String? blockName = jsonConvert.convert<String>(json['block_name']);
  if (blockName != null) {
    marketInventoryLongEntity.blockName = blockName;
  }
  final String? blockShort = jsonConvert.convert<String>(json['block_short']);
  if (blockShort != null) {
    marketInventoryLongEntity.blockShort = blockShort;
  }
  final String? plotName = jsonConvert.convert<String>(json['plot_name']);
  if (plotName != null) {
    marketInventoryLongEntity.plotName = plotName;
  }
  final String? plotSize = jsonConvert.convert<String>(json['plot_size']);
  if (plotSize != null) {
    marketInventoryLongEntity.plotSize = plotSize;
  }
  final double? lat = jsonConvert.convert<double>(json['lat']);
  if (lat != null) {
    marketInventoryLongEntity.lat = lat;
  }
  final double? long = jsonConvert.convert<double>(json['long']);
  if (long != null) {
    marketInventoryLongEntity.long = long;
  }
  final String? mapUrl = jsonConvert.convert<String>(json['map_url']);
  if (mapUrl != null) {
    marketInventoryLongEntity.mapUrl = mapUrl;
  }
  final int? minZoom = jsonConvert.convert<int>(json['min_zoom']);
  if (minZoom != null) {
    marketInventoryLongEntity.minZoom = minZoom;
  }
  final int? maxZoom = jsonConvert.convert<int>(json['max_zoom']);
  if (maxZoom != null) {
    marketInventoryLongEntity.maxZoom = maxZoom;
  }
  return marketInventoryLongEntity;
}

Map<String, dynamic> $MarketInventoryLongEntityToJson(
    MarketInventoryLongEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['price'] = entity.price;
  data['extra_info'] = entity.extraInfo;
  data['status'] = entity.status;
  data['is_hot'] = entity.isHot;
  data['created_at'] = entity.createdAt;
  data['category'] = entity.category;
  data['type'] = entity.type;
  data['staff_id'] = entity.staffId;
  data['city_id'] = entity.cityId;
  data['society_id'] = entity.societyId;
  data['block_id'] = entity.blockId;
  data['plot_id'] = entity.plotId;
  data['staff_name'] = entity.staffName;
  data['staff_phone'] = entity.staffPhone;
  data['agency_name'] = entity.agencyName;
  data['staff_image'] = entity.staffImage;
  data['city_name'] = entity.cityName;
  data['society_name'] = entity.societyName;
  data['society_short'] = entity.societyShort;
  data['block_name'] = entity.blockName;
  data['block_short'] = entity.blockShort;
  data['plot_name'] = entity.plotName;
  data['plot_size'] = entity.plotSize;
  data['lat'] = entity.lat;
  data['long'] = entity.long;
  data['map_url'] = entity.mapUrl;
  data['min_zoom'] = entity.minZoom;
  data['max_zoom'] = entity.maxZoom;
  return data;
}

extension MarketInventoryLongEntityExtension on MarketInventoryLongEntity {
  MarketInventoryLongEntity copyWith({
    int? id,
    String? price,
    String? extraInfo,
    int? status,
    bool? isHot,
    String? createdAt,
    String? category,
    String? type,
    int? staffId,
    int? cityId,
    int? societyId,
    int? blockId,
    int? plotId,
    String? staffName,
    String? staffPhone,
    String? agencyName,
    String? staffImage,
    String? cityName,
    String? societyName,
    String? societyShort,
    String? blockName,
    String? blockShort,
    String? plotName,
    String? plotSize,
    double? lat,
    double? long,
    String? mapUrl,
    int? minZoom,
    int? maxZoom,
  }) {
    return MarketInventoryLongEntity()
      ..id = id ?? this.id
      ..price = price ?? this.price
      ..extraInfo = extraInfo ?? this.extraInfo
      ..status = status ?? this.status
      ..isHot = isHot ?? this.isHot
      ..createdAt = createdAt ?? this.createdAt
      ..category = category ?? this.category
      ..type = type ?? this.type
      ..staffId = staffId ?? this.staffId
      ..cityId = cityId ?? this.cityId
      ..societyId = societyId ?? this.societyId
      ..blockId = blockId ?? this.blockId
      ..plotId = plotId ?? this.plotId
      ..staffName = staffName ?? this.staffName
      ..staffPhone = staffPhone ?? this.staffPhone
      ..agencyName = agencyName ?? this.agencyName
      ..staffImage = staffImage ?? this.staffImage
      ..cityName = cityName ?? this.cityName
      ..societyName = societyName ?? this.societyName
      ..societyShort = societyShort ?? this.societyShort
      ..blockName = blockName ?? this.blockName
      ..blockShort = blockShort ?? this.blockShort
      ..plotName = plotName ?? this.plotName
      ..plotSize = plotSize ?? this.plotSize
      ..lat = lat ?? this.lat
      ..long = long ?? this.long
      ..mapUrl = mapUrl ?? this.mapUrl
      ..minZoom = minZoom ?? this.minZoom
      ..maxZoom = maxZoom ?? this.maxZoom;
  }
}