import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/onboarding_entity.dart';
import 'package:property_dealers_directory/models/city_entity.dart';


OnboardingEntity $OnboardingEntityFromJson(Map<String, dynamic> json) {
  final OnboardingEntity onboardingEntity = OnboardingEntity();
  final bool? notificationHot = jsonConvert.convert<bool>(
      json['notificationHot']);
  if (notificationHot != null) {
    onboardingEntity.notificationHot = notificationHot;
  }
  final String? notificationSocieties = jsonConvert.convert<String>(
      json['notificationSocieties']);
  if (notificationSocieties != null) {
    onboardingEntity.notificationSocieties = notificationSocieties;
  }
  final CityEntity? city = jsonConvert.convert<CityEntity>(json['city']);
  if (city != null) {
    onboardingEntity.city = city;
  }
  final CityEntity? society = jsonConvert.convert<CityEntity>(json['society']);
  if (society != null) {
    onboardingEntity.society = society;
  }
  final String? userType = jsonConvert.convert<String>(json['userType']);
  if (userType != null) {
    onboardingEntity.userType = userType;
  }
  final bool? tutorialDone = jsonConvert.convert<bool>(json['tutorialDone']);
  if (tutorialDone != null) {
    onboardingEntity.tutorialDone = tutorialDone;
  }
  final bool? tutorialSkip = jsonConvert.convert<bool>(json['tutorialSkip']);
  if (tutorialSkip != null) {
    onboardingEntity.tutorialSkip = tutorialSkip;
  }
  return onboardingEntity;
}

Map<String, dynamic> $OnboardingEntityToJson(OnboardingEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['notificationHot'] = entity.notificationHot;
  data['notificationSocieties'] = entity.notificationSocieties;
  data['city'] = entity.city.toJson();
  data['society'] = entity.society.toJson();
  data['userType'] = entity.userType;
  data['tutorialDone'] = entity.tutorialDone;
  data['tutorialSkip'] = entity.tutorialSkip;
  return data;
}

extension OnboardingEntityExtension on OnboardingEntity {
  OnboardingEntity copyWith({
    bool? notificationHot,
    String? notificationSocieties,
    CityEntity? city,
    CityEntity? society,
    String? userType,
    bool? tutorialDone,
    bool? tutorialSkip,
  }) {
    return OnboardingEntity()
      ..notificationHot = notificationHot ?? this.notificationHot
      ..notificationSocieties = notificationSocieties ??
          this.notificationSocieties
      ..city = city ?? this.city
      ..society = society ?? this.society
      ..userType = userType ?? this.userType
      ..tutorialDone = tutorialDone ?? this.tutorialDone
      ..tutorialSkip = tutorialSkip ?? this.tutorialSkip;
  }
}