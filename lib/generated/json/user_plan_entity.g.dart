import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/user_plan_entity.dart';

UserPlanEntity $UserPlanEntityFromJson(Map<String, dynamic> json) {
  final UserPlanEntity userPlanEntity = UserPlanEntity();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    userPlanEntity.name = name;
  }
  final String? expiredAt = jsonConvert.convert<String>(json['expired_at']);
  if (expiredAt != null) {
    userPlanEntity.expiredAt = expiredAt;
  }
  final bool? expired = jsonConvert.convert<bool>(json['expired']);
  if (expired != null) {
    userPlanEntity.expired = expired;
  }
  final List<UserPlanFeatures>? features = (json['features'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<UserPlanFeatures>(e) as UserPlanFeatures)
      .toList();
  if (features != null) {
    userPlanEntity.features = features;
  }
  return userPlanEntity;
}

Map<String, dynamic> $UserPlanEntityToJson(UserPlanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['expired_at'] = entity.expiredAt;
  data['expired'] = entity.expired;
  data['features'] = entity.features?.map((v) => v.toJson()).toList();
  return data;
}

extension UserPlanEntityExtension on UserPlanEntity {
  UserPlanEntity copyWith({
    String? name,
    String? expiredAt,
    bool? expired,
    List<UserPlanFeatures>? features,
  }) {
    return UserPlanEntity()
      ..name = name ?? this.name
      ..expiredAt = expiredAt ?? this.expiredAt
      ..expired = expired ?? this.expired
      ..features = features ?? this.features;
  }
}

UserPlanFeatures $UserPlanFeaturesFromJson(Map<String, dynamic> json) {
  final UserPlanFeatures userPlanFeatures = UserPlanFeatures();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    userPlanFeatures.name = name;
  }
  final String? limit = jsonConvert.convert<String>(json['limit']);
  if (limit != null) {
    userPlanFeatures.limit = limit;
  }
  final int? consumed = jsonConvert.convert<int>(json['consumed']);
  if (consumed != null) {
    userPlanFeatures.consumed = consumed;
  }
  final int? planId = jsonConvert.convert<int>(json['plan_id']);
  if (planId != null) {
    userPlanFeatures.planId = planId;
  }
  return userPlanFeatures;
}

Map<String, dynamic> $UserPlanFeaturesToJson(UserPlanFeatures entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['limit'] = entity.limit;
  data['consumed'] = entity.consumed;
  data['plan_id'] = entity.planId;
  return data;
}

extension UserPlanFeaturesExtension on UserPlanFeatures {
  UserPlanFeatures copyWith({
    String? name,
    String? limit,
    int? consumed,
    int? planId,
  }) {
    return UserPlanFeatures()
      ..name = name ?? this.name
      ..limit = limit ?? this.limit
      ..consumed = consumed ?? this.consumed
      ..planId = planId ?? this.planId;
  }
}