import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/project_entity.dart';
import 'package:property_dealers_directory/models/classified_entity.dart';


ProjectEntity $ProjectEntityFromJson(Map<String, dynamic> json) {
  final ProjectEntity projectEntity = ProjectEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    projectEntity.id = id;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    projectEntity.logo = logo;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    projectEntity.title = title;
  }
  final List<ClassifiedMedia>? media = (json['media'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ClassifiedMedia>(e) as ClassifiedMedia)
      .toList();
  if (media != null) {
    projectEntity.media = media;
  }
  final String? developerName = jsonConvert.convert<String>(
      json['developer_name']);
  if (developerName != null) {
    projectEntity.developerName = developerName;
  }
  final String? developerLogo = jsonConvert.convert<String>(
      json['developer_logo']);
  if (developerLogo != null) {
    projectEntity.developerLogo = developerLogo;
  }
  final String? marketerName = jsonConvert.convert<String>(
      json['marketer_name']);
  if (marketerName != null) {
    projectEntity.marketerName = marketerName;
  }
  final String? marketerLogo = jsonConvert.convert<String>(
      json['marketer_logo']);
  if (marketerLogo != null) {
    projectEntity.marketerLogo = marketerLogo;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    projectEntity.type = type;
  }
  final bool? isActive = jsonConvert.convert<bool>(json['is_active']);
  if (isActive != null) {
    projectEntity.isActive = isActive;
  }
  final bool? isFeatured = jsonConvert.convert<bool>(json['is_featured']);
  if (isFeatured != null) {
    projectEntity.isFeatured = isFeatured;
  }
  final String? location = jsonConvert.convert<String>(json['location']);
  if (location != null) {
    projectEntity.location = location;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    projectEntity.address = address;
  }
  final String? lat = jsonConvert.convert<String>(json['lat']);
  if (lat != null) {
    projectEntity.lat = lat;
  }
  final String? lng = jsonConvert.convert<String>(json['lng']);
  if (lng != null) {
    projectEntity.lng = lng;
  }
  final String? about = jsonConvert.convert<String>(json['about']);
  if (about != null) {
    projectEntity.about = about;
  }
  final String? contactNumber = jsonConvert.convert<String>(
      json['contact_number']);
  if (contactNumber != null) {
    projectEntity.contactNumber = contactNumber;
  }
  final List<ProjectItems?>? items = (json['items'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ProjectItems>(e)).toList();
  if (items != null) {
    projectEntity.items = items;
  }
  return projectEntity;
}

Map<String, dynamic> $ProjectEntityToJson(ProjectEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['logo'] = entity.logo;
  data['title'] = entity.title;
  data['media'] = entity.media.map((v) => v.toJson()).toList();
  data['developer_name'] = entity.developerName;
  data['developer_logo'] = entity.developerLogo;
  data['marketer_name'] = entity.marketerName;
  data['marketer_logo'] = entity.marketerLogo;
  data['type'] = entity.type;
  data['is_active'] = entity.isActive;
  data['is_featured'] = entity.isFeatured;
  data['location'] = entity.location;
  data['address'] = entity.address;
  data['lat'] = entity.lat;
  data['lng'] = entity.lng;
  data['about'] = entity.about;
  data['contact_number'] = entity.contactNumber;
  data['items'] = entity.items?.map((v) => v?.toJson()).toList();
  return data;
}

extension ProjectEntityExtension on ProjectEntity {
  ProjectEntity copyWith({
    int? id,
    String? logo,
    String? title,
    List<ClassifiedMedia>? media,
    String? developerName,
    String? developerLogo,
    String? marketerName,
    String? marketerLogo,
    String? type,
    bool? isActive,
    bool? isFeatured,
    String? location,
    String? address,
    String? lat,
    String? lng,
    String? about,
    String? contactNumber,
    List<ProjectItems?>? items,
  }) {
    return ProjectEntity()
      ..id = id ?? this.id
      ..logo = logo ?? this.logo
      ..title = title ?? this.title
      ..media = media ?? this.media
      ..developerName = developerName ?? this.developerName
      ..developerLogo = developerLogo ?? this.developerLogo
      ..marketerName = marketerName ?? this.marketerName
      ..marketerLogo = marketerLogo ?? this.marketerLogo
      ..type = type ?? this.type
      ..isActive = isActive ?? this.isActive
      ..isFeatured = isFeatured ?? this.isFeatured
      ..location = location ?? this.location
      ..address = address ?? this.address
      ..lat = lat ?? this.lat
      ..lng = lng ?? this.lng
      ..about = about ?? this.about
      ..contactNumber = contactNumber ?? this.contactNumber
      ..items = items ?? this.items;
  }
}

ProjectItems $ProjectItemsFromJson(Map<String, dynamic> json) {
  final ProjectItems projectItems = ProjectItems();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    projectItems.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    projectItems.title = title;
  }
  final List<String>? photos = (json['photos'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (photos != null) {
    projectItems.photos = photos;
  }
  final double? size = jsonConvert.convert<double>(json['size']);
  if (size != null) {
    projectItems.size = size;
  }
  final String? sizeUnit = jsonConvert.convert<String>(json['size_unit']);
  if (sizeUnit != null) {
    projectItems.sizeUnit = sizeUnit;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    projectItems.type = type;
  }
  final int? totalPayment = jsonConvert.convert<int>(json['total_payment']);
  if (totalPayment != null) {
    projectItems.totalPayment = totalPayment;
  }
  final List<ProjectItemsPlans?>? plans = (json['plans'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ProjectItemsPlans>(e))
      .toList();
  if (plans != null) {
    projectItems.plans = plans;
  }
  return projectItems;
}

Map<String, dynamic> $ProjectItemsToJson(ProjectItems entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['title'] = entity.title;
  data['photos'] = entity.photos;
  data['size'] = entity.size;
  data['size_unit'] = entity.sizeUnit;
  data['type'] = entity.type;
  data['total_payment'] = entity.totalPayment;
  data['plans'] = entity.plans?.map((v) => v?.toJson()).toList();
  return data;
}

extension ProjectItemsExtension on ProjectItems {
  ProjectItems copyWith({
    int? id,
    String? title,
    List<String>? photos,
    double? size,
    String? sizeUnit,
    String? type,
    int? totalPayment,
    List<ProjectItemsPlans?>? plans,
  }) {
    return ProjectItems()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..photos = photos ?? this.photos
      ..size = size ?? this.size
      ..sizeUnit = sizeUnit ?? this.sizeUnit
      ..type = type ?? this.type
      ..totalPayment = totalPayment ?? this.totalPayment
      ..plans = plans ?? this.plans;
  }
}

ProjectItemsPlans $ProjectItemsPlansFromJson(Map<String, dynamic> json) {
  final ProjectItemsPlans projectItemsPlans = ProjectItemsPlans();
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    projectItemsPlans.title = title;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    projectItemsPlans.amount = amount;
  }
  return projectItemsPlans;
}

Map<String, dynamic> $ProjectItemsPlansToJson(ProjectItemsPlans entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['title'] = entity.title;
  data['amount'] = entity.amount;
  return data;
}

extension ProjectItemsPlansExtension on ProjectItemsPlans {
  ProjectItemsPlans copyWith({
    String? title,
    String? amount,
  }) {
    return ProjectItemsPlans()
      ..title = title ?? this.title
      ..amount = amount ?? this.amount;
  }
}