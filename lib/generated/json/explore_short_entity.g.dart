import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/explore_short_entity.dart';

ExploreShortEntity $ExploreShortEntityFromJson(Map<String, dynamic> json) {
  final ExploreShortEntity exploreShortEntity = ExploreShortEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    exploreShortEntity.id = id;
  }
  final ExploreShortCategory? category = jsonConvert.convert<
      ExploreShortCategory>(json['category']);
  if (category != null) {
    exploreShortEntity.category = category;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    exploreShortEntity.city = city;
  }
  final String? society = jsonConvert.convert<String>(json['society']);
  if (society != null) {
    exploreShortEntity.society = society;
  }
  final String? block = jsonConvert.convert<String>(json['block']);
  if (block != null) {
    exploreShortEntity.block = block;
  }
  final String? businessName = jsonConvert.convert<String>(
      json['business_name']);
  if (businessName != null) {
    exploreShortEntity.businessName = businessName;
  }
  final String? businessLogo = jsonConvert.convert<String>(
      json['business_logo']);
  if (businessLogo != null) {
    exploreShortEntity.businessLogo = businessLogo;
  }
  final String? telephone1 = jsonConvert.convert<String>(json['telephone_1']);
  if (telephone1 != null) {
    exploreShortEntity.telephone1 = telephone1;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    exploreShortEntity.email = email;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    exploreShortEntity.address = address;
  }
  final List<ExploreShortHours>? hours = (json['hours'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ExploreShortHours>(e) as ExploreShortHours)
      .toList();
  if (hours != null) {
    exploreShortEntity.hours = hours;
  }
  return exploreShortEntity;
}

Map<String, dynamic> $ExploreShortEntityToJson(ExploreShortEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['category'] = entity.category.toJson();
  data['city'] = entity.city;
  data['society'] = entity.society;
  data['block'] = entity.block;
  data['business_name'] = entity.businessName;
  data['business_logo'] = entity.businessLogo;
  data['telephone_1'] = entity.telephone1;
  data['email'] = entity.email;
  data['address'] = entity.address;
  data['hours'] = entity.hours?.map((v) => v.toJson()).toList();
  return data;
}

extension ExploreShortEntityExtension on ExploreShortEntity {
  ExploreShortEntity copyWith({
    int? id,
    ExploreShortCategory? category,
    String? city,
    String? society,
    String? block,
    String? businessName,
    String? businessLogo,
    String? telephone1,
    String? email,
    String? address,
    List<ExploreShortHours>? hours,
  }) {
    return ExploreShortEntity()
      ..id = id ?? this.id
      ..category = category ?? this.category
      ..city = city ?? this.city
      ..society = society ?? this.society
      ..block = block ?? this.block
      ..businessName = businessName ?? this.businessName
      ..businessLogo = businessLogo ?? this.businessLogo
      ..telephone1 = telephone1 ?? this.telephone1
      ..email = email ?? this.email
      ..address = address ?? this.address
      ..hours = hours ?? this.hours;
  }
}

ExploreShortCategory $ExploreShortCategoryFromJson(Map<String, dynamic> json) {
  final ExploreShortCategory exploreShortCategory = ExploreShortCategory();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    exploreShortCategory.id = id;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    exploreShortCategory.image = image;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    exploreShortCategory.name = name;
  }
  return exploreShortCategory;
}

Map<String, dynamic> $ExploreShortCategoryToJson(ExploreShortCategory entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['image'] = entity.image;
  data['name'] = entity.name;
  return data;
}

extension ExploreShortCategoryExtension on ExploreShortCategory {
  ExploreShortCategory copyWith({
    int? id,
    String? image,
    String? name,
  }) {
    return ExploreShortCategory()
      ..id = id ?? this.id
      ..image = image ?? this.image
      ..name = name ?? this.name;
  }
}

ExploreShortHours $ExploreShortHoursFromJson(Map<String, dynamic> json) {
  final ExploreShortHours exploreShortHours = ExploreShortHours();
  final String? day = jsonConvert.convert<String>(json['day']);
  if (day != null) {
    exploreShortHours.day = day;
  }
  final String? opening = jsonConvert.convert<String>(json['opening']);
  if (opening != null) {
    exploreShortHours.opening = opening;
  }
  final String? closing = jsonConvert.convert<String>(json['closing']);
  if (closing != null) {
    exploreShortHours.closing = closing;
  }
  return exploreShortHours;
}

Map<String, dynamic> $ExploreShortHoursToJson(ExploreShortHours entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['day'] = entity.day;
  data['opening'] = entity.opening;
  data['closing'] = entity.closing;
  return data;
}

extension ExploreShortHoursExtension on ExploreShortHours {
  ExploreShortHours copyWith({
    String? day,
    String? opening,
    String? closing,
  }) {
    return ExploreShortHours()
      ..day = day ?? this.day
      ..opening = opening ?? this.opening
      ..closing = closing ?? this.closing;
  }
}