import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/inventory_design_entity.dart';

InventoryDesignEntity $InventoryDesignEntityFromJson(
    Map<String, dynamic> json) {
  final InventoryDesignEntity inventoryDesignEntity = InventoryDesignEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    inventoryDesignEntity.id = id;
  }
  final String? link = jsonConvert.convert<String>(json['link']);
  if (link != null) {
    inventoryDesignEntity.link = link;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    inventoryDesignEntity.image = image;
  }
  final bool? forTitanium = jsonConvert.convert<bool>(json['for_titanium']);
  if (forTitanium != null) {
    inventoryDesignEntity.forTitanium = forTitanium;
  }
  return inventoryDesignEntity;
}

Map<String, dynamic> $InventoryDesignEntityToJson(
    InventoryDesignEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['link'] = entity.link;
  data['image'] = entity.image;
  data['for_titanium'] = entity.forTitanium;
  return data;
}

extension InventoryDesignEntityExtension on InventoryDesignEntity {
  InventoryDesignEntity copyWith({
    int? id,
    String? link,
    String? image,
    bool? forTitanium,
  }) {
    return InventoryDesignEntity()
      ..id = id ?? this.id
      ..link = link ?? this.link
      ..image = image ?? this.image
      ..forTitanium = forTitanium ?? this.forTitanium;
  }
}