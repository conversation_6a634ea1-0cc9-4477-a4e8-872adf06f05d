import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/plot_entity.dart';
import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';

import 'package:property_dealers_directory/models/classified_entity.dart';


PlotEntity $PlotEntityFromJson(Map<String, dynamic> json) {
  final PlotEntity plotEntity = PlotEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    plotEntity.id = id;
  }
  final String? plotNo = jsonConvert.convert<String>(json['plot_no']);
  if (plotNo != null) {
    plotEntity.plotNo = plotNo;
  }
  final double? lat = jsonConvert.convert<double>(json['lat']);
  if (lat != null) {
    plotEntity.lat = lat;
  }
  final double? long = jsonConvert.convert<double>(json['long']);
  if (long != null) {
    plotEntity.long = long;
  }
  final String? streetNo = jsonConvert.convert<String>(json['street_no']);
  if (streetNo != null) {
    plotEntity.streetNo = streetNo;
  }
  final String? streetType = jsonConvert.convert<String>(json['street_type']);
  if (streetType != null) {
    plotEntity.streetType = streetType;
  }
  final String? category = jsonConvert.convert<String>(json['category']);
  if (category != null) {
    plotEntity.category = category;
  }
  final int? plotSize = jsonConvert.convert<int>(json['plot_size']);
  if (plotSize != null) {
    plotEntity.plotSize = plotSize;
  }
  final String? sizeType = jsonConvert.convert<String>(json['size_type']);
  if (sizeType != null) {
    plotEntity.sizeType = sizeType;
  }
  final double? blockId = jsonConvert.convert<double>(json['block_id']);
  if (blockId != null) {
    plotEntity.blockId = blockId;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    plotEntity.createdAt = createdAt;
  }
  final String? updatedAt = jsonConvert.convert<String>(json['updated_at']);
  if (updatedAt != null) {
    plotEntity.updatedAt = updatedAt;
  }
  final String? societyName = jsonConvert.convert<String>(json['society_name']);
  if (societyName != null) {
    plotEntity.societyName = societyName;
  }
  final String? roadWidth = jsonConvert.convert<String>(json['road_width']);
  if (roadWidth != null) {
    plotEntity.roadWidth = roadWidth;
  }
  final String? note = jsonConvert.convert<String>(json['note']);
  if (note != null) {
    plotEntity.note = note;
  }
  final List? features = (json['features'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (features != null) {
    plotEntity.features = features;
  }
  final bool? isVilla = jsonConvert.convert<bool>(json['is_villa']);
  if (isVilla != null) {
    plotEntity.isVilla = isVilla;
  }
  final List<ClassifiedMarker>? classifieds = (json['classifieds'] as List<
      dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ClassifiedMarker>(e) as ClassifiedMarker)
      .toList();
  if (classifieds != null) {
    plotEntity.classifieds = classifieds;
  }
  return plotEntity;
}

Map<String, dynamic> $PlotEntityToJson(PlotEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['plot_no'] = entity.plotNo;
  data['lat'] = entity.lat;
  data['long'] = entity.long;
  data['street_no'] = entity.streetNo;
  data['street_type'] = entity.streetType;
  data['category'] = entity.category;
  data['plot_size'] = entity.plotSize;
  data['size_type'] = entity.sizeType;
  data['block_id'] = entity.blockId;
  data['created_at'] = entity.createdAt;
  data['updated_at'] = entity.updatedAt;
  data['society_name'] = entity.societyName;
  data['road_width'] = entity.roadWidth;
  data['note'] = entity.note;
  data['features'] = entity.features;
  data['is_villa'] = entity.isVilla;
  data['classifieds'] = entity.classifieds?.map((v) => v.toJson()).toList();
  return data;
}

extension PlotEntityExtension on PlotEntity {
  PlotEntity copyWith({
    int? id,
    String? plotNo,
    double? lat,
    double? long,
    String? streetNo,
    String? streetType,
    String? category,
    int? plotSize,
    String? sizeType,
    double? blockId,
    String? createdAt,
    String? updatedAt,
    String? societyName,
    String? roadWidth,
    String? note,
    List? features,
    bool? isVilla,
    List<ClassifiedMarker>? classifieds,
  }) {
    return PlotEntity()
      ..id = id ?? this.id
      ..plotNo = plotNo ?? this.plotNo
      ..lat = lat ?? this.lat
      ..long = long ?? this.long
      ..streetNo = streetNo ?? this.streetNo
      ..streetType = streetType ?? this.streetType
      ..category = category ?? this.category
      ..plotSize = plotSize ?? this.plotSize
      ..sizeType = sizeType ?? this.sizeType
      ..blockId = blockId ?? this.blockId
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? this.updatedAt
      ..societyName = societyName ?? this.societyName
      ..roadWidth = roadWidth ?? this.roadWidth
      ..note = note ?? this.note
      ..features = features ?? this.features
      ..isVilla = isVilla ?? this.isVilla
      ..classifieds = classifieds ?? this.classifieds;
  }
}

ClassifiedMarker $ClassifiedMarkerFromJson(Map<String, dynamic> json) {
  final ClassifiedMarker classifiedMarker = ClassifiedMarker();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    classifiedMarker.id = id;
  }
  final int? plotId = jsonConvert.convert<int>(json['plot_id']);
  if (plotId != null) {
    classifiedMarker.plotId = plotId;
  }
  final double? lat = jsonConvert.convert<double>(json['lat']);
  if (lat != null) {
    classifiedMarker.lat = lat;
  }
  final double? long = jsonConvert.convert<double>(json['long']);
  if (long != null) {
    classifiedMarker.long = long;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['is_hot']);
  if (isHot != null) {
    classifiedMarker.isHot = isHot;
  }
  final String? category = jsonConvert.convert<String>(json['category']);
  if (category != null) {
    classifiedMarker.category = category;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    classifiedMarker.title = title;
  }
  return classifiedMarker;
}

Map<String, dynamic> $ClassifiedMarkerToJson(ClassifiedMarker entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['plot_id'] = entity.plotId;
  data['lat'] = entity.lat;
  data['long'] = entity.long;
  data['is_hot'] = entity.isHot;
  data['category'] = entity.category;
  data['title'] = entity.title;
  return data;
}

extension ClassifiedMarkerExtension on ClassifiedMarker {
  ClassifiedMarker copyWith({
    int? id,
    int? plotId,
    double? lat,
    double? long,
    bool? isHot,
    String? category,
    String? title,
  }) {
    return ClassifiedMarker()
      ..id = id ?? this.id
      ..plotId = plotId ?? this.plotId
      ..lat = lat ?? this.lat
      ..long = long ?? this.long
      ..isHot = isHot ?? this.isHot
      ..category = category ?? this.category
      ..title = title ?? this.title;
  }
}