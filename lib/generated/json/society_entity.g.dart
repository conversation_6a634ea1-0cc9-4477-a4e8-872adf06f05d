import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/society_entity.dart';
import 'package:property_dealers_directory/config.dart';

import 'package:property_dealers_directory/services/remote_config.dart';

import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';


SocietyEntity $SocietyEntityFromJson(Map<String, dynamic> json) {
  final SocietyEntity societyEntity = SocietyEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    societyEntity.id = id;
  }
  final int? cityId = jsonConvert.convert<int>(json['city_id']);
  if (cityId != null) {
    societyEntity.cityId = cityId;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    societyEntity.name = name;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    societyEntity.logo = logo;
  }
  final String? cityName = jsonConvert.convert<String>(json['city_name']);
  if (cityName != null) {
    societyEntity.cityName = cityName;
  }
  return societyEntity;
}

Map<String, dynamic> $SocietyEntityToJson(SocietyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['city_id'] = entity.cityId;
  data['name'] = entity.name;
  data['logo'] = entity.logo;
  data['city_name'] = entity.cityName;
  return data;
}

extension SocietyEntityExtension on SocietyEntity {
  SocietyEntity copyWith({
    int? id,
    int? cityId,
    String? name,
    String? logo,
    String? cityName,
  }) {
    return SocietyEntity()
      ..id = id ?? this.id
      ..cityId = cityId ?? this.cityId
      ..name = name ?? this.name
      ..logo = logo ?? this.logo
      ..cityName = cityName ?? this.cityName;
  }
}