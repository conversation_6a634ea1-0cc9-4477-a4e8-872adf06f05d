import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/classified_short_entity.dart';

ClassifiedShortEntity $ClassifiedShortEntityFromJson(
    Map<String, dynamic> json) {
  final ClassifiedShortEntity classifiedShortEntity = ClassifiedShortEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    classifiedShortEntity.id = id;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    classifiedShortEntity.price = price;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    classifiedShortEntity.type = type;
  }
  final String? category = jsonConvert.convert<String>(json['category']);
  if (category != null) {
    classifiedShortEntity.category = category;
  }
  final String? purpose = jsonConvert.convert<String>(json['purpose']);
  if (purpose != null) {
    classifiedShortEntity.purpose = purpose;
  }
  final String? size = jsonConvert.convert<String>(json['size']);
  if (size != null) {
    classifiedShortEntity.size = size;
  }
  final String? sizeUnit = jsonConvert.convert<String>(json['size_unit']);
  if (sizeUnit != null) {
    classifiedShortEntity.sizeUnit = sizeUnit;
  }
  final int? bedrooms = jsonConvert.convert<int>(json['bedrooms']);
  if (bedrooms != null) {
    classifiedShortEntity.bedrooms = bedrooms;
  }
  final int? bathrooms = jsonConvert.convert<int>(json['bathrooms']);
  if (bathrooms != null) {
    classifiedShortEntity.bathrooms = bathrooms;
  }
  final String? photo = jsonConvert.convert<String>(json['photo']);
  if (photo != null) {
    classifiedShortEntity.photo = photo;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    classifiedShortEntity.city = city;
  }
  final String? society = jsonConvert.convert<String>(json['society']);
  if (society != null) {
    classifiedShortEntity.society = society;
  }
  final String? block = jsonConvert.convert<String>(json['block']);
  if (block != null) {
    classifiedShortEntity.block = block;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    classifiedShortEntity.status = status;
  }
  final bool? isFeatured = jsonConvert.convert<bool>(json['is_featured']);
  if (isFeatured != null) {
    classifiedShortEntity.isFeatured = isFeatured;
  }
  final int? impressions = jsonConvert.convert<int>(json['impressions']);
  if (impressions != null) {
    classifiedShortEntity.impressions = impressions;
  }
  final int? views = jsonConvert.convert<int>(json['views']);
  if (views != null) {
    classifiedShortEntity.views = views;
  }
  final int? clicks = jsonConvert.convert<int>(json['clicks']);
  if (clicks != null) {
    classifiedShortEntity.clicks = clicks;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    classifiedShortEntity.createdAt = createdAt;
  }
  final bool? agencyFeatured = jsonConvert.convert<bool>(
      json['agency_featured']);
  if (agencyFeatured != null) {
    classifiedShortEntity.agencyFeatured = agencyFeatured;
  }
  final String? agencyLogo = jsonConvert.convert<String>(json['agency_logo']);
  if (agencyLogo != null) {
    classifiedShortEntity.agencyLogo = agencyLogo;
  }
  final String? titleAuto = jsonConvert.convert<String>(json['title_auto']);
  if (titleAuto != null) {
    classifiedShortEntity.titleAuto = titleAuto;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    classifiedShortEntity.title = title;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    classifiedShortEntity.description = description;
  }
  return classifiedShortEntity;
}

Map<String, dynamic> $ClassifiedShortEntityToJson(
    ClassifiedShortEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['price'] = entity.price;
  data['type'] = entity.type;
  data['category'] = entity.category;
  data['purpose'] = entity.purpose;
  data['size'] = entity.size;
  data['size_unit'] = entity.sizeUnit;
  data['bedrooms'] = entity.bedrooms;
  data['bathrooms'] = entity.bathrooms;
  data['photo'] = entity.photo;
  data['city'] = entity.city;
  data['society'] = entity.society;
  data['block'] = entity.block;
  data['status'] = entity.status;
  data['is_featured'] = entity.isFeatured;
  data['impressions'] = entity.impressions;
  data['views'] = entity.views;
  data['clicks'] = entity.clicks;
  data['created_at'] = entity.createdAt;
  data['agency_featured'] = entity.agencyFeatured;
  data['agency_logo'] = entity.agencyLogo;
  data['title_auto'] = entity.titleAuto;
  data['title'] = entity.title;
  data['description'] = entity.description;
  return data;
}

extension ClassifiedShortEntityExtension on ClassifiedShortEntity {
  ClassifiedShortEntity copyWith({
    int? id,
    String? price,
    String? type,
    String? category,
    String? purpose,
    String? size,
    String? sizeUnit,
    int? bedrooms,
    int? bathrooms,
    String? photo,
    String? city,
    String? society,
    String? block,
    String? status,
    bool? isFeatured,
    int? impressions,
    int? views,
    int? clicks,
    String? createdAt,
    bool? agencyFeatured,
    String? agencyLogo,
    String? titleAuto,
    String? title,
    String? description,
  }) {
    return ClassifiedShortEntity()
      ..id = id ?? this.id
      ..price = price ?? this.price
      ..type = type ?? this.type
      ..category = category ?? this.category
      ..purpose = purpose ?? this.purpose
      ..size = size ?? this.size
      ..sizeUnit = sizeUnit ?? this.sizeUnit
      ..bedrooms = bedrooms ?? this.bedrooms
      ..bathrooms = bathrooms ?? this.bathrooms
      ..photo = photo ?? this.photo
      ..city = city ?? this.city
      ..society = society ?? this.society
      ..block = block ?? this.block
      ..status = status ?? this.status
      ..isFeatured = isFeatured ?? this.isFeatured
      ..impressions = impressions ?? this.impressions
      ..views = views ?? this.views
      ..clicks = clicks ?? this.clicks
      ..createdAt = createdAt ?? this.createdAt
      ..agencyFeatured = agencyFeatured ?? this.agencyFeatured
      ..agencyLogo = agencyLogo ?? this.agencyLogo
      ..titleAuto = titleAuto ?? this.titleAuto
      ..title = title ?? this.title
      ..description = description ?? this.description;
  }
}