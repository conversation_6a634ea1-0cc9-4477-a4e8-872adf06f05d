import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/property_entity.dart';
import 'package:property_dealers_directory/config.dart';

import 'package:property_dealers_directory/models/block_entity.dart';

import 'package:property_dealers_directory/models/city_entity.dart';

import 'package:property_dealers_directory/models/society_entity.dart';

import 'package:property_dealers_directory/services/remote_config.dart';


PropertyEntity $PropertyEntityFromJson(Map<String, dynamic> json) {
  final PropertyEntity propertyEntity = PropertyEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    propertyEntity.id = id;
  }
  final String? agencyId = jsonConvert.convert<String>(json['agency_id']);
  if (agencyId != null) {
    propertyEntity.agencyId = agencyId;
  }
  final String? cityId = jsonConvert.convert<String>(json['city_id']);
  if (cityId != null) {
    propertyEntity.cityId = cityId;
  }
  final String? societyId = jsonConvert.convert<String>(json['society_id']);
  if (societyId != null) {
    propertyEntity.societyId = societyId;
  }
  final int? designId = jsonConvert.convert<int>(json['design_id']);
  if (designId != null) {
    propertyEntity.designId = designId;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    propertyEntity.type = type;
  }
  final String? note = jsonConvert.convert<String>(json['note']);
  if (note != null) {
    propertyEntity.note = note;
  }
  final String? purpose = jsonConvert.convert<String>(json['purpose']);
  if (purpose != null) {
    propertyEntity.purpose = purpose;
  }
  final String? category = jsonConvert.convert<String>(json['category']);
  if (category != null) {
    propertyEntity.category = category;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    propertyEntity.status = status;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    propertyEntity.title = title;
  }
  final int? price = jsonConvert.convert<int>(json['price']);
  if (price != null) {
    propertyEntity.price = price;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    propertyEntity.size = size;
  }
  final String? sizeUnit = jsonConvert.convert<String>(json['size_unit']);
  if (sizeUnit != null) {
    propertyEntity.sizeUnit = sizeUnit;
  }
  final String? typeV2 = jsonConvert.convert<String>(json['type_v2']);
  if (typeV2 != null) {
    propertyEntity.typeV2 = typeV2;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    propertyEntity.createdAt = createdAt;
  }
  final String? createdAgo = jsonConvert.convert<String>(json['created_ago']);
  if (createdAgo != null) {
    propertyEntity.createdAgo = createdAgo;
  }
  final String? updatedAt = jsonConvert.convert<String>(json['updated_at']);
  if (updatedAt != null) {
    propertyEntity.updatedAt = updatedAt;
  }
  final String? societyName = jsonConvert.convert<String>(json['society_name']);
  if (societyName != null) {
    propertyEntity.societyName = societyName;
  }
  final String? cityName = jsonConvert.convert<String>(json['city_name']);
  if (cityName != null) {
    propertyEntity.cityName = cityName;
  }
  final String? agencyName = jsonConvert.convert<String>(json['agency_name']);
  if (agencyName != null) {
    propertyEntity.agencyName = agencyName;
  }
  final String? staffName = jsonConvert.convert<String>(json['staff_name']);
  if (staffName != null) {
    propertyEntity.staffName = staffName;
  }
  final String? staffPhone = jsonConvert.convert<String>(json['staff_phone']);
  if (staffPhone != null) {
    propertyEntity.staffPhone = staffPhone;
  }
  final String? staffImage = jsonConvert.convert<String>(json['staff_image']);
  if (staffImage != null) {
    propertyEntity.staffImage = staffImage;
  }
  final int? isFeatured = jsonConvert.convert<int>(json['is_featured']);
  if (isFeatured != null) {
    propertyEntity.isFeatured = isFeatured;
  }
  final int? isHot = jsonConvert.convert<int>(json['is_hot']);
  if (isHot != null) {
    propertyEntity.isHot = isHot;
  }
  final int? impressions = jsonConvert.convert<int>(json['impressions']);
  if (impressions != null) {
    propertyEntity.impressions = impressions;
  }
  final int? clicks = jsonConvert.convert<int>(json['clicks']);
  if (clicks != null) {
    propertyEntity.clicks = clicks;
  }
  final int? phoneClicks = jsonConvert.convert<int>(json['phone_clicks']);
  if (phoneClicks != null) {
    propertyEntity.phoneClicks = phoneClicks;
  }
  final int? whatsappClicks = jsonConvert.convert<int>(json['whatsapp_clicks']);
  if (whatsappClicks != null) {
    propertyEntity.whatsappClicks = whatsappClicks;
  }
  final int? staffId = jsonConvert.convert<int>(json['staff_id']);
  if (staffId != null) {
    propertyEntity.staffId = staffId;
  }
  final List<String>? keywords = (json['keywords'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (keywords != null) {
    propertyEntity.keywords = keywords;
  }
  final CityEntity? city = jsonConvert.convert<CityEntity>(json['city']);
  if (city != null) {
    propertyEntity.city = city;
  }
  final SocietyEntity? society = jsonConvert.convert<SocietyEntity>(
      json['society']);
  if (society != null) {
    propertyEntity.society = society;
  }
  final BlockEntity? block = jsonConvert.convert<BlockEntity>(json['block']);
  if (block != null) {
    propertyEntity.block = block;
  }
  final bool? hotList = jsonConvert.convert<bool>(json['hotList']);
  if (hotList != null) {
    propertyEntity.hotList = hotList;
  }
  return propertyEntity;
}

Map<String, dynamic> $PropertyEntityToJson(PropertyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['agency_id'] = entity.agencyId;
  data['city_id'] = entity.cityId;
  data['society_id'] = entity.societyId;
  data['design_id'] = entity.designId;
  data['type'] = entity.type;
  data['note'] = entity.note;
  data['purpose'] = entity.purpose;
  data['category'] = entity.category;
  data['status'] = entity.status;
  data['title'] = entity.title;
  data['price'] = entity.price;
  data['size'] = entity.size;
  data['size_unit'] = entity.sizeUnit;
  data['type_v2'] = entity.typeV2;
  data['created_at'] = entity.createdAt;
  data['created_ago'] = entity.createdAgo;
  data['updated_at'] = entity.updatedAt;
  data['society_name'] = entity.societyName;
  data['city_name'] = entity.cityName;
  data['agency_name'] = entity.agencyName;
  data['staff_name'] = entity.staffName;
  data['staff_phone'] = entity.staffPhone;
  data['staff_image'] = entity.staffImage;
  data['is_featured'] = entity.isFeatured;
  data['is_hot'] = entity.isHot;
  data['impressions'] = entity.impressions;
  data['clicks'] = entity.clicks;
  data['phone_clicks'] = entity.phoneClicks;
  data['whatsapp_clicks'] = entity.whatsappClicks;
  data['staff_id'] = entity.staffId;
  data['keywords'] = entity.keywords;
  data['city'] = entity.city?.toJson();
  data['society'] = entity.society?.toJson();
  data['block'] = entity.block?.toJson();
  data['hotList'] = entity.hotList;
  return data;
}

extension PropertyEntityExtension on PropertyEntity {
  PropertyEntity copyWith({
    int? id,
    String? agencyId,
    String? cityId,
    String? societyId,
    int? designId,
    String? type,
    String? note,
    String? purpose,
    String? category,
    String? status,
    String? title,
    int? price,
    int? size,
    String? sizeUnit,
    String? typeV2,
    String? createdAt,
    String? createdAgo,
    String? updatedAt,
    String? societyName,
    String? cityName,
    String? agencyName,
    String? staffName,
    String? staffPhone,
    String? staffImage,
    int? isFeatured,
    int? isHot,
    int? impressions,
    int? clicks,
    int? phoneClicks,
    int? whatsappClicks,
    int? staffId,
    List<String>? keywords,
    CityEntity? city,
    SocietyEntity? society,
    BlockEntity? block,
    bool? hotList,
  }) {
    return PropertyEntity()
      ..id = id ?? this.id
      ..agencyId = agencyId ?? this.agencyId
      ..cityId = cityId ?? this.cityId
      ..societyId = societyId ?? this.societyId
      ..designId = designId ?? this.designId
      ..type = type ?? this.type
      ..note = note ?? this.note
      ..purpose = purpose ?? this.purpose
      ..category = category ?? this.category
      ..status = status ?? this.status
      ..title = title ?? this.title
      ..price = price ?? this.price
      ..size = size ?? this.size
      ..sizeUnit = sizeUnit ?? this.sizeUnit
      ..typeV2 = typeV2 ?? this.typeV2
      ..createdAt = createdAt ?? this.createdAt
      ..createdAgo = createdAgo ?? this.createdAgo
      ..updatedAt = updatedAt ?? this.updatedAt
      ..societyName = societyName ?? this.societyName
      ..cityName = cityName ?? this.cityName
      ..agencyName = agencyName ?? this.agencyName
      ..staffName = staffName ?? this.staffName
      ..staffPhone = staffPhone ?? this.staffPhone
      ..staffImage = staffImage ?? this.staffImage
      ..isFeatured = isFeatured ?? this.isFeatured
      ..isHot = isHot ?? this.isHot
      ..impressions = impressions ?? this.impressions
      ..clicks = clicks ?? this.clicks
      ..phoneClicks = phoneClicks ?? this.phoneClicks
      ..whatsappClicks = whatsappClicks ?? this.whatsappClicks
      ..staffId = staffId ?? this.staffId
      ..keywords = keywords ?? this.keywords
      ..city = city ?? this.city
      ..society = society ?? this.society
      ..block = block ?? this.block
      ..hotList = hotList ?? this.hotList;
  }
}