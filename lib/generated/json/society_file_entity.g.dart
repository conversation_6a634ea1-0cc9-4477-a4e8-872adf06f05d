import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/society_file_entity.dart';

SocietyFileEntity $SocietyFileEntityFromJson(Map<String, dynamic> json) {
  final SocietyFileEntity societyFileEntity = SocietyFileEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    societyFileEntity.id = id;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    societyFileEntity.url = url;
  }
  final String? fileName = jsonConvert.convert<String>(json['file_name']);
  if (fileName != null) {
    societyFileEntity.fileName = fileName;
  }
  return societyFileEntity;
}

Map<String, dynamic> $SocietyFileEntityToJson(SocietyFileEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['url'] = entity.url;
  data['file_name'] = entity.fileName;
  return data;
}

extension SocietyFileEntityExtension on SocietyFileEntity {
  SocietyFileEntity copyWith({
    int? id,
    String? url,
    String? fileName,
  }) {
    return SocietyFileEntity()
      ..id = id ?? this.id
      ..url = url ?? this.url
      ..fileName = fileName ?? this.fileName;
  }
}