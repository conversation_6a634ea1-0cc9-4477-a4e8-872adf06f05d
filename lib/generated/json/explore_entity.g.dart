import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/explore_entity.dart';
import 'package:property_dealers_directory/models/explore_short_entity.dart';


ExploreEntity $ExploreEntityFromJson(Map<String, dynamic> json) {
  final ExploreEntity exploreEntity = ExploreEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    exploreEntity.id = id;
  }
  final ExploreShortCategory? category = jsonConvert.convert<
      ExploreShortCategory>(json['category']);
  if (category != null) {
    exploreEntity.category = category;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    exploreEntity.city = city;
  }
  final String? society = jsonConvert.convert<String>(json['society']);
  if (society != null) {
    exploreEntity.society = society;
  }
  final String? block = jsonConvert.convert<String>(json['block']);
  if (block != null) {
    exploreEntity.block = block;
  }
  final String? businessName = jsonConvert.convert<String>(
      json['business_name']);
  if (businessName != null) {
    exploreEntity.businessName = businessName;
  }
  final String? businessLogo = jsonConvert.convert<String>(
      json['business_logo']);
  if (businessLogo != null) {
    exploreEntity.businessLogo = businessLogo;
  }
  final String? businessDescription = jsonConvert.convert<String>(
      json['business_description']);
  if (businessDescription != null) {
    exploreEntity.businessDescription = businessDescription;
  }
  final String? telephone1 = jsonConvert.convert<String>(json['telephone_1']);
  if (telephone1 != null) {
    exploreEntity.telephone1 = telephone1;
  }
  final String? telephone2 = jsonConvert.convert<String>(json['telephone_2']);
  if (telephone2 != null) {
    exploreEntity.telephone2 = telephone2;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    exploreEntity.email = email;
  }
  final String? facebook = jsonConvert.convert<String>(json['facebook']);
  if (facebook != null) {
    exploreEntity.facebook = facebook;
  }
  final String? instagram = jsonConvert.convert<String>(json['instagram']);
  if (instagram != null) {
    exploreEntity.instagram = instagram;
  }
  final String? website = jsonConvert.convert<String>(json['website']);
  if (website != null) {
    exploreEntity.website = website;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    exploreEntity.address = address;
  }
  final String? googleAddress = jsonConvert.convert<String>(
      json['google_address']);
  if (googleAddress != null) {
    exploreEntity.googleAddress = googleAddress;
  }
  final double? latitude = jsonConvert.convert<double>(json['latitude']);
  if (latitude != null) {
    exploreEntity.latitude = latitude;
  }
  final double? longitude = jsonConvert.convert<double>(json['longitude']);
  if (longitude != null) {
    exploreEntity.longitude = longitude;
  }
  final List<ExploreShortHours>? hours = (json['hours'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ExploreShortHours>(e) as ExploreShortHours)
      .toList();
  if (hours != null) {
    exploreEntity.hours = hours;
  }
  final List<ExploreShortCategory>? services = (json['services'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ExploreShortCategory>(e) as ExploreShortCategory)
      .toList();
  if (services != null) {
    exploreEntity.services = services;
  }
  final List<ExploreShortCategory>? features = (json['features'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ExploreShortCategory>(e) as ExploreShortCategory)
      .toList();
  if (features != null) {
    exploreEntity.features = features;
  }
  final List<ExploreCollections>? collections = (json['collections'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ExploreCollections>(e) as ExploreCollections)
      .toList();
  if (collections != null) {
    exploreEntity.collections = collections;
  }
  return exploreEntity;
}

Map<String, dynamic> $ExploreEntityToJson(ExploreEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['category'] = entity.category.toJson();
  data['city'] = entity.city;
  data['society'] = entity.society;
  data['block'] = entity.block;
  data['business_name'] = entity.businessName;
  data['business_logo'] = entity.businessLogo;
  data['business_description'] = entity.businessDescription;
  data['telephone_1'] = entity.telephone1;
  data['telephone_2'] = entity.telephone2;
  data['email'] = entity.email;
  data['facebook'] = entity.facebook;
  data['instagram'] = entity.instagram;
  data['website'] = entity.website;
  data['address'] = entity.address;
  data['google_address'] = entity.googleAddress;
  data['latitude'] = entity.latitude;
  data['longitude'] = entity.longitude;
  data['hours'] = entity.hours?.map((v) => v.toJson()).toList();
  data['services'] = entity.services?.map((v) => v.toJson()).toList();
  data['features'] = entity.features?.map((v) => v.toJson()).toList();
  data['collections'] = entity.collections?.map((v) => v.toJson()).toList();
  return data;
}

extension ExploreEntityExtension on ExploreEntity {
  ExploreEntity copyWith({
    int? id,
    ExploreShortCategory? category,
    String? city,
    String? society,
    String? block,
    String? businessName,
    String? businessLogo,
    String? businessDescription,
    String? telephone1,
    String? telephone2,
    String? email,
    String? facebook,
    String? instagram,
    String? website,
    String? address,
    String? googleAddress,
    double? latitude,
    double? longitude,
    List<ExploreShortHours>? hours,
    List<ExploreShortCategory>? services,
    List<ExploreShortCategory>? features,
    List<ExploreCollections>? collections,
  }) {
    return ExploreEntity()
      ..id = id ?? this.id
      ..category = category ?? this.category
      ..city = city ?? this.city
      ..society = society ?? this.society
      ..block = block ?? this.block
      ..businessName = businessName ?? this.businessName
      ..businessLogo = businessLogo ?? this.businessLogo
      ..businessDescription = businessDescription ?? this.businessDescription
      ..telephone1 = telephone1 ?? this.telephone1
      ..telephone2 = telephone2 ?? this.telephone2
      ..email = email ?? this.email
      ..facebook = facebook ?? this.facebook
      ..instagram = instagram ?? this.instagram
      ..website = website ?? this.website
      ..address = address ?? this.address
      ..googleAddress = googleAddress ?? this.googleAddress
      ..latitude = latitude ?? this.latitude
      ..longitude = longitude ?? this.longitude
      ..hours = hours ?? this.hours
      ..services = services ?? this.services
      ..features = features ?? this.features
      ..collections = collections ?? this.collections;
  }
}

ExploreCollections $ExploreCollectionsFromJson(Map<String, dynamic> json) {
  final ExploreCollections exploreCollections = ExploreCollections();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    exploreCollections.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    exploreCollections.name = name;
  }
  final List<String>? images = (json['images'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (images != null) {
    exploreCollections.images = images;
  }
  return exploreCollections;
}

Map<String, dynamic> $ExploreCollectionsToJson(ExploreCollections entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['images'] = entity.images;
  return data;
}

extension ExploreCollectionsExtension on ExploreCollections {
  ExploreCollections copyWith({
    int? id,
    String? name,
    List<String>? images,
  }) {
    return ExploreCollections()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..images = images ?? this.images;
  }
}