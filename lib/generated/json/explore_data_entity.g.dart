import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/explore_data_entity.dart';
import 'package:property_dealers_directory/models/explore_short_entity.dart';


ExploreDataEntity $ExploreDataEntityFromJson(Map<String, dynamic> json) {
  final ExploreDataEntity exploreDataEntity = ExploreDataEntity();
  final List<ExploreShortCategory>? categories = (json['categories'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ExploreShortCategory>(e) as ExploreShortCategory)
      .toList();
  if (categories != null) {
    exploreDataEntity.categories = categories;
  }
  final List<ExploreShortCategory>? services = (json['services'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ExploreShortCategory>(e) as ExploreShortCategory)
      .toList();
  if (services != null) {
    exploreDataEntity.services = services;
  }
  final List<ExploreShortCategory>? features = (json['features'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ExploreShortCategory>(e) as ExploreShortCategory)
      .toList();
  if (features != null) {
    exploreDataEntity.features = features;
  }
  return exploreDataEntity;
}

Map<String, dynamic> $ExploreDataEntityToJson(ExploreDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['categories'] = entity.categories.map((v) => v.toJson()).toList();
  data['services'] = entity.services.map((v) => v.toJson()).toList();
  data['features'] = entity.features.map((v) => v.toJson()).toList();
  return data;
}

extension ExploreDataEntityExtension on ExploreDataEntity {
  ExploreDataEntity copyWith({
    List<ExploreShortCategory>? categories,
    List<ExploreShortCategory>? services,
    List<ExploreShortCategory>? features,
  }) {
    return ExploreDataEntity()
      ..categories = categories ?? this.categories
      ..services = services ?? this.services
      ..features = features ?? this.features;
  }
}