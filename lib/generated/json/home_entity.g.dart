import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/home_entity.dart';
import 'package:property_dealers_directory/models/agency_short_entity.dart';

import 'package:property_dealers_directory/models/blog_entity.dart';

import 'package:property_dealers_directory/models/classified_short_entity.dart';

import 'package:property_dealers_directory/models/project_Short_entity.dart';


HomeEntity $HomeEntityFromJson(Map<String, dynamic> json) {
  final HomeEntity homeEntity = HomeEntity();
  final List<AgencyShortEntity>? agencies = (json['agencies'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<AgencyShortEntity>(e) as AgencyShortEntity)
      .toList();
  if (agencies != null) {
    homeEntity.agencies = agencies;
  }
  final List<ProjectShortEntity>? projects = (json['projects'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ProjectShortEntity>(e) as ProjectShortEntity)
      .toList();
  if (projects != null) {
    homeEntity.projects = projects;
  }
  final List<ClassifiedShortEntity>? classifieds = (json['classifieds'] as List<
      dynamic>?)
      ?.map(
          (e) =>
      jsonConvert.convert<ClassifiedShortEntity>(e) as ClassifiedShortEntity)
      .toList();
  if (classifieds != null) {
    homeEntity.classifieds = classifieds;
  }
  final List<BlogEntity>? blogs = (json['blogs'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BlogEntity>(e) as BlogEntity).toList();
  if (blogs != null) {
    homeEntity.blogs = blogs;
  }
  return homeEntity;
}

Map<String, dynamic> $HomeEntityToJson(HomeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['agencies'] = entity.agencies?.map((v) => v.toJson()).toList();
  data['projects'] = entity.projects?.map((v) => v.toJson()).toList();
  data['classifieds'] = entity.classifieds?.map((v) => v.toJson()).toList();
  data['blogs'] = entity.blogs?.map((v) => v.toJson()).toList();
  return data;
}

extension HomeEntityExtension on HomeEntity {
  HomeEntity copyWith({
    List<AgencyShortEntity>? agencies,
    List<ProjectShortEntity>? projects,
    List<ClassifiedShortEntity>? classifieds,
    List<BlogEntity>? blogs,
  }) {
    return HomeEntity()
      ..agencies = agencies ?? this.agencies
      ..projects = projects ?? this.projects
      ..classifieds = classifieds ?? this.classifieds
      ..blogs = blogs ?? this.blogs;
  }
}