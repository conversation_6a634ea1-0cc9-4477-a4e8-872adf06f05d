import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/blog_entity.dart';

BlogEntity $BlogEntityFromJson(Map<String, dynamic> json) {
  final BlogEntity blogEntity = BlogEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    blogEntity.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    blogEntity.title = title;
  }
  final String? date = jsonConvert.convert<String>(json['date']);
  if (date != null) {
    blogEntity.date = date;
  }
  final String? banner = jsonConvert.convert<String>(json['banner']);
  if (banner != null) {
    blogEntity.banner = banner;
  }
  final String? body = jsonConvert.convert<String>(json['body']);
  if (body != null) {
    blogEntity.body = body;
  }
  final bool? isPopular = jsonConvert.convert<bool>(json['is_popular']);
  if (isPopular != null) {
    blogEntity.isPopular = isPopular;
  }
  final String? shortBody = jsonConvert.convert<String>(json['short_body']);
  if (shortBody != null) {
    blogEntity.shortBody = shortBody;
  }
  final String? slug = jsonConvert.convert<String>(json['slug']);
  if (slug != null) {
    blogEntity.slug = slug;
  }
  return blogEntity;
}

Map<String, dynamic> $BlogEntityToJson(BlogEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['title'] = entity.title;
  data['date'] = entity.date;
  data['banner'] = entity.banner;
  data['body'] = entity.body;
  data['is_popular'] = entity.isPopular;
  data['short_body'] = entity.shortBody;
  data['slug'] = entity.slug;
  return data;
}

extension BlogEntityExtension on BlogEntity {
  BlogEntity copyWith({
    int? id,
    String? title,
    String? date,
    String? banner,
    String? body,
    bool? isPopular,
    String? shortBody,
    String? slug,
  }) {
    return BlogEntity()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..date = date ?? this.date
      ..banner = banner ?? this.banner
      ..body = body ?? this.body
      ..isPopular = isPopular ?? this.isPopular
      ..shortBody = shortBody ?? this.shortBody
      ..slug = slug ?? this.slug;
  }
}