import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/classified_entity.dart';

ClassifiedEntity $ClassifiedEntityFromJson(Map<String, dynamic> json) {
  final ClassifiedEntity classifiedEntity = ClassifiedEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    classifiedEntity.id = id;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    classifiedEntity.url = url;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    classifiedEntity.title = title;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    classifiedEntity.description = description;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    classifiedEntity.type = type;
  }
  final String? category = jsonConvert.convert<String>(json['category']);
  if (category != null) {
    classifiedEntity.category = category;
  }
  final String? purpose = jsonConvert.convert<String>(json['purpose']);
  if (purpose != null) {
    classifiedEntity.purpose = purpose;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    classifiedEntity.price = price;
  }
  final int? priceNumeric = jsonConvert.convert<int>(json['price_numeric']);
  if (priceNumeric != null) {
    classifiedEntity.priceNumeric = priceNumeric;
  }
  final String? size = jsonConvert.convert<String>(json['size']);
  if (size != null) {
    classifiedEntity.size = size;
  }
  final String? sizeUnit = jsonConvert.convert<String>(json['size_unit']);
  if (sizeUnit != null) {
    classifiedEntity.sizeUnit = sizeUnit;
  }
  final int? bedrooms = jsonConvert.convert<int>(json['bedrooms']);
  if (bedrooms != null) {
    classifiedEntity.bedrooms = bedrooms;
  }
  final int? bathrooms = jsonConvert.convert<int>(json['bathrooms']);
  if (bathrooms != null) {
    classifiedEntity.bathrooms = bathrooms;
  }
  final List<String>? features = (json['features'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (features != null) {
    classifiedEntity.features = features;
  }
  final bool? isFeatured = jsonConvert.convert<bool>(json['is_featured']);
  if (isFeatured != null) {
    classifiedEntity.isFeatured = isFeatured;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    classifiedEntity.status = status;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    classifiedEntity.createdAt = createdAt;
  }
  final ClassifiedStaff? staff = jsonConvert.convert<ClassifiedStaff>(
      json['staff']);
  if (staff != null) {
    classifiedEntity.staff = staff;
  }
  final ClassifiedAgency? agency = jsonConvert.convert<ClassifiedAgency>(
      json['agency']);
  if (agency != null) {
    classifiedEntity.agency = agency;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    classifiedEntity.city = city;
  }
  final String? society = jsonConvert.convert<String>(json['society']);
  if (society != null) {
    classifiedEntity.society = society;
  }
  final int? societyId = jsonConvert.convert<int>(json['society_id']);
  if (societyId != null) {
    classifiedEntity.societyId = societyId;
  }
  final int? cityId = jsonConvert.convert<int>(json['city_id']);
  if (cityId != null) {
    classifiedEntity.cityId = cityId;
  }
  final int? blockId = jsonConvert.convert<int>(json['block_id']);
  if (blockId != null) {
    classifiedEntity.blockId = blockId;
  }
  final int? plotId = jsonConvert.convert<int>(json['plot_id']);
  if (plotId != null) {
    classifiedEntity.plotId = plotId;
  }
  final int? featuredImageId = jsonConvert.convert<int>(
      json['featured_image_id']);
  if (featuredImageId != null) {
    classifiedEntity.featuredImageId = featuredImageId;
  }
  final ClassifiedBlock? block = jsonConvert.convert<ClassifiedBlock>(
      json['block']);
  if (block != null) {
    classifiedEntity.block = block;
  }
  final ClassifiedPlot? plot = jsonConvert.convert<ClassifiedPlot>(
      json['plot']);
  if (plot != null) {
    classifiedEntity.plot = plot;
  }
  final List<ClassifiedMedia>? media = (json['media'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<ClassifiedMedia>(e) as ClassifiedMedia)
      .toList();
  if (media != null) {
    classifiedEntity.media = media;
  }
  final List<String>? societyFeatures = (json['society_features'] as List<
      dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (societyFeatures != null) {
    classifiedEntity.societyFeatures = societyFeatures;
  }
  final String? ytUrl = jsonConvert.convert<String>(json['yr_url']);
  if (ytUrl != null) {
    classifiedEntity.ytUrl = ytUrl;
  }
  return classifiedEntity;
}

Map<String, dynamic> $ClassifiedEntityToJson(ClassifiedEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['url'] = entity.url;
  data['title'] = entity.title;
  data['description'] = entity.description;
  data['type'] = entity.type;
  data['category'] = entity.category;
  data['purpose'] = entity.purpose;
  data['price'] = entity.price;
  data['price_numeric'] = entity.priceNumeric;
  data['size'] = entity.size;
  data['size_unit'] = entity.sizeUnit;
  data['bedrooms'] = entity.bedrooms;
  data['bathrooms'] = entity.bathrooms;
  data['features'] = entity.features;
  data['is_featured'] = entity.isFeatured;
  data['status'] = entity.status;
  data['created_at'] = entity.createdAt;
  data['staff'] = entity.staff.toJson();
  data['agency'] = entity.agency.toJson();
  data['city'] = entity.city;
  data['society'] = entity.society;
  data['society_id'] = entity.societyId;
  data['city_id'] = entity.cityId;
  data['block_id'] = entity.blockId;
  data['plot_id'] = entity.plotId;
  data['featured_image_id'] = entity.featuredImageId;
  data['block'] = entity.block.toJson();
  data['plot'] = entity.plot.toJson();
  data['media'] = entity.media.map((v) => v.toJson()).toList();
  data['society_features'] = entity.societyFeatures;
  data['yr_url'] = entity.ytUrl;
  return data;
}

extension ClassifiedEntityExtension on ClassifiedEntity {
  ClassifiedEntity copyWith({
    int? id,
    String? url,
    String? title,
    String? description,
    String? type,
    String? category,
    String? purpose,
    String? price,
    int? priceNumeric,
    String? size,
    String? sizeUnit,
    int? bedrooms,
    int? bathrooms,
    List<String>? features,
    bool? isFeatured,
    String? status,
    String? createdAt,
    ClassifiedStaff? staff,
    ClassifiedAgency? agency,
    String? city,
    String? society,
    int? societyId,
    int? cityId,
    int? blockId,
    int? plotId,
    int? featuredImageId,
    ClassifiedBlock? block,
    ClassifiedPlot? plot,
    List<ClassifiedMedia>? media,
    List<String>? societyFeatures,
    String? ytUrl,
  }) {
    return ClassifiedEntity()
      ..id = id ?? this.id
      ..url = url ?? this.url
      ..title = title ?? this.title
      ..description = description ?? this.description
      ..type = type ?? this.type
      ..category = category ?? this.category
      ..purpose = purpose ?? this.purpose
      ..price = price ?? this.price
      ..priceNumeric = priceNumeric ?? this.priceNumeric
      ..size = size ?? this.size
      ..sizeUnit = sizeUnit ?? this.sizeUnit
      ..bedrooms = bedrooms ?? this.bedrooms
      ..bathrooms = bathrooms ?? this.bathrooms
      ..features = features ?? this.features
      ..isFeatured = isFeatured ?? this.isFeatured
      ..status = status ?? this.status
      ..createdAt = createdAt ?? this.createdAt
      ..staff = staff ?? this.staff
      ..agency = agency ?? this.agency
      ..city = city ?? this.city
      ..society = society ?? this.society
      ..societyId = societyId ?? this.societyId
      ..cityId = cityId ?? this.cityId
      ..blockId = blockId ?? this.blockId
      ..plotId = plotId ?? this.plotId
      ..featuredImageId = featuredImageId ?? this.featuredImageId
      ..block = block ?? this.block
      ..plot = plot ?? this.plot
      ..media = media ?? this.media
      ..societyFeatures = societyFeatures ?? this.societyFeatures
      ..ytUrl = ytUrl ?? this.ytUrl;
  }
}

ClassifiedStaff $ClassifiedStaffFromJson(Map<String, dynamic> json) {
  final ClassifiedStaff classifiedStaff = ClassifiedStaff();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    classifiedStaff.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    classifiedStaff.name = name;
  }
  final String? designation = jsonConvert.convert<String>(json['designation']);
  if (designation != null) {
    classifiedStaff.designation = designation;
  }
  final String? phone = jsonConvert.convert<String>(json['phone']);
  if (phone != null) {
    classifiedStaff.phone = phone;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    classifiedStaff.image = image;
  }
  return classifiedStaff;
}

Map<String, dynamic> $ClassifiedStaffToJson(ClassifiedStaff entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['designation'] = entity.designation;
  data['phone'] = entity.phone;
  data['image'] = entity.image;
  return data;
}

extension ClassifiedStaffExtension on ClassifiedStaff {
  ClassifiedStaff copyWith({
    int? id,
    String? name,
    String? designation,
    String? phone,
    String? image,
  }) {
    return ClassifiedStaff()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..designation = designation ?? this.designation
      ..phone = phone ?? this.phone
      ..image = image ?? this.image;
  }
}

ClassifiedAgency $ClassifiedAgencyFromJson(Map<String, dynamic> json) {
  final ClassifiedAgency classifiedAgency = ClassifiedAgency();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    classifiedAgency.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    classifiedAgency.name = name;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    classifiedAgency.logo = logo;
  }
  final String? city = jsonConvert.convert<String>(json['city']);
  if (city != null) {
    classifiedAgency.city = city;
  }
  final String? society = jsonConvert.convert<String>(json['society']);
  if (society != null) {
    classifiedAgency.society = society;
  }
  return classifiedAgency;
}

Map<String, dynamic> $ClassifiedAgencyToJson(ClassifiedAgency entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['logo'] = entity.logo;
  data['city'] = entity.city;
  data['society'] = entity.society;
  return data;
}

extension ClassifiedAgencyExtension on ClassifiedAgency {
  ClassifiedAgency copyWith({
    int? id,
    String? name,
    String? logo,
    String? city,
    String? society,
  }) {
    return ClassifiedAgency()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..logo = logo ?? this.logo
      ..city = city ?? this.city
      ..society = society ?? this.society;
  }
}

ClassifiedBlock $ClassifiedBlockFromJson(Map<String, dynamic> json) {
  final ClassifiedBlock classifiedBlock = ClassifiedBlock();
  final String? mapUrl = jsonConvert.convert<String>(json['map_url']);
  if (mapUrl != null) {
    classifiedBlock.mapUrl = mapUrl;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    classifiedBlock.name = name;
  }
  final int? maxZoom = jsonConvert.convert<int>(json['max_zoom']);
  if (maxZoom != null) {
    classifiedBlock.maxZoom = maxZoom;
  }
  final int? minZoom = jsonConvert.convert<int>(json['min_zoom']);
  if (minZoom != null) {
    classifiedBlock.minZoom = minZoom;
  }
  return classifiedBlock;
}

Map<String, dynamic> $ClassifiedBlockToJson(ClassifiedBlock entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['map_url'] = entity.mapUrl;
  data['name'] = entity.name;
  data['max_zoom'] = entity.maxZoom;
  data['min_zoom'] = entity.minZoom;
  return data;
}

extension ClassifiedBlockExtension on ClassifiedBlock {
  ClassifiedBlock copyWith({
    String? mapUrl,
    String? name,
    int? maxZoom,
    int? minZoom,
  }) {
    return ClassifiedBlock()
      ..mapUrl = mapUrl ?? this.mapUrl
      ..name = name ?? this.name
      ..maxZoom = maxZoom ?? this.maxZoom
      ..minZoom = minZoom ?? this.minZoom;
  }
}

ClassifiedPlot $ClassifiedPlotFromJson(Map<String, dynamic> json) {
  final ClassifiedPlot classifiedPlot = ClassifiedPlot();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    classifiedPlot.id = id;
  }
  final String? plotNo = jsonConvert.convert<String>(json['plot_no']);
  if (plotNo != null) {
    classifiedPlot.plotNo = plotNo;
  }
  final String? streetNo = jsonConvert.convert<String>(json['street_no']);
  if (streetNo != null) {
    classifiedPlot.streetNo = streetNo;
  }
  final String? streetType = jsonConvert.convert<String>(json['street_type']);
  if (streetType != null) {
    classifiedPlot.streetType = streetType;
  }
  final double? lat = jsonConvert.convert<double>(json['lat']);
  if (lat != null) {
    classifiedPlot.lat = lat;
  }
  final double? long = jsonConvert.convert<double>(json['long']);
  if (long != null) {
    classifiedPlot.long = long;
  }
  return classifiedPlot;
}

Map<String, dynamic> $ClassifiedPlotToJson(ClassifiedPlot entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['plot_no'] = entity.plotNo;
  data['street_no'] = entity.streetNo;
  data['street_type'] = entity.streetType;
  data['lat'] = entity.lat;
  data['long'] = entity.long;
  return data;
}

extension ClassifiedPlotExtension on ClassifiedPlot {
  ClassifiedPlot copyWith({
    int? id,
    String? plotNo,
    String? streetNo,
    String? streetType,
    double? lat,
    double? long,
  }) {
    return ClassifiedPlot()
      ..id = id ?? this.id
      ..plotNo = plotNo ?? this.plotNo
      ..streetNo = streetNo ?? this.streetNo
      ..streetType = streetType ?? this.streetType
      ..lat = lat ?? this.lat
      ..long = long ?? this.long;
  }
}

ClassifiedMedia $ClassifiedMediaFromJson(Map<String, dynamic> json) {
  final ClassifiedMedia classifiedMedia = ClassifiedMedia();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    classifiedMedia.id = id;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    classifiedMedia.url = url;
  }
  final String? thumb = jsonConvert.convert<String>(json['thumb']);
  if (thumb != null) {
    classifiedMedia.thumb = thumb;
  }
  final String? medium = jsonConvert.convert<String>(json['medium']);
  if (medium != null) {
    classifiedMedia.medium = medium;
  }
  return classifiedMedia;
}

Map<String, dynamic> $ClassifiedMediaToJson(ClassifiedMedia entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['url'] = entity.url;
  data['thumb'] = entity.thumb;
  data['medium'] = entity.medium;
  return data;
}

extension ClassifiedMediaExtension on ClassifiedMedia {
  ClassifiedMedia copyWith({
    int? id,
    String? url,
    String? thumb,
    String? medium,
  }) {
    return ClassifiedMedia()
      ..id = id ?? this.id
      ..url = url ?? this.url
      ..thumb = thumb ?? this.thumb
      ..medium = medium ?? this.medium;
  }
}