import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/login_entity.dart';
import 'package:property_dealers_directory/models/agency_entity.dart';

import 'package:property_dealers_directory/models/staff_entity.dart';


LoginEntity $LoginEntityFromJson(Map<String, dynamic> json) {
  final LoginEntity loginEntity = LoginEntity();
  final AgencyEntity? agency = jsonConvert.convert<AgencyEntity>(
      json['agency']);
  if (agency != null) {
    loginEntity.agency = agency;
  }
  final StaffEntity? staff = jsonConvert.convert<StaffEntity>(json['staff']);
  if (staff != null) {
    loginEntity.staff = staff;
  }
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    loginEntity.token = token;
  }
  return loginEntity;
}

Map<String, dynamic> $LoginEntityToJson(LoginEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['agency'] = entity.agency?.toJson();
  data['staff'] = entity.staff?.toJson();
  data['token'] = entity.token;
  return data;
}

extension LoginEntityExtension on LoginEntity {
  LoginEntity copyWith({
    AgencyEntity? agency,
    StaffEntity? staff,
    String? token,
  }) {
    return LoginEntity()
      ..agency = agency ?? this.agency
      ..staff = staff ?? this.staff
      ..token = token ?? this.token;
  }
}