import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/market_inventory_entity.dart';

MarketInventoryEntity $MarketInventoryEntityFromJson(
    Map<String, dynamic> json) {
  final MarketInventoryEntity marketInventoryEntity = MarketInventoryEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    marketInventoryEntity.id = id;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    marketInventoryEntity.price = price;
  }
  final String? extraInfo = jsonConvert.convert<String>(json['extra_info']);
  if (extraInfo != null) {
    marketInventoryEntity.extraInfo = extraInfo;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    marketInventoryEntity.status = status;
  }
  final int? isHot = jsonConvert.convert<int>(json['is_hot']);
  if (isHot != null) {
    marketInventoryEntity.isHot = isHot;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    marketInventoryEntity.createdAt = createdAt;
  }
  final int? staffId = jsonConvert.convert<int>(json['staff_id']);
  if (staffId != null) {
    marketInventoryEntity.staffId = staffId;
  }
  final int? cityId = jsonConvert.convert<int>(json['city_id']);
  if (cityId != null) {
    marketInventoryEntity.cityId = cityId;
  }
  final int? societyId = jsonConvert.convert<int>(json['society_id']);
  if (societyId != null) {
    marketInventoryEntity.societyId = societyId;
  }
  final int? blockId = jsonConvert.convert<int>(json['block_id']);
  if (blockId != null) {
    marketInventoryEntity.blockId = blockId;
  }
  final int? plotId = jsonConvert.convert<int>(json['plot_id']);
  if (plotId != null) {
    marketInventoryEntity.plotId = plotId;
  }
  final String? staffName = jsonConvert.convert<String>(json['staff_name']);
  if (staffName != null) {
    marketInventoryEntity.staffName = staffName;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    marketInventoryEntity.type = type;
  }
  final String? category = jsonConvert.convert<String>(json['category']);
  if (category != null) {
    marketInventoryEntity.category = category;
  }
  final String? agencyName = jsonConvert.convert<String>(json['agency_name']);
  if (agencyName != null) {
    marketInventoryEntity.agencyName = agencyName;
  }
  final String? staffPhone = jsonConvert.convert<String>(json['staff_phone']);
  if (staffPhone != null) {
    marketInventoryEntity.staffPhone = staffPhone;
  }
  final String? cityName = jsonConvert.convert<String>(json['city_name']);
  if (cityName != null) {
    marketInventoryEntity.cityName = cityName;
  }
  final String? societyName = jsonConvert.convert<String>(json['society_name']);
  if (societyName != null) {
    marketInventoryEntity.societyName = societyName;
  }
  final String? societyShort = jsonConvert.convert<String>(
      json['society_short']);
  if (societyShort != null) {
    marketInventoryEntity.societyShort = societyShort;
  }
  final String? blockName = jsonConvert.convert<String>(json['block_name']);
  if (blockName != null) {
    marketInventoryEntity.blockName = blockName;
  }
  final String? blockShort = jsonConvert.convert<String>(json['block_short']);
  if (blockShort != null) {
    marketInventoryEntity.blockShort = blockShort;
  }
  final String? plotSize = jsonConvert.convert<String>(json['plot_size']);
  if (plotSize != null) {
    marketInventoryEntity.plotSize = plotSize;
  }
  final String? plotName = jsonConvert.convert<String>(json['plot_name']);
  if (plotName != null) {
    marketInventoryEntity.plotName = plotName;
  }
  return marketInventoryEntity;
}

Map<String, dynamic> $MarketInventoryEntityToJson(
    MarketInventoryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['price'] = entity.price;
  data['extra_info'] = entity.extraInfo;
  data['status'] = entity.status;
  data['is_hot'] = entity.isHot;
  data['created_at'] = entity.createdAt;
  data['staff_id'] = entity.staffId;
  data['city_id'] = entity.cityId;
  data['society_id'] = entity.societyId;
  data['block_id'] = entity.blockId;
  data['plot_id'] = entity.plotId;
  data['staff_name'] = entity.staffName;
  data['type'] = entity.type;
  data['category'] = entity.category;
  data['agency_name'] = entity.agencyName;
  data['staff_phone'] = entity.staffPhone;
  data['city_name'] = entity.cityName;
  data['society_name'] = entity.societyName;
  data['society_short'] = entity.societyShort;
  data['block_name'] = entity.blockName;
  data['block_short'] = entity.blockShort;
  data['plot_size'] = entity.plotSize;
  data['plot_name'] = entity.plotName;
  return data;
}

extension MarketInventoryEntityExtension on MarketInventoryEntity {
  MarketInventoryEntity copyWith({
    int? id,
    String? price,
    String? extraInfo,
    int? status,
    int? isHot,
    String? createdAt,
    int? staffId,
    int? cityId,
    int? societyId,
    int? blockId,
    int? plotId,
    String? staffName,
    String? type,
    String? category,
    String? agencyName,
    String? staffPhone,
    String? cityName,
    String? societyName,
    String? societyShort,
    String? blockName,
    String? blockShort,
    String? plotSize,
    String? plotName,
  }) {
    return MarketInventoryEntity()
      ..id = id ?? this.id
      ..price = price ?? this.price
      ..extraInfo = extraInfo ?? this.extraInfo
      ..status = status ?? this.status
      ..isHot = isHot ?? this.isHot
      ..createdAt = createdAt ?? this.createdAt
      ..staffId = staffId ?? this.staffId
      ..cityId = cityId ?? this.cityId
      ..societyId = societyId ?? this.societyId
      ..blockId = blockId ?? this.blockId
      ..plotId = plotId ?? this.plotId
      ..staffName = staffName ?? this.staffName
      ..type = type ?? this.type
      ..category = category ?? this.category
      ..agencyName = agencyName ?? this.agencyName
      ..staffPhone = staffPhone ?? this.staffPhone
      ..cityName = cityName ?? this.cityName
      ..societyName = societyName ?? this.societyName
      ..societyShort = societyShort ?? this.societyShort
      ..blockName = blockName ?? this.blockName
      ..blockShort = blockShort ?? this.blockShort
      ..plotSize = plotSize ?? this.plotSize
      ..plotName = plotName ?? this.plotName;
  }
}