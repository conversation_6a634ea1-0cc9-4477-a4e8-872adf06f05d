// ignore_for_file: non_constant_identifier_names
// ignore_for_file: camel_case_types
// ignore_for_file: prefer_single_quotes

// This file is automatically generated. DO NOT EDIT, all your changes would be lost.
import 'package:flutter/material.dart' show debugPrint;
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/models/agency_entity.dart';
import 'package:property_dealers_directory/models/agency_short_entity.dart';
import 'package:property_dealers_directory/models/block_entity.dart';
import 'package:property_dealers_directory/models/blog_entity.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import 'package:property_dealers_directory/models/classified_entity.dart';
import 'package:property_dealers_directory/models/classified_short_entity.dart';
import 'package:property_dealers_directory/models/explore_data_entity.dart';
import 'package:property_dealers_directory/models/explore_entity.dart';
import 'package:property_dealers_directory/models/explore_short_entity.dart';
import 'package:property_dealers_directory/models/home_entity.dart';
import 'package:property_dealers_directory/models/inventory_design_entity.dart';
import 'package:property_dealers_directory/models/login_entity.dart';
import 'package:property_dealers_directory/models/market_inventory_entity.dart';
import 'package:property_dealers_directory/models/market_inventory_long_entity.dart';
import 'package:property_dealers_directory/models/market_inventory_short_entity.dart';
import 'package:property_dealers_directory/models/onboarding_entity.dart';
import 'package:property_dealers_directory/models/plot_entity.dart';
import 'package:property_dealers_directory/models/project_Short_entity.dart';
import 'package:property_dealers_directory/models/project_entity.dart';
import 'package:property_dealers_directory/models/property_entity.dart';
import 'package:property_dealers_directory/models/rates_entity.dart';
import 'package:property_dealers_directory/models/society_entity.dart';
import 'package:property_dealers_directory/models/society_file_entity.dart';
import 'package:property_dealers_directory/models/staff_entity.dart';
import 'package:property_dealers_directory/models/submit_agency_entity.dart';
import 'package:property_dealers_directory/models/user_plan_entity.dart';
import 'package:property_dealers_directory/models/video_entity.dart';

JsonConvert jsonConvert = JsonConvert();

typedef JsonConvertFunction<T> = T Function(Map<String, dynamic> json);
typedef EnumConvertFunction<T> = T Function(String value);
typedef ConvertExceptionHandler = void Function(Object error, StackTrace stackTrace);
extension MapSafeExt<K, V> on Map<K, V> {
  T? getOrNull<T>(K? key) {
    if (!containsKey(key) || key == null) {
      return null;
    } else {
      return this[key] as T?;
    }
  }
}

class JsonConvert {
  static ConvertExceptionHandler? onError;
  JsonConvertClassCollection convertFuncMap = JsonConvertClassCollection();

  /// When you are in the development, to generate a new model class, hot-reload doesn't find new generation model class, you can build on MaterialApp method called jsonConvert. ReassembleConvertFuncMap (); This method only works in a development environment
  /// https://flutter.cn/docs/development/tools/hot-reload
  /// class MyApp extends StatelessWidget {
  ///    const MyApp({Key? key})
  ///        : super(key: key);
  ///
  ///    @override
  ///    Widget build(BuildContext context) {
  ///      jsonConvert.reassembleConvertFuncMap();
  ///      return MaterialApp();
  ///    }
  /// }
  void reassembleConvertFuncMap() {
    bool isReleaseMode = const bool.fromEnvironment('dart.vm.product');
    if (!isReleaseMode) {
      convertFuncMap = JsonConvertClassCollection();
    }
  }

  T? convert<T>(dynamic value, {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    if (value is T) {
      return value;
    }
    try {
      return _asT<T>(value, enumConvert: enumConvert);
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return null;
    }
  }

  List<T?>? convertList<T>(List<dynamic>? value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return value
          .map((dynamic e) => _asT<T>(e, enumConvert: enumConvert))
          .toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  List<T>? convertListNotNull<T>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    if (value == null) {
      return null;
    }
    try {
      return (value as List<dynamic>).map((dynamic e) =>
      _asT<T>(e, enumConvert: enumConvert)!).toList();
    } catch (e, stackTrace) {
      debugPrint('asT<$T> $e $stackTrace');
      if (onError != null) {
        onError!(e, stackTrace);
      }
      return <T>[];
    }
  }

  T? _asT<T extends Object?>(dynamic value,
      {EnumConvertFunction? enumConvert}) {
    final String type = T.toString();
    final String valueS = value.toString();
    if (enumConvert != null) {
      return enumConvert(valueS) as T;
    } else if (type == "String") {
      return valueS as T;
    } else if (type == "int") {
      final int? intValue = int.tryParse(valueS);
      if (intValue == null) {
        return double.tryParse(valueS)?.toInt() as T?;
      } else {
        return intValue as T;
      }
    } else if (type == "double") {
      return double.parse(valueS) as T;
    } else if (type == "DateTime") {
      return DateTime.parse(valueS) as T;
    } else if (type == "bool") {
      if (valueS == '0' || valueS == '1') {
        return (valueS == '1') as T;
      }
      return (valueS == 'true') as T;
    } else if (type == "Map" || type.startsWith("Map<")) {
      return value as T;
    } else {
      if (convertFuncMap.containsKey(type)) {
        if (value == null) {
          return null;
        }
        var covertFunc = convertFuncMap[type]!;
        if (covertFunc is Map<String, dynamic>) {
          return covertFunc(value as Map<String, dynamic>) as T;
        } else {
          return covertFunc(Map<String, dynamic>.from(value)) as T;
        }
      } else {
        throw UnimplementedError(
            '$type unimplemented,you can try running the app again');
      }
    }
  }

  //list is returned by type
  static M? _getListChildType<M>(List<Map<String, dynamic>> data) {
    if (<AdBannerEntity>[] is M) {
      return data.map<AdBannerEntity>((Map<String, dynamic> e) =>
          AdBannerEntity.fromJson(e)).toList() as M;
    }
    if (<AgencyEntity>[] is M) {
      return data.map<AgencyEntity>((Map<String, dynamic> e) =>
          AgencyEntity.fromJson(e)).toList() as M;
    }
    if (<AgencyShortEntity>[] is M) {
      return data.map<AgencyShortEntity>((Map<String, dynamic> e) =>
          AgencyShortEntity.fromJson(e)).toList() as M;
    }
    if (<BlockEntity>[] is M) {
      return data.map<BlockEntity>((Map<String, dynamic> e) =>
          BlockEntity.fromJson(e)).toList() as M;
    }
    if (<BlogEntity>[] is M) {
      return data.map<BlogEntity>((Map<String, dynamic> e) =>
          BlogEntity.fromJson(e)).toList() as M;
    }
    if (<CityEntity>[] is M) {
      return data.map<CityEntity>((Map<String, dynamic> e) =>
          CityEntity.fromJson(e)).toList() as M;
    }
    if (<ClassifiedEntity>[] is M) {
      return data.map<ClassifiedEntity>((Map<String, dynamic> e) =>
          ClassifiedEntity.fromJson(e)).toList() as M;
    }
    if (<ClassifiedStaff>[] is M) {
      return data.map<ClassifiedStaff>((Map<String, dynamic> e) =>
          ClassifiedStaff.fromJson(e)).toList() as M;
    }
    if (<ClassifiedAgency>[] is M) {
      return data.map<ClassifiedAgency>((Map<String, dynamic> e) =>
          ClassifiedAgency.fromJson(e)).toList() as M;
    }
    if (<ClassifiedBlock>[] is M) {
      return data.map<ClassifiedBlock>((Map<String, dynamic> e) =>
          ClassifiedBlock.fromJson(e)).toList() as M;
    }
    if (<ClassifiedPlot>[] is M) {
      return data.map<ClassifiedPlot>((Map<String, dynamic> e) =>
          ClassifiedPlot.fromJson(e)).toList() as M;
    }
    if (<ClassifiedMedia>[] is M) {
      return data.map<ClassifiedMedia>((Map<String, dynamic> e) =>
          ClassifiedMedia.fromJson(e)).toList() as M;
    }
    if (<ClassifiedShortEntity>[] is M) {
      return data.map<ClassifiedShortEntity>((Map<String, dynamic> e) =>
          ClassifiedShortEntity.fromJson(e)).toList() as M;
    }
    if (<ExploreDataEntity>[] is M) {
      return data.map<ExploreDataEntity>((Map<String, dynamic> e) =>
          ExploreDataEntity.fromJson(e)).toList() as M;
    }
    if (<ExploreEntity>[] is M) {
      return data.map<ExploreEntity>((Map<String, dynamic> e) =>
          ExploreEntity.fromJson(e)).toList() as M;
    }
    if (<ExploreCollections>[] is M) {
      return data.map<ExploreCollections>((Map<String, dynamic> e) =>
          ExploreCollections.fromJson(e)).toList() as M;
    }
    if (<ExploreShortEntity>[] is M) {
      return data.map<ExploreShortEntity>((Map<String, dynamic> e) =>
          ExploreShortEntity.fromJson(e)).toList() as M;
    }
    if (<ExploreShortCategory>[] is M) {
      return data.map<ExploreShortCategory>((Map<String, dynamic> e) =>
          ExploreShortCategory.fromJson(e)).toList() as M;
    }
    if (<ExploreShortHours>[] is M) {
      return data.map<ExploreShortHours>((Map<String, dynamic> e) =>
          ExploreShortHours.fromJson(e)).toList() as M;
    }
    if (<HomeEntity>[] is M) {
      return data.map<HomeEntity>((Map<String, dynamic> e) =>
          HomeEntity.fromJson(e)).toList() as M;
    }
    if (<InventoryDesignEntity>[] is M) {
      return data.map<InventoryDesignEntity>((Map<String, dynamic> e) =>
          InventoryDesignEntity.fromJson(e)).toList() as M;
    }
    if (<LoginEntity>[] is M) {
      return data.map<LoginEntity>((Map<String, dynamic> e) =>
          LoginEntity.fromJson(e)).toList() as M;
    }
    if (<MarketInventoryEntity>[] is M) {
      return data.map<MarketInventoryEntity>((Map<String, dynamic> e) =>
          MarketInventoryEntity.fromJson(e)).toList() as M;
    }
    if (<MarketInventoryLongEntity>[] is M) {
      return data.map<MarketInventoryLongEntity>((Map<String, dynamic> e) =>
          MarketInventoryLongEntity.fromJson(e)).toList() as M;
    }
    if (<MarketInventoryShortEntity>[] is M) {
      return data.map<MarketInventoryShortEntity>((Map<String, dynamic> e) =>
          MarketInventoryShortEntity.fromJson(e)).toList() as M;
    }
    if (<OnboardingEntity>[] is M) {
      return data.map<OnboardingEntity>((Map<String, dynamic> e) =>
          OnboardingEntity.fromJson(e)).toList() as M;
    }
    if (<PlotEntity>[] is M) {
      return data.map<PlotEntity>((Map<String, dynamic> e) =>
          PlotEntity.fromJson(e)).toList() as M;
    }
    if (<ClassifiedMarker>[] is M) {
      return data.map<ClassifiedMarker>((Map<String, dynamic> e) =>
          ClassifiedMarker.fromJson(e)).toList() as M;
    }
    if (<ProjectShortEntity>[] is M) {
      return data.map<ProjectShortEntity>((Map<String, dynamic> e) =>
          ProjectShortEntity.fromJson(e)).toList() as M;
    }
    if (<ProjectEntity>[] is M) {
      return data.map<ProjectEntity>((Map<String, dynamic> e) =>
          ProjectEntity.fromJson(e)).toList() as M;
    }
    if (<ProjectItems>[] is M) {
      return data.map<ProjectItems>((Map<String, dynamic> e) =>
          ProjectItems.fromJson(e)).toList() as M;
    }
    if (<ProjectItemsPlans>[] is M) {
      return data.map<ProjectItemsPlans>((Map<String, dynamic> e) =>
          ProjectItemsPlans.fromJson(e)).toList() as M;
    }
    if (<PropertyEntity>[] is M) {
      return data.map<PropertyEntity>((Map<String, dynamic> e) =>
          PropertyEntity.fromJson(e)).toList() as M;
    }
    if (<RateEntity>[] is M) {
      return data.map<RateEntity>((Map<String, dynamic> e) =>
          RateEntity.fromJson(e)).toList() as M;
    }
    if (<RateRates>[] is M) {
      return data.map<RateRates>((Map<String, dynamic> e) =>
          RateRates.fromJson(e)).toList() as M;
    }
    if (<SocietyEntity>[] is M) {
      return data.map<SocietyEntity>((Map<String, dynamic> e) =>
          SocietyEntity.fromJson(e)).toList() as M;
    }
    if (<SocietyFileEntity>[] is M) {
      return data.map<SocietyFileEntity>((Map<String, dynamic> e) =>
          SocietyFileEntity.fromJson(e)).toList() as M;
    }
    if (<StaffEntity>[] is M) {
      return data.map<StaffEntity>((Map<String, dynamic> e) =>
          StaffEntity.fromJson(e)).toList() as M;
    }
    if (<SubmitAgencyEntity>[] is M) {
      return data.map<SubmitAgencyEntity>((Map<String, dynamic> e) =>
          SubmitAgencyEntity.fromJson(e)).toList() as M;
    }
    if (<UserPlanEntity>[] is M) {
      return data.map<UserPlanEntity>((Map<String, dynamic> e) =>
          UserPlanEntity.fromJson(e)).toList() as M;
    }
    if (<UserPlanFeatures>[] is M) {
      return data.map<UserPlanFeatures>((Map<String, dynamic> e) =>
          UserPlanFeatures.fromJson(e)).toList() as M;
    }
    if (<VideoEntity>[] is M) {
      return data.map<VideoEntity>((Map<String, dynamic> e) =>
          VideoEntity.fromJson(e)).toList() as M;
    }

    debugPrint("$M not found");

    return null;
  }

  static M? fromJsonAsT<M>(dynamic json) {
    if (json is M) {
      return json;
    }
    if (json is List) {
      return _getListChildType<M>(
          json.map((dynamic e) => e as Map<String, dynamic>).toList());
    } else {
      return jsonConvert.convert<M>(json);
    }
  }
}

class JsonConvertClassCollection {
  Map<String, JsonConvertFunction> convertFuncMap = {
    (AdBannerEntity).toString(): AdBannerEntity.fromJson,
    (AgencyEntity).toString(): AgencyEntity.fromJson,
    (AgencyShortEntity).toString(): AgencyShortEntity.fromJson,
    (BlockEntity).toString(): BlockEntity.fromJson,
    (BlogEntity).toString(): BlogEntity.fromJson,
    (CityEntity).toString(): CityEntity.fromJson,
    (ClassifiedEntity).toString(): ClassifiedEntity.fromJson,
    (ClassifiedStaff).toString(): ClassifiedStaff.fromJson,
    (ClassifiedAgency).toString(): ClassifiedAgency.fromJson,
    (ClassifiedBlock).toString(): ClassifiedBlock.fromJson,
    (ClassifiedPlot).toString(): ClassifiedPlot.fromJson,
    (ClassifiedMedia).toString(): ClassifiedMedia.fromJson,
    (ClassifiedShortEntity).toString(): ClassifiedShortEntity.fromJson,
    (ExploreDataEntity).toString(): ExploreDataEntity.fromJson,
    (ExploreEntity).toString(): ExploreEntity.fromJson,
    (ExploreCollections).toString(): ExploreCollections.fromJson,
    (ExploreShortEntity).toString(): ExploreShortEntity.fromJson,
    (ExploreShortCategory).toString(): ExploreShortCategory.fromJson,
    (ExploreShortHours).toString(): ExploreShortHours.fromJson,
    (HomeEntity).toString(): HomeEntity.fromJson,
    (InventoryDesignEntity).toString(): InventoryDesignEntity.fromJson,
    (LoginEntity).toString(): LoginEntity.fromJson,
    (MarketInventoryEntity).toString(): MarketInventoryEntity.fromJson,
    (MarketInventoryLongEntity).toString(): MarketInventoryLongEntity.fromJson,
    (MarketInventoryShortEntity).toString(): MarketInventoryShortEntity
        .fromJson,
    (OnboardingEntity).toString(): OnboardingEntity.fromJson,
    (PlotEntity).toString(): PlotEntity.fromJson,
    (ClassifiedMarker).toString(): ClassifiedMarker.fromJson,
    (ProjectShortEntity).toString(): ProjectShortEntity.fromJson,
    (ProjectEntity).toString(): ProjectEntity.fromJson,
    (ProjectItems).toString(): ProjectItems.fromJson,
    (ProjectItemsPlans).toString(): ProjectItemsPlans.fromJson,
    (PropertyEntity).toString(): PropertyEntity.fromJson,
    (RateEntity).toString(): RateEntity.fromJson,
    (RateRates).toString(): RateRates.fromJson,
    (SocietyEntity).toString(): SocietyEntity.fromJson,
    (SocietyFileEntity).toString(): SocietyFileEntity.fromJson,
    (StaffEntity).toString(): StaffEntity.fromJson,
    (SubmitAgencyEntity).toString(): SubmitAgencyEntity.fromJson,
    (UserPlanEntity).toString(): UserPlanEntity.fromJson,
    (UserPlanFeatures).toString(): UserPlanFeatures.fromJson,
    (VideoEntity).toString(): VideoEntity.fromJson,
  };

  bool containsKey(String type) {
    return convertFuncMap.containsKey(type);
  }

  JsonConvertFunction? operator [](String key) {
    return convertFuncMap[key];
  }
}