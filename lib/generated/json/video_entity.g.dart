import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/video_entity.dart';

VideoEntity $VideoEntityFromJson(Map<String, dynamic> json) {
  final VideoEntity videoEntity = VideoEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    videoEntity.id = id;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    videoEntity.title = title;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    videoEntity.description = description;
  }
  final String? videoId = jsonConvert.convert<String>(json['video_id']);
  if (videoId != null) {
    videoEntity.videoId = videoId;
  }
  final String? createdAt = jsonConvert.convert<String>(json['created_at']);
  if (createdAt != null) {
    videoEntity.createdAt = createdAt;
  }
  final String? updatedAt = jsonConvert.convert<String>(json['updated_at']);
  if (updatedAt != null) {
    videoEntity.updatedAt = updatedAt;
  }
  return videoEntity;
}

Map<String, dynamic> $VideoEntityToJson(VideoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['title'] = entity.title;
  data['description'] = entity.description;
  data['video_id'] = entity.videoId;
  data['created_at'] = entity.createdAt;
  data['updated_at'] = entity.updatedAt;
  return data;
}

extension VideoEntityExtension on VideoEntity {
  VideoEntity copyWith({
    int? id,
    String? title,
    String? description,
    String? videoId,
    String? createdAt,
    String? updatedAt,
  }) {
    return VideoEntity()
      ..id = id ?? this.id
      ..title = title ?? this.title
      ..description = description ?? this.description
      ..videoId = videoId ?? this.videoId
      ..createdAt = createdAt ?? this.createdAt
      ..updatedAt = updatedAt ?? this.updatedAt;
  }
}