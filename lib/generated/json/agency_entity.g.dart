import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/agency_entity.dart';
import 'package:property_dealers_directory/config.dart';

import 'package:property_dealers_directory/models/city_entity.dart';

import 'package:property_dealers_directory/models/staff_entity.dart';

import 'package:property_dealers_directory/services/remote_config.dart';


AgencyEntity $AgencyEntityFromJson(Map<String, dynamic> json) {
  final AgencyEntity agencyEntity = AgencyEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    agencyEntity.id = id;
  }
  final String? cityId = jsonConvert.convert<String>(json['city_id']);
  if (cityId != null) {
    agencyEntity.cityId = cityId;
  }
  final String? societyId = jsonConvert.convert<String>(json['society_id']);
  if (societyId != null) {
    agencyEntity.societyId = societyId;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    agencyEntity.logo = logo;
  }
  final String? background = jsonConvert.convert<String>(json['background']);
  if (background != null) {
    agencyEntity.background = background;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    agencyEntity.name = name;
  }
  final String? ceo = jsonConvert.convert<String>(json['ceo']);
  if (ceo != null) {
    agencyEntity.ceo = ceo;
  }
  final String? mobile1 = jsonConvert.convert<String>(json['mobile_1']);
  if (mobile1 != null) {
    agencyEntity.mobile1 = mobile1;
  }
  final String? mobile2 = jsonConvert.convert<String>(json['mobile_2']);
  if (mobile2 != null) {
    agencyEntity.mobile2 = mobile2;
  }
  final String? whatsapp = jsonConvert.convert<String>(json['whatsapp']);
  if (whatsapp != null) {
    agencyEntity.whatsapp = whatsapp;
  }
  final String? imo = jsonConvert.convert<String>(json['imo']);
  if (imo != null) {
    agencyEntity.imo = imo;
  }
  final String? landline1 = jsonConvert.convert<String>(json['landline_1']);
  if (landline1 != null) {
    agencyEntity.landline1 = landline1;
  }
  final String? landline2 = jsonConvert.convert<String>(json['landline_2']);
  if (landline2 != null) {
    agencyEntity.landline2 = landline2;
  }
  final String? fax = jsonConvert.convert<String>(json['fax']);
  if (fax != null) {
    agencyEntity.fax = fax;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    agencyEntity.email = email;
  }
  final String? website = jsonConvert.convert<String>(json['website']);
  if (website != null) {
    agencyEntity.website = website;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    agencyEntity.address = address;
  }
  final String? facebook = jsonConvert.convert<String>(json['facebook']);
  if (facebook != null) {
    agencyEntity.facebook = facebook;
  }
  final String? twitter = jsonConvert.convert<String>(json['twitter']);
  if (twitter != null) {
    agencyEntity.twitter = twitter;
  }
  final String? instagram = jsonConvert.convert<String>(json['instagram']);
  if (instagram != null) {
    agencyEntity.instagram = instagram;
  }
  final String? youtube = jsonConvert.convert<String>(json['youtube']);
  if (youtube != null) {
    agencyEntity.youtube = youtube;
  }
  final String? linkedin = jsonConvert.convert<String>(json['linkedin']);
  if (linkedin != null) {
    agencyEntity.linkedin = linkedin;
  }
  final String? latitude = jsonConvert.convert<String>(json['latitude']);
  if (latitude != null) {
    agencyEntity.latitude = latitude;
  }
  final String? longitude = jsonConvert.convert<String>(json['longitude']);
  if (longitude != null) {
    agencyEntity.longitude = longitude;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    agencyEntity.description = description;
  }
  final String? googleLocation = jsonConvert.convert<String>(
      json['google_location']);
  if (googleLocation != null) {
    agencyEntity.googleLocation = googleLocation;
  }
  final String? skills = jsonConvert.convert<String>(json['skills']);
  if (skills != null) {
    agencyEntity.skills = skills;
  }
  final bool? isFeatured = jsonConvert.convert<bool>(json['is_featured']);
  if (isFeatured != null) {
    agencyEntity.isFeatured = isFeatured;
  }
  final CityEntity? society = jsonConvert.convert<CityEntity>(json['society']);
  if (society != null) {
    agencyEntity.society = society;
  }
  final CityEntity? city = jsonConvert.convert<CityEntity>(json['city']);
  if (city != null) {
    agencyEntity.city = city;
  }
  final List<StaffEntity?>? staff = (json['staff'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffEntity>(e)).toList();
  if (staff != null) {
    agencyEntity.staff = staff;
  }
  return agencyEntity;
}

Map<String, dynamic> $AgencyEntityToJson(AgencyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['city_id'] = entity.cityId;
  data['society_id'] = entity.societyId;
  data['logo'] = entity.logo;
  data['background'] = entity.background;
  data['name'] = entity.name;
  data['ceo'] = entity.ceo;
  data['mobile_1'] = entity.mobile1;
  data['mobile_2'] = entity.mobile2;
  data['whatsapp'] = entity.whatsapp;
  data['imo'] = entity.imo;
  data['landline_1'] = entity.landline1;
  data['landline_2'] = entity.landline2;
  data['fax'] = entity.fax;
  data['email'] = entity.email;
  data['website'] = entity.website;
  data['address'] = entity.address;
  data['facebook'] = entity.facebook;
  data['twitter'] = entity.twitter;
  data['instagram'] = entity.instagram;
  data['youtube'] = entity.youtube;
  data['linkedin'] = entity.linkedin;
  data['latitude'] = entity.latitude;
  data['longitude'] = entity.longitude;
  data['description'] = entity.description;
  data['google_location'] = entity.googleLocation;
  data['skills'] = entity.skills;
  data['is_featured'] = entity.isFeatured;
  data['society'] = entity.society?.toJson();
  data['city'] = entity.city?.toJson();
  data['staff'] = entity.staff?.map((v) => v?.toJson()).toList();
  return data;
}

extension AgencyEntityExtension on AgencyEntity {
  AgencyEntity copyWith({
    int? id,
    String? cityId,
    String? societyId,
    String? logo,
    String? background,
    String? name,
    String? ceo,
    String? mobile1,
    String? mobile2,
    String? whatsapp,
    String? imo,
    String? landline1,
    String? landline2,
    String? fax,
    String? email,
    String? website,
    String? address,
    String? facebook,
    String? twitter,
    String? instagram,
    String? youtube,
    String? linkedin,
    String? latitude,
    String? longitude,
    String? description,
    String? googleLocation,
    String? skills,
    bool? isFeatured,
    CityEntity? society,
    CityEntity? city,
    List<StaffEntity?>? staff,
  }) {
    return AgencyEntity()
      ..id = id ?? this.id
      ..cityId = cityId ?? this.cityId
      ..societyId = societyId ?? this.societyId
      ..logo = logo ?? this.logo
      ..background = background ?? this.background
      ..name = name ?? this.name
      ..ceo = ceo ?? this.ceo
      ..mobile1 = mobile1 ?? this.mobile1
      ..mobile2 = mobile2 ?? this.mobile2
      ..whatsapp = whatsapp ?? this.whatsapp
      ..imo = imo ?? this.imo
      ..landline1 = landline1 ?? this.landline1
      ..landline2 = landline2 ?? this.landline2
      ..fax = fax ?? this.fax
      ..email = email ?? this.email
      ..website = website ?? this.website
      ..address = address ?? this.address
      ..facebook = facebook ?? this.facebook
      ..twitter = twitter ?? this.twitter
      ..instagram = instagram ?? this.instagram
      ..youtube = youtube ?? this.youtube
      ..linkedin = linkedin ?? this.linkedin
      ..latitude = latitude ?? this.latitude
      ..longitude = longitude ?? this.longitude
      ..description = description ?? this.description
      ..googleLocation = googleLocation ?? this.googleLocation
      ..skills = skills ?? this.skills
      ..isFeatured = isFeatured ?? this.isFeatured
      ..society = society ?? this.society
      ..city = city ?? this.city
      ..staff = staff ?? this.staff;
  }
}