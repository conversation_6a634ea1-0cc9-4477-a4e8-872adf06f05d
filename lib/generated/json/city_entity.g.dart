import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import 'package:property_dealers_directory/models/block_entity.dart';

import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';

import 'package:property_dealers_directory/services/remote_config.dart';


CityEntity $CityEntityFromJson(Map<String, dynamic> json) {
  final CityEntity cityEntity = CityEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    cityEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cityEntity.name = name;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    cityEntity.logo = logo;
  }
  final List<CityEntity>? societies = (json['societies'] as List<dynamic>?)
      ?.map(
          (e) => jsonConvert.convert<CityEntity>(e) as CityEntity)
      .toList();
  if (societies != null) {
    cityEntity.societies = societies;
  }
  final BlockEntity? block = jsonConvert.convert<BlockEntity>(json['block']);
  if (block != null) {
    cityEntity.block = block;
  }
  final String? shortName = jsonConvert.convert<String>(json['short_name']);
  if (shortName != null) {
    cityEntity.shortName = shortName;
  }
  return cityEntity;
}

Map<String, dynamic> $CityEntityToJson(CityEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['logo'] = entity.logo;
  data['societies'] = entity.societies.map((v) => v.toJson()).toList();
  data['block'] = entity.block?.toJson();
  data['short_name'] = entity.shortName;
  return data;
}

extension CityEntityExtension on CityEntity {
  CityEntity copyWith({
    int? id,
    String? name,
    String? logo,
    List<CityEntity>? societies,
    BlockEntity? block,
    String? shortName,
  }) {
    return CityEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..logo = logo ?? this.logo
      ..societies = societies ?? this.societies
      ..block = block ?? this.block
      ..shortName = shortName ?? this.shortName;
  }
}