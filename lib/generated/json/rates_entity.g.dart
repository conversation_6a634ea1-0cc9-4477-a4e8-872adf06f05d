import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/rates_entity.dart';

RateEntity $RateEntityFromJson(Map<String, dynamic> json) {
  final RateEntity rateEntity = RateEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    rateEntity.id = id;
  }
  final int? blockId = jsonConvert.convert<int>(json['block_id']);
  if (blockId != null) {
    rateEntity.blockId = blockId;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    rateEntity.title = title;
  }
  final String? updatedAt = jsonConvert.convert<String>(json['updated_at']);
  if (updatedAt != null) {
    rateEntity.updatedAt = updatedAt;
  }
  final List<RateRates>? rates = (json['rates'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<RateRates>(e) as RateRates).toList();
  if (rates != null) {
    rateEntity.rates = rates;
  }
  return rateEntity;
}

Map<String, dynamic> $RateEntityToJson(RateEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['block_id'] = entity.blockId;
  data['title'] = entity.title;
  data['updated_at'] = entity.updatedAt;
  data['rates'] = entity.rates?.map((v) => v.toJson()).toList();
  return data;
}

extension RateEntityExtension on RateEntity {
  RateEntity copyWith({
    int? id,
    int? blockId,
    String? title,
    String? updatedAt,
    List<RateRates>? rates,
  }) {
    return RateEntity()
      ..id = id ?? this.id
      ..blockId = blockId ?? this.blockId
      ..title = title ?? this.title
      ..updatedAt = updatedAt ?? this.updatedAt
      ..rates = rates ?? this.rates;
  }
}

RateRates $RateRatesFromJson(Map<String, dynamic> json) {
  final RateRates rateRates = RateRates();
  final String? max = jsonConvert.convert<String>(json['max']);
  if (max != null) {
    rateRates.max = max;
  }
  final String? min = jsonConvert.convert<String>(json['min']);
  if (min != null) {
    rateRates.min = min;
  }
  final String? size = jsonConvert.convert<String>(json['size']);
  if (size != null) {
    rateRates.size = size;
  }
  final String? sizeUnit = jsonConvert.convert<String>(json['size_unit']);
  if (sizeUnit != null) {
    rateRates.sizeUnit = sizeUnit;
  }
  return rateRates;
}

Map<String, dynamic> $RateRatesToJson(RateRates entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['max'] = entity.max;
  data['min'] = entity.min;
  data['size'] = entity.size;
  data['size_unit'] = entity.sizeUnit;
  return data;
}

extension RateRatesExtension on RateRates {
  RateRates copyWith({
    String? max,
    String? min,
    String? size,
    String? sizeUnit,
  }) {
    return RateRates()
      ..max = max ?? this.max
      ..min = min ?? this.min
      ..size = size ?? this.size
      ..sizeUnit = sizeUnit ?? this.sizeUnit;
  }
}