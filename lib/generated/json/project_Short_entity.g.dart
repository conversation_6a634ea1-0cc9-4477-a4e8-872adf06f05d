import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/project_Short_entity.dart';

ProjectShortEntity $ProjectShortEntityFromJson(Map<String, dynamic> json) {
  final ProjectShortEntity projectShortEntity = ProjectShortEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    projectShortEntity.id = id;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    projectShortEntity.logo = logo;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    projectShortEntity.title = title;
  }
  final String? location = jsonConvert.convert<String>(json['location']);
  if (location != null) {
    projectShortEntity.location = location;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    projectShortEntity.type = type;
  }
  final bool? isFeatured = jsonConvert.convert<bool>(json['is_featured']);
  if (isFeatured != null) {
    projectShortEntity.isFeatured = isFeatured;
  }
  return projectShortEntity;
}

Map<String, dynamic> $ProjectShortEntityToJson(ProjectShortEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['logo'] = entity.logo;
  data['title'] = entity.title;
  data['location'] = entity.location;
  data['type'] = entity.type;
  data['is_featured'] = entity.isFeatured;
  return data;
}

extension ProjectShortEntityExtension on ProjectShortEntity {
  ProjectShortEntity copyWith({
    int? id,
    String? logo,
    String? title,
    String? location,
    String? type,
    bool? isFeatured,
  }) {
    return ProjectShortEntity()
      ..id = id ?? this.id
      ..logo = logo ?? this.logo
      ..title = title ?? this.title
      ..location = location ?? this.location
      ..type = type ?? this.type
      ..isFeatured = isFeatured ?? this.isFeatured;
  }
}