import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/block_entity.dart';
import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';


BlockEntity $BlockEntityFromJson(Map<String, dynamic> json) {
  final BlockEntity blockEntity = BlockEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    blockEntity.id = id;
  }
  final int? societyId = jsonConvert.convert<int>(json['society_id']);
  if (societyId != null) {
    blockEntity.societyId = societyId;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    blockEntity.name = name;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    blockEntity.logo = logo;
  }
  final int? priority = jsonConvert.convert<int>(json['priority']);
  if (priority != null) {
    blockEntity.priority = priority;
  }
  final String? description = jsonConvert.convert<String>(json['description']);
  if (description != null) {
    blockEntity.description = description;
  }
  final double? extentX1 = jsonConvert.convert<double>(json['extent_x1']);
  if (extentX1 != null) {
    blockEntity.extentX1 = extentX1;
  }
  final double? extentX2 = jsonConvert.convert<double>(json['extent_x2']);
  if (extentX2 != null) {
    blockEntity.extentX2 = extentX2;
  }
  final double? extentY1 = jsonConvert.convert<double>(json['extent_y1']);
  if (extentY1 != null) {
    blockEntity.extentY1 = extentY1;
  }
  final double? extentY2 = jsonConvert.convert<double>(json['extent_y2']);
  if (extentY2 != null) {
    blockEntity.extentY2 = extentY2;
  }
  final String? mapUrl = jsonConvert.convert<String>(json['map_url']);
  if (mapUrl != null) {
    blockEntity.mapUrl = mapUrl;
  }
  final int? maxZoom = jsonConvert.convert<int>(json['max_zoom']);
  if (maxZoom != null) {
    blockEntity.maxZoom = maxZoom;
  }
  final int? minZoom = jsonConvert.convert<int>(json['min_zoom']);
  if (minZoom != null) {
    blockEntity.minZoom = minZoom;
  }
  final int? hasMap = jsonConvert.convert<int>(json['has_map']);
  if (hasMap != null) {
    blockEntity.hasMap = hasMap;
  }
  final int? isActive = jsonConvert.convert<int>(json['is_active']);
  if (isActive != null) {
    blockEntity.isActive = isActive;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    blockEntity.type = type;
  }
  final int? plotsCount = jsonConvert.convert<int>(json['plots_count']);
  if (plotsCount != null) {
    blockEntity.plotsCount = plotsCount;
  }
  final String? descriptions = jsonConvert.convert<String>(
      json['descriptions']);
  if (descriptions != null) {
    blockEntity.descriptions = descriptions;
  }
  final String? shortName = jsonConvert.convert<String>(json['short_name']);
  if (shortName != null) {
    blockEntity.shortName = shortName;
  }
  final double? centerLatitude = jsonConvert.convert<double>(
      json['center_latitude']);
  if (centerLatitude != null) {
    blockEntity.centerLatitude = centerLatitude;
  }
  final double? centerLongitude = jsonConvert.convert<double>(
      json['center_longitude']);
  if (centerLongitude != null) {
    blockEntity.centerLongitude = centerLongitude;
  }
  return blockEntity;
}

Map<String, dynamic> $BlockEntityToJson(BlockEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['society_id'] = entity.societyId;
  data['name'] = entity.name;
  data['logo'] = entity.logo;
  data['priority'] = entity.priority;
  data['description'] = entity.description;
  data['extent_x1'] = entity.extentX1;
  data['extent_x2'] = entity.extentX2;
  data['extent_y1'] = entity.extentY1;
  data['extent_y2'] = entity.extentY2;
  data['map_url'] = entity.mapUrl;
  data['max_zoom'] = entity.maxZoom;
  data['min_zoom'] = entity.minZoom;
  data['has_map'] = entity.hasMap;
  data['is_active'] = entity.isActive;
  data['type'] = entity.type;
  data['plots_count'] = entity.plotsCount;
  data['descriptions'] = entity.descriptions;
  data['short_name'] = entity.shortName;
  data['center_latitude'] = entity.centerLatitude;
  data['center_longitude'] = entity.centerLongitude;
  return data;
}

extension BlockEntityExtension on BlockEntity {
  BlockEntity copyWith({
    int? id,
    int? societyId,
    String? name,
    String? logo,
    int? priority,
    String? description,
    double? extentX1,
    double? extentX2,
    double? extentY1,
    double? extentY2,
    String? mapUrl,
    int? maxZoom,
    int? minZoom,
    int? hasMap,
    int? isActive,
    String? type,
    int? plotsCount,
    String? descriptions,
    String? shortName,
    double? centerLatitude,
    double? centerLongitude,
  }) {
    return BlockEntity()
      ..id = id ?? this.id
      ..societyId = societyId ?? this.societyId
      ..name = name ?? this.name
      ..logo = logo ?? this.logo
      ..priority = priority ?? this.priority
      ..description = description ?? this.description
      ..extentX1 = extentX1 ?? this.extentX1
      ..extentX2 = extentX2 ?? this.extentX2
      ..extentY1 = extentY1 ?? this.extentY1
      ..extentY2 = extentY2 ?? this.extentY2
      ..mapUrl = mapUrl ?? this.mapUrl
      ..maxZoom = maxZoom ?? this.maxZoom
      ..minZoom = minZoom ?? this.minZoom
      ..hasMap = hasMap ?? this.hasMap
      ..isActive = isActive ?? this.isActive
      ..type = type ?? this.type
      ..plotsCount = plotsCount ?? this.plotsCount
      ..descriptions = descriptions ?? this.descriptions
      ..shortName = shortName ?? this.shortName
      ..centerLatitude = centerLatitude ?? this.centerLatitude
      ..centerLongitude = centerLongitude ?? this.centerLongitude;
  }
}