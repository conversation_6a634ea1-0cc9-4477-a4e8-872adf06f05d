import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/market_inventory_short_entity.dart';

MarketInventoryShortEntity $MarketInventoryShortEntityFromJson(
    Map<String, dynamic> json) {
  final MarketInventoryShortEntity marketInventoryShortEntity = MarketInventoryShortEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    marketInventoryShortEntity.id = id;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    marketInventoryShortEntity.price = price;
  }
  final bool? isHot = jsonConvert.convert<bool>(json['is_hot']);
  if (isHot != null) {
    marketInventoryShortEntity.isHot = isHot;
  }
  final String? societyShort = jsonConvert.convert<String>(
      json['society_short']);
  if (societyShort != null) {
    marketInventoryShortEntity.societyShort = societyShort;
  }
  final String? blockShort = jsonConvert.convert<String>(json['block_short']);
  if (blockShort != null) {
    marketInventoryShortEntity.blockShort = blockShort;
  }
  final String? plotName = jsonConvert.convert<String>(json['plot_name']);
  if (plotName != null) {
    marketInventoryShortEntity.plotName = plotName;
  }
  return marketInventoryShortEntity;
}

Map<String, dynamic> $MarketInventoryShortEntityToJson(
    MarketInventoryShortEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['price'] = entity.price;
  data['is_hot'] = entity.isHot;
  data['society_short'] = entity.societyShort;
  data['block_short'] = entity.blockShort;
  data['plot_name'] = entity.plotName;
  return data;
}

extension MarketInventoryShortEntityExtension on MarketInventoryShortEntity {
  MarketInventoryShortEntity copyWith({
    int? id,
    String? price,
    bool? isHot,
    String? societyShort,
    String? blockShort,
    String? plotName,
  }) {
    return MarketInventoryShortEntity()
      ..id = id ?? this.id
      ..price = price ?? this.price
      ..isHot = isHot ?? this.isHot
      ..societyShort = societyShort ?? this.societyShort
      ..blockShort = blockShort ?? this.blockShort
      ..plotName = plotName ?? this.plotName;
  }
}