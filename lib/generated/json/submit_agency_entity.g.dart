import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/submit_agency_entity.dart';
import 'package:property_dealers_directory/models/society_entity.dart';


SubmitAgencyEntity $SubmitAgencyEntityFromJson(Map<String, dynamic> json) {
  final SubmitAgencyEntity submitAgencyEntity = SubmitAgencyEntity();
  final String? formKey = jsonConvert.convert<String>(json['form_key']);
  if (formKey != null) {
    submitAgencyEntity.formKey = formKey;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    submitAgencyEntity.name = name;
  }
  final String? designation = jsonConvert.convert<String>(json['designation']);
  if (designation != null) {
    submitAgencyEntity.designation = designation;
  }
  final String? phone = jsonConvert.convert<String>(json['phone']);
  if (phone != null) {
    submitAgencyEntity.phone = phone;
  }
  final String? agencyName = jsonConvert.convert<String>(json['agency_name']);
  if (agencyName != null) {
    submitAgencyEntity.agencyName = agencyName;
  }
  final String? ceoName = jsonConvert.convert<String>(json['ceo_name']);
  if (ceoName != null) {
    submitAgencyEntity.ceoName = ceoName;
  }
  final String? ceoPhone1 = jsonConvert.convert<String>(json['ceo_phone_1']);
  if (ceoPhone1 != null) {
    submitAgencyEntity.ceoPhone1 = ceoPhone1;
  }
  final String? ceoPhone2 = jsonConvert.convert<String>(json['ceo_phone_2']);
  if (ceoPhone2 != null) {
    submitAgencyEntity.ceoPhone2 = ceoPhone2;
  }
  final String? landline1 = jsonConvert.convert<String>(json['landline_1']);
  if (landline1 != null) {
    submitAgencyEntity.landline1 = landline1;
  }
  final String? landline2 = jsonConvert.convert<String>(json['landline_2']);
  if (landline2 != null) {
    submitAgencyEntity.landline2 = landline2;
  }
  final String? whatsapp = jsonConvert.convert<String>(json['whatsapp']);
  if (whatsapp != null) {
    submitAgencyEntity.whatsapp = whatsapp;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    submitAgencyEntity.email = email;
  }
  final String? fax = jsonConvert.convert<String>(json['fax']);
  if (fax != null) {
    submitAgencyEntity.fax = fax;
  }
  final String? cityId = jsonConvert.convert<String>(json['city_id']);
  if (cityId != null) {
    submitAgencyEntity.cityId = cityId;
  }
  final String? societyId = jsonConvert.convert<String>(json['society_id']);
  if (societyId != null) {
    submitAgencyEntity.societyId = societyId;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    submitAgencyEntity.address = address;
  }
  final String? facebook = jsonConvert.convert<String>(json['facebook']);
  if (facebook != null) {
    submitAgencyEntity.facebook = facebook;
  }
  final String? youtube = jsonConvert.convert<String>(json['youtube']);
  if (youtube != null) {
    submitAgencyEntity.youtube = youtube;
  }
  final String? twitter = jsonConvert.convert<String>(json['twitter']);
  if (twitter != null) {
    submitAgencyEntity.twitter = twitter;
  }
  final String? instagram = jsonConvert.convert<String>(json['instagram']);
  if (instagram != null) {
    submitAgencyEntity.instagram = instagram;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    submitAgencyEntity.message = message;
  }
  final String? website = jsonConvert.convert<String>(json['website']);
  if (website != null) {
    submitAgencyEntity.website = website;
  }
  final String? about = jsonConvert.convert<String>(json['about']);
  if (about != null) {
    submitAgencyEntity.about = about;
  }
  final String? source = jsonConvert.convert<String>(json['source']);
  if (source != null) {
    submitAgencyEntity.source = source;
  }
  final String? fcmId = jsonConvert.convert<String>(json['fcm_id']);
  if (fcmId != null) {
    submitAgencyEntity.fcmId = fcmId;
  }
  final SocietyEntity? society = jsonConvert.convert<SocietyEntity>(
      json['society']);
  if (society != null) {
    submitAgencyEntity.society = society;
  }
  return submitAgencyEntity;
}

Map<String, dynamic> $SubmitAgencyEntityToJson(SubmitAgencyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['form_key'] = entity.formKey;
  data['name'] = entity.name;
  data['designation'] = entity.designation;
  data['phone'] = entity.phone;
  data['agency_name'] = entity.agencyName;
  data['ceo_name'] = entity.ceoName;
  data['ceo_phone_1'] = entity.ceoPhone1;
  data['ceo_phone_2'] = entity.ceoPhone2;
  data['landline_1'] = entity.landline1;
  data['landline_2'] = entity.landline2;
  data['whatsapp'] = entity.whatsapp;
  data['email'] = entity.email;
  data['fax'] = entity.fax;
  data['city_id'] = entity.cityId;
  data['society_id'] = entity.societyId;
  data['address'] = entity.address;
  data['facebook'] = entity.facebook;
  data['youtube'] = entity.youtube;
  data['twitter'] = entity.twitter;
  data['instagram'] = entity.instagram;
  data['message'] = entity.message;
  data['website'] = entity.website;
  data['about'] = entity.about;
  data['source'] = entity.source;
  data['fcm_id'] = entity.fcmId;
  data['society'] = entity.society?.toJson();
  return data;
}

extension SubmitAgencyEntityExtension on SubmitAgencyEntity {
  SubmitAgencyEntity copyWith({
    String? formKey,
    String? name,
    String? designation,
    String? phone,
    String? agencyName,
    String? ceoName,
    String? ceoPhone1,
    String? ceoPhone2,
    String? landline1,
    String? landline2,
    String? whatsapp,
    String? email,
    String? fax,
    String? cityId,
    String? societyId,
    String? address,
    String? facebook,
    String? youtube,
    String? twitter,
    String? instagram,
    String? message,
    String? website,
    String? about,
    String? source,
    String? fcmId,
    SocietyEntity? society,
  }) {
    return SubmitAgencyEntity()
      ..formKey = formKey ?? this.formKey
      ..name = name ?? this.name
      ..designation = designation ?? this.designation
      ..phone = phone ?? this.phone
      ..agencyName = agencyName ?? this.agencyName
      ..ceoName = ceoName ?? this.ceoName
      ..ceoPhone1 = ceoPhone1 ?? this.ceoPhone1
      ..ceoPhone2 = ceoPhone2 ?? this.ceoPhone2
      ..landline1 = landline1 ?? this.landline1
      ..landline2 = landline2 ?? this.landline2
      ..whatsapp = whatsapp ?? this.whatsapp
      ..email = email ?? this.email
      ..fax = fax ?? this.fax
      ..cityId = cityId ?? this.cityId
      ..societyId = societyId ?? this.societyId
      ..address = address ?? this.address
      ..facebook = facebook ?? this.facebook
      ..youtube = youtube ?? this.youtube
      ..twitter = twitter ?? this.twitter
      ..instagram = instagram ?? this.instagram
      ..message = message ?? this.message
      ..website = website ?? this.website
      ..about = about ?? this.about
      ..source = source ?? this.source
      ..fcmId = fcmId ?? this.fcmId
      ..society = society ?? this.society;
  }
}