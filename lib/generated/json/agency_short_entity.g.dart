import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/agency_short_entity.dart';
import 'package:property_dealers_directory/services/remote_config.dart';


AgencyShortEntity $AgencyShortEntityFromJson(Map<String, dynamic> json) {
  final AgencyShortEntity agencyShortEntity = AgencyShortEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    agencyShortEntity.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    agencyShortEntity.name = name;
  }
  final String? ceo = jsonConvert.convert<String>(json['ceo']);
  if (ceo != null) {
    agencyShortEntity.ceo = ceo;
  }
  final String? logo = jsonConvert.convert<String>(json['logo']);
  if (logo != null) {
    agencyShortEntity.logo = logo;
  }
  final String? societyName = jsonConvert.convert<String>(json['society_name']);
  if (societyName != null) {
    agencyShortEntity.societyName = societyName;
  }
  final bool? isFeatured = jsonConvert.convert<bool>(json['is_featured']);
  if (isFeatured != null) {
    agencyShortEntity.isFeatured = isFeatured;
  }
  return agencyShortEntity;
}

Map<String, dynamic> $AgencyShortEntityToJson(AgencyShortEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['ceo'] = entity.ceo;
  data['logo'] = entity.logo;
  data['society_name'] = entity.societyName;
  data['is_featured'] = entity.isFeatured;
  return data;
}

extension AgencyShortEntityExtension on AgencyShortEntity {
  AgencyShortEntity copyWith({
    int? id,
    String? name,
    String? ceo,
    String? logo,
    String? societyName,
    bool? isFeatured,
  }) {
    return AgencyShortEntity()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..ceo = ceo ?? this.ceo
      ..logo = logo ?? this.logo
      ..societyName = societyName ?? this.societyName
      ..isFeatured = isFeatured ?? this.isFeatured;
  }
}