import 'package:property_dealers_directory/generated/json/base/json_convert_content.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';

AdBannerEntity $AdBannerEntityFromJson(Map<String, dynamic> json) {
  final AdBannerEntity adBannerEntity = AdBannerEntity();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    adBannerEntity.id = id;
  }
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    adBannerEntity.type = type;
  }
  final String? image = jsonConvert.convert<String>(json['image']);
  if (image != null) {
    adBannerEntity.image = image;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    adBannerEntity.url = url;
  }
  final String? position = jsonConvert.convert<String>(json['position']);
  if (position != null) {
    adBannerEntity.position = position;
  }
  return adBannerEntity;
}

Map<String, dynamic> $AdBannerEntityToJson(AdBannerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['type'] = entity.type;
  data['image'] = entity.image;
  data['url'] = entity.url;
  data['position'] = entity.position;
  return data;
}

extension AdBannerEntityExtension on AdBannerEntity {
  AdBannerEntity copyWith({
    int? id,
    String? type,
    String? image,
    String? url,
    String? position,
  }) {
    return AdBannerEntity()
      ..id = id ?? this.id
      ..type = type ?? this.type
      ..image = image ?? this.image
      ..url = url ?? this.url
      ..position = position ?? this.position;
  }
}