import 'dart:convert';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../models/city_entity.dart';

class CitySelected extends GetxController {
  GetStorage storage = GetStorage();

  CityEntity? city;
  CityEntity? society;

  CitySelected() {
    read();
  }

  bool get isSelected => city != null;
  bool get isSocietySelected => society != null;

  read() {
    String data = storage.read('SELECTED_CITY') ?? '';
    if (data.isEmpty) return;
    this.city = CityEntity.fromJson(jsonDecode(data));
    String _society = storage.read('SELECTED_SOCIETY') ?? '';
    if (_society.isEmpty) return;
    this.society = CityEntity.fromJson(jsonDecode(_society));
  }

  write(CityEntity _city) {
    this.city = _city;
    this.storage.write('SELECTED_CITY', jsonEncode(_city));
    FirebaseAnalytics.instance.setUserProperty(name: 'Selected_City', value: _city.name);
  }

  setSociety(CityEntity _society) {
    this.society = _society;
    this.storage.write('SELECTED_SOCIETY', jsonEncode(_society));
    FirebaseAnalytics.instance.setUserProperty(name: 'Selected_Society', value: _society.name);
  }

  List<CityEntity?>? get societies {
    if (city?.societies![0].id != 0) {
      CityEntity all = CityEntity();
      all.id = 0;
      all.name = 'All';
      city?.societies!.insert(0, all);
    }
    return city?.societies;
  }
}
