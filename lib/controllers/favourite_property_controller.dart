import 'dart:convert';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class FavouritePropertyController extends GetxController {
  GetStorage storage = GetStorage();

  List list = [];

  FavouritePropertyController() {
    read();
  }

  String get ids => list.join(',');

  void add(int? id) {
    if (id == null) return;
    if (list.contains(id)) return;
    list.add(id);
    write();
  }

  void remove(int? id) {
    if (list.contains(id)) {
      list.remove(id);
      write();
    }
  }

  bool isFav(int? id) {
    return list.contains(id);
  }

  read() {
    String data = storage.read('INVENTORY_FAV_LIST') ?? "";
    if (data.isEmpty) return;
    this.list = jsonDecode(data);
  }

  write() {
    this.storage.write('INVENTORY_FAV_LIST', jsonEncode(list));
  }
}
