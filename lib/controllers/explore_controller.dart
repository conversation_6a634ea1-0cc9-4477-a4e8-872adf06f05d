import 'package:get/get.dart';

import '../../models/explore_short_entity.dart';

class ExploreController extends GetxController {
  bool locationFilter = false;
  bool categoryFilter = false;
  int? cityId;
  int? societyId;
  int? blockId;
  String? query;
  List<ExploreShortCategory> categories = [];
  List<ExploreShortCategory> services = [];
  List<ExploreShortCategory> features = [];

  addExploreData(ExploreShortCategory selectedItem, String type) {
    List<ExploreShortCategory> list = [];
    if (type == "c") list = categories;
    if (type == "s") list = services;
    if (type == "f") list = features;
    list.addIf(!list.contains(selectedItem), selectedItem);
    if (list.isNotEmpty || query != null)
      categoryFilter = true;
    else
      categoryFilter = false;
    update();
    notifyChildrens();
  }

  removeExploreData(List<ExploreShortCategory> allSelectedItems, String type) {
    List<ExploreShortCategory> list = [];
    if (type == "c") list = categories;
    if (type == "s") list = services;
    if (type == "f") list = features;
    list.removeWhere((e) {
      print(!allSelectedItems.contains(e));
      return !allSelectedItems.contains(e);
    });
    if (list.isNotEmpty || query != null)
      categoryFilter = true;
    else
      categoryFilter = false;
    update();
    notifyChildrens();
  }
}
