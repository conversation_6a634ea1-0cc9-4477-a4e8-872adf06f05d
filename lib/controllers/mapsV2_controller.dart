import 'package:get/get.dart';
import 'package:latlong2/latlong.dart';

import '../data/api.dart';
import '../models/block_entity.dart';
import '../models/city_entity.dart';
import '../models/plot_entity.dart';

class MapsV2Controller extends GetxController {
  List<CityEntity?> societies = [];
  List<BlockEntity?> blocks = [];
  List<PlotEntity?> plots = [];

  CityEntity city = CityEntity();
  CityEntity society = CityEntity();
  BlockEntity block = BlockEntity();
  PlotEntity plot = PlotEntity();

  bool get isPlotLoaded => plot.id != null;

  LatLng get center {
    if (this.isPlotLoaded) return LatLng(plot.lat.toDouble(), plot.long.toDouble());
    return LatLng(block.centerLatitude.toDouble(), block.centerLongitude.toDouble());
  }

  Future<void> citySelected(List<CityEntity> cities, String? _city) async {
    societies = [];
    society = CityEntity();
    blocks = [];
    block = BlockEntity();
    plots = [];
    plot = PlotEntity();
    city = cities.firstWhere((e) => e.name == _city);
    societies = cities.firstWhere((e) => e.name == _city).societies!;
  }

  Future<void> societySelected(String? _society) async {
    blocks = [];
    block = BlockEntity();
    plots = [];
    plot = PlotEntity();
    society = societies.firstWhere((e) => e?.name == _society)!;
    await getBlocks();
  }

  Future<void> blockSelected(String? _block) async {
    plots = [];
    plot = PlotEntity();
    block = blocks.firstWhere((e) => e?.name == _block)!;
    await getPlots();
  }

  Future<void> getBlocks() async {
    var value = await API.blocksList(society.id, cityId: city.id, category: "");
    if (value.status!.status == ApiStatus.OK) {
      blocks = value.data!;
    }
  }

  Future<void> getPlots() async {
    var value = await API.getPlotList(block.id);
    if (value.status!.status == ApiStatus.OK) {
      plots = value.data!;
    }
  }
}
