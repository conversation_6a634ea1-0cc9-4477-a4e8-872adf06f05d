import 'package:get/get.dart';

import '../models/block_entity.dart';
import '../models/city_entity.dart';

class LocationDataController extends GetxController {
  int? city;
  int? society;
  int? block;
  String type = 'All';

  bool showBlockSection = false;

  CityEntity? currentCity;
  CityEntity? currentSociety;
  BlockEntity? currentBlock;
  List<BlockEntity?> societyBlocks = [];
  List<BlockEntity?> allBlocks = [];
}
