import 'dart:convert';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'user_plan_controller.dart';
import 'package:property_dealers_directory/data/api.dart';

import '../models/agency_entity.dart';
import '../models/login_entity.dart';
import '../models/staff_entity.dart';

class LoginController extends GetxController {
  GetStorage storage = GetStorage();

  LoginController() {
    readInfo();
  }

  String fcm = '';
  String token = '';
  LoginEntity? user;

  AgencyEntity? get agency => user?.agency;

  StaffEntity? get staff => user?.staff;

  bool get isLoggedIn => token.length > 0;

  read() {
    this.token = storage.read('LOGIN_TOKEN') ?? '';
  }

  readInfo() {
    this.read();
    if (!isLoggedIn) return;
    this.user = null;
    String? info = storage.read('USER_INFO');
    if (info == null || info.isEmpty) return;
    var login = LoginEntity.fromJson(jsonDecode(info));
    this.user = login;
  }

  login(LoginEntity loginEntity) {
    storage.write('USER_IS_LOGGED', true);
    storage.write('LOGIN_TOKEN', loginEntity.token);
    storage.write('USER_INFO', jsonEncode(loginEntity.toJson()));
    this.token = loginEntity.token;
    this.user = loginEntity;
    updateToken();
    setFirebaseProperties();
    Get.find<UserPlanController>().refresh();
  }

  logout() async {
    storage.write('LOGIN_TOKEN', '');
    storage.write('USER_IS_LOGGED', false);
    storage.write('USER_INFO', '');
    token = '';
    user = null;
    // Get.find<UserPlanController>().clear();
  }

  void updateToken() {
    if (!isLoggedIn || fcm.isEmpty) return;
    API.updateToken(fcm);
  }

  setFirebaseProperties() {
    final FirebaseAnalytics firebase = FirebaseAnalytics.instance;
    firebase.setUserProperty(name: 'User_Logged', value: isLoggedIn ? 'true' : 'false');
    firebase.setUserProperty(name: 'User_ID', value: staff?.id.toString() ?? '');
    firebase.setUserProperty(name: 'User_Agency_ID', value: agency?.id.toString() ?? '');
    firebase.setUserProperty(name: 'User_Agency_Name', value: agency?.name ?? '');
  }
}
