import 'dart:convert';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../data/api.dart';
import '../models/user_plan_entity.dart';
import 'login_controller.dart';

class UserPlanController extends GetxController {
  GetStorage storage = GetStorage();
  final LoginController login = Get.find();
  UserPlanEntity? plan;

  bool get isValid {
    if (plan == null) return false;
    return login.isLoggedIn && !plan!.expired;
  }

  UserPlanController() {
    read();
  }

  read() {
    var _plan = storage.read<String>('user_plan');

    if (_plan != null && _plan.isNotEmpty) {
      plan = UserPlanEntity.fromJson(jsonDecode(_plan) ?? {});
    }
  }

  clear() {
    storage.write('user_plan', '');
    plan = null;
  }

  refresh() {
    if(login.isLoggedIn)
      API.getUserPlan().then((value) {
        plan = value.data;
        FirebaseAnalytics.instance.setUserProperty(name: 'User_Plan_Active', value: isValid ? 'true' : 'false');
        storage.write('user_plan', jsonEncode(plan));
      });
  }
}
