import 'dart:convert';
import 'dart:typed_data';

import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

import '../models/society_entity.dart';
import '../models/submit_agency_entity.dart';

class AgencyRequestController extends GetxController {
  GetStorage storage = GetStorage();

  AgencyRequestController() {
    readInfo();
  }

  Uint8List? croppedImageA;
  Uint8List? croppedImage;
  SocietyEntity? society;
  SubmitAgencyEntity? agencyEntity;

  readInfo() {
    String data = storage.read('AGENCY_DETAIL') ?? "";
    if (data.isEmpty) return;
    this.agencyEntity = SubmitAgencyEntity.fromJson(jsonDecode(data));
  }

  writeInfo(SubmitAgencyEntity _agencyEntity) {
    storage.write('AGENCY_DETAIL', jsonEncode(_agencyEntity.toJson()));
    this.agencyEntity = _agencyEntity;
  }
}
