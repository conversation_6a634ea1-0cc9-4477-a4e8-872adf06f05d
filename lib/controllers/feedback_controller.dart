import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';

class FeedbackController extends GetxController {
  GetStorage storage = GetStorage();

  int clicks = 0;
  DateTime? previousDate;
  bool submitted = false;

  FeedbackController() {
    clicks = storage.read<int>('feedback_clicks') ?? 0;
    submitted = storage.read<bool>('feedback_submitted') ?? false;
    String date = storage.read<String>('feedback_previous') ?? '';

    if (date.isNotEmpty) {
      previousDate = DateTime.parse(date);
    }
  }

  bool get canShowFeedback {

    if(submitted)
      return false;

    clicks++;
    storage.write('feedback_clicks', clicks);

    print(clicks);
    if (clicks == 3) {
      return true;
    }
    if (previousDate != null && previousDate!.add(Duration(days: 5)).isBefore(DateTime.now())) {
      return true;
    }

    if(clicks > 10 && previousDate == null){
      return true;
    }

    return false;
  }

  showDialog() {
    previousDate = DateTime.now();
    storage.write('feedback_previous',previousDate.toString());
  }

  submit(){
    submitted = true;
    storage.write('feedback_submitted', submitted);
  }
}
