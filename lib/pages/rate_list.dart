import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

import '../config.dart';
import '../data/api.dart';
import '../models/rates_entity.dart';
import '../services/remote_config.dart';
import '../utils/Notify.dart';
import '../utils/functions.dart';
import '../utils/helpers.dart';
import '../utils/sliver_list_base_state.dart';
import '../utils/widgets.dart';
import '../widgets/not_available.dart';

class RateList extends StatefulWidget {
  final int id;
  final String title;

  const RateList(
    this.id,
    this.title,
  );

  @override
  _RateListState createState() => _RateListState();
}

class _RateListState extends SliverListBaseState<RateList, List<RateEntity>> {
  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  String query = '';

  @override
  String get title {
    if (query.isEmpty)
      return widget.title;
    else
      return 'Search results for "$query"';
  }

  @override
  void getData() {
    API.getRateList(widget.id).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'RateListPage');
  }

  @override
  bool get paginate => true;

  @override
  double get expandedHeight => 120;

  @override
  Widget get customAppBar => PreferredSize(
        preferredSize: Size.fromHeight(120),
        child: AppBar(
            elevation: 2,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
            title: Text(title),
            flexibleSpace: Padding(
              padding: const EdgeInsets.only(top: 83, left: 20, right: 20, bottom: 10),
              child: FormBuilder(
                key: _fbKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: <Widget>[
                        Container(
                          height: 42,
                          width: MediaQuery.of(context).size.width * .888,
                          padding: EdgeInsets.symmetric(horizontal: 5.0),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(
                                color: Colors.grey[400]!,
                                width: 0.8,
                              ),
                              borderRadius: BorderRadius.circular(5.0)),
                          child: Row(
                            children: [
                              Expanded(
                                child: FormBuilderTextField(
                                    textInputAction: TextInputAction.search,
                                    initialValue: '',
                                    name: 'query',
                                    decoration: InputDecoration(hintText: 'Search block', border: InputBorder.none),
                                    onSubmitted: (value) {
                                      searchPressed();
                                    }),
                              ),
                              InkWell(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: searchPressed,
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                                  decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5.0)),
                                  child: Icon(
                                    Icons.search,
                                    size: 18.0,
                                    color: Colors.white,
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            )),
      );

  void searchPressed() {
    _fbKey.currentState!.save();
    query = _fbKey.currentState!.value['query'].toString().toLowerCase();
    query = query.trim();
    setState(() {
      if (!response!.data!.any((element) => element.title.toLowerCase().contains(query))) {
        data = response!.data;
        Notify.toast('Block not found for your search term');
        // Notify.showSnack(context, 'Block not found for your search term');
      } else {
        data = response!.data!.where((text) => text.title.toLowerCase().contains(query)).toList();
      }
    });
    sendAnalyticsEvent(eventName: 'list_rates_search_button_click');
  }

  @override
  Widget listItem(int index) {
    RateEntity res = data![index];
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: TileCard(
        elevation: 2,
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text("${res.title}", style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold)),
              ],
            ),
            Divider(thickness: 1, color: Colors.black),
            Table(
              children: [
                TableRow(children: [
                  TableCell(
                    child: Column(
                      children: [
                        Text("Size", style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold)),
                        Divider(thickness: 1, color: Colors.black),
                        SizedBox(height: 10.0),
                        for (int i = 0; i < res.rates!.length; i++)
                          Text("${res.rates![i].size} ${res.rates![i].sizeUnit}\n"),
                      ],
                    ),
                  ),
                  TableCell(
                    child: Column(
                      children: [
                        Text("Min", style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold)),
                        Divider(thickness: 1, color: Colors.black),
                        SizedBox(height: 10.0),
                        for (int i = 0; i < res.rates!.length; i++) Text("${res.rates![i].min}  \n"),
                      ],
                    ),
                  ),
                  TableCell(
                    child: Column(
                      children: [
                        Text("Max", style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.bold)),
                        Divider(thickness: 1, color: Colors.black),
                        SizedBox(height: 10.0),
                        for (int i = 0; i < res.rates!.length; i++) Text("${res.rates![i].max}\n"),
                      ],
                    ),
                  ),
                ]),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget get notFound => Container(
        alignment: Alignment.center,
        child: NotAvailable(
          'Rates for this block are not available for now, call for more information.',
          title: 'Rates not available.',
          buttonText: 'Call Now',
          callback: (BuildContext _context) {
            openLink('tel:${RemoteConfigService.contactPhone}');
          },
        ),
      );
}
