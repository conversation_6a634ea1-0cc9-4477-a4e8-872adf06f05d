import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:property_dealers_directory/controllers/feedback_controller.dart';
import 'package:property_dealers_directory/services/remote_config.dart';

import '../common/main_textField.dart';
import '../controllers/login_controller.dart';
import '../data/api.dart';
import '../nav.dart';
import '../providers/onboarding_provider.dart';
import '../utils/Notify.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class SplashPage extends StatefulWidget {
  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> {

  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(microseconds: 300)).then((value) {
      if(RemoteConfigService.apiUrl.isEmpty){
        RemoteConfigService.fetch();
      }

      LoginController controller = Get.find();
      controller.readInfo();
      obProvider.refreshCity();
    });
    Future.delayed(Duration(seconds: 3)).then((_) {
      FeedbackController feedback = Get.find();
      if (Get.currentRoute.contains('PropertiesListPage')) return;
      if (obProvider.isSocietySelected){
        if(obProvider.isIndividual && feedback.canShowFeedback){
          Nav.feedbackPage(context);
          feedback.showDialog();
          return;
        } else {
          Nav.homePage(context);
        }
      }
      else
        Nav.userLocationPage(context);
    });
  }

  // if(feedback.canShowFeedback){
  //   _showDialog(context);
  //   feedback.showDialog();
  // }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: <Widget>[
          Positioned.fill(child: Image.asset("images/splash_bg_simple.png", fit: BoxFit.cover)),
          Center(child: Lottie.asset('animations/splash_lottie.json', repeat: false, height: 200, width: 200)),
        ],
      ),
    );
  }
  void _showDialog(context) {
    showDialog<void>(
      context: context,
      // barrierDismissible: true, // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        FeedbackController feedback = Get.find();
        return SimpleDialog(
          title: Text('Feedback'),
          children: [
            Container(
              height: MediaQuery.of(context).size.height * .7,
              child: SingleChildScrollView(
                child: FormBuilder(
                  key: _fbKey,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Name")),
                        FormBuilderTextField(
                          name: 'name',
                          keyboardType: TextInputType.name,
                          decoration: InputStyle(hint: "Enter name", icon: Icons.person),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Name is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Mobile #")),
                        FormBuilderTextField(
                          name: 'phone',
                          keyboardType: TextInputType.number,
                          decoration: InputStyle(hint: "Enter mobile", icon: Icons.phone),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Mobile is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Current City")),
                        FormBuilderTextField(
                          name: 'city',
                          keyboardType: TextInputType.text,
                          decoration: InputStyle(hint: "Enter current city", icon: Icons.location_city),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'City is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Feedback")),
                        FormBuilderTextField(
                          name: 'feedback',
                          maxLines: 4,
                          keyboardType: TextInputType.text,
                          decoration: InputStyle(hint: "Enter message", icon: Icons.message),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Message is required'),
                          ]),
                        ),
                        const SizedBox(height: 20),
                        Center(child: PrimaryButton(text: "Submit", onPressed: () async{
                          if (_fbKey.currentState!.saveAndValidate()) {
                            Notify.progress(context, 'Please wait...');
                            APIResponse<dynamic> result = await API.feedbackFormSubmit(_fbKey.currentState!.value);
                            if (result.isOK) {
                              print(result);
                              Notify.hide();
                              Notify.toast('Request Submit Successfully', type: 1);
                              feedback.submit();
                              Nav.back(context);
                            } else {
                              Notify.hide();
                              Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
                            }
                          }
                        })),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: GestureDetector(
                              onTap: (){
                                Nav.back(context);
                              },
                              child: Text("Skip")),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

}
