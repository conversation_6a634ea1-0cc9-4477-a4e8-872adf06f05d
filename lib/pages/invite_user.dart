import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:property_dealers_directory/data/api.dart';
import 'package:property_dealers_directory/utils/Notify.dart';
import 'package:property_dealers_directory/utils/widgets.dart';

class InviteUser extends StatefulWidget {
  const InviteUser({Key? key}) : super(key: key);

  @override
  _InviteUserState createState() => _InviteUserState();

  static openDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: InviteUser(),
        );
      },
    );
  }
}

class _InviteUserState extends State<InviteUser> {
  GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return Container(
      child: FormBuilder(
        key: _fb<PERSON><PERSON>,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            FormBuilderTextField(
              name: 'phone',
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: 'Phone',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
                FormBuilderValidators.numeric(),
                FormBuilderValidators.minLength(11),
                FormBuilderValidators.maxLength(11),
              ]),
            ),
            FormBuilderTextField(
              name: 'name',
              decoration: InputDecoration(
                labelText: 'Name',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
                FormBuilderValidators.minLength(3),
              ]),
            ),
            FormBuilderTextField(
              name: 'designation',
              decoration: InputDecoration(
                labelText: 'Designation',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
              ]),
            ),
            FormBuilderTextField(
              name: 'agency_name',
              decoration: InputDecoration(
                labelText: 'Agency Name',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
              ]),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    if (_fbKey.currentState!.saveAndValidate()) {
                      print(_fbKey.currentState!.value);
                      Notify.progress(context, 'Please Wait...');
                      var resp =
                          await API.sendUserInvite(_fbKey.currentState!.value);
                      Notify.hide();
                      if (resp.isOK) {
                        Navigator.pop(context);
                        Notify.toast(resp.status?.message, type: 1);
                      } else {
                        Notify.toast(resp.status?.message);
                      }
                    }
                  },
                  child: Text('Send Invite'),
                ),
                CancelButton(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
