import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:property_dealers_directory/data/api.dart';

import '../../models/market_inventory_long_entity.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../../utils/helpers.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import '../../widgets/social.dart';
import '../classified_property/classified_location_map.dart';

class MarketInventoryDetails extends StatefulWidget {

  final int id;

  MarketInventoryDetails({Key? key, required this.id}) : super(key: key);

  @override
  State<MarketInventoryDetails> createState() => _MarketInventoryDetailsState();
}

class _MarketInventoryDetailsState extends BaseState<MarketInventoryDetails, MarketInventoryLongEntity> {



  @override
  String get title => data == null ? 'Loading...' : data!.type + ' ' + data!.category ;

  @override
  Widget body() {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TileCard(
              elevation: 2,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(child: CustomRow(heading: data!.cityName, text: data!.societyName)),
                      Expanded(child: CustomRow(heading: "", text: data!.blockShort)),
                    ],
                  ),
                  Divider(thickness: 1),
                  Row(
                    children: [
                      Expanded(flex: 3, child: CustomRow(heading: "Plot:", text: data!.plotName)),
                      Expanded(flex: 2,child:  CustomRow(heading: "Plot Size:", text: data!.plotSize)),
                      Expanded(flex: 2, child:  CustomRow(heading: "Price:", text: data!.price, maxLines: 2,)),
                    ],
                  ),
                  Divider(thickness: 1),
                  CustomRow(heading: "Extra Information:", text: data!.extraInfo.toString()),

                ],
              ),
            ),
            SizedBox(height: 10.0),
            TileCard(
              horizontal: 0.0,
              child: ListTile(
                title: Text(data!.staffName),
                subtitle: Text(data!.agencyName),
                leading: NetworkImageWithPlaceholder(data!.staffImage, height: 50, width: 50, circular: true),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    SocialIcon.type(
                      'Call',
                      SocialIcon.PHONE,
                      onPressed: (){
                        openLink('tel: +92${data!.staffPhone.substring(1)}');
                        sendAnalyticsEvent(
                            eventName: 'detail_market_inventory_callAgent_button_click',
                            parameters: {'market_inventory_id': widget.id});
                        Map<String, dynamic> body = {};
                        body['id'] = data!.id;
                        body['action'] = "Phone Call";
                        body['model'] = "market_inventory";
                        API.postAnalytics(body);
                      },
                    ),
                    SizedBox(width: 10.0),
                    SocialIcon.type(
                      'WhatsApp',
                      SocialIcon.WHATSAPP,
                      onPressed: (){
                        openWhatsApp(
                          data!.staffPhone,
                          message: 'I have seen your agency in ilaaqa.com app',
                        );
                        sendAnalyticsEvent(
                            eventName: 'detail_market_inventory_whatsAppAgent_click',
                            parameters: {'classified_id': widget.id});
                        Map<String, dynamic> body = {};
                        body['id'] = data!.id;
                        body['action'] = "Whatsapp Call";
                        body['model'] = "market_inventory";
                        API.postAnalytics(body);
                      },
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 10.0),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Heading.big('Location:', font: 16),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 2.0),
                  child: Container(
                    height: 200,
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(10.0),
                      child: ClassifiedLocationMap(
                        lat: data!.lat,
                        long: data!.long,
                        mapURL: data!.mapUrl,
                        minZoom: data!.minZoom,
                        maxZoom: data!.maxZoom,
                        plotName: data!.plotName,
                        id: data!.id,
                      ),
                    ),
                  ),
                ),
                SizedBox(height: 10),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  void getData() {
    API.getMarketInventoryDetail(widget.id).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'MarketInventoryDetailPage');
  }
}

class CustomRow extends StatelessWidget {
  final String heading;
  final String text;
  final int? maxLines;
  const CustomRow({Key? key, required this.heading, required this.text, this.maxLines}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 5.0, horizontal: 10.0),
      width: sWidth,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(heading),
          SizedBox(height: 5.0),
          AutoSizeText(
            text,
            maxLines: maxLines,
            style: TextStyle(color: Colors.black, fontSize: 16, fontWeight: FontWeight.w400),
          ),
        ],
      ),
    );
  }
}


