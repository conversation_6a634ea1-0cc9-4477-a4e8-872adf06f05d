import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_progress_hud/flutter_progress_hud.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show toBeginningOfSentenceCase;
import 'package:package_info_plus/package_info_plus.dart';

import '../../common/common_button.dart';
import '../../common/main_textField.dart';
import '../../controllers/login_controller.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../models/plot_entity.dart';
import '../../models/property_entity.dart';
import '../../models/society_entity.dart';
import '../../nav.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';



class AddMarketInventory extends StatefulWidget {
  final int? propertyId;

  const AddMarketInventory({this.propertyId});

  @override
  _AddMarketInventoryState createState() => _AddMarketInventoryState();
}

class _AddMarketInventoryState extends State<AddMarketInventory> {
  final key = new GlobalKey<FormState>();
  final _fbKey = GlobalKey<FormBuilderState>();
  final _dropdownKey = GlobalKey<DropdownSearchState>();
  final _dropdownBlock = GlobalKey<DropdownSearchState>();
  final LoginController controller = Get.find();
  final priceController = TextEditingController();

  List typesList = ['Residential', 'Commercial'];
  List categories = ['File', 'Plot','House', 'Villa', 'Office', 'Shop', 'Apartment', 'Flat', 'Plaza'];
  String type = 'Residential';
  List<CityEntity?> cities = [];
  List<CityEntity?>? societies = [];
  List<BlockEntity?>? blocks = [];
  List<PlotEntity?>? plots = [];
  List? featuresList;
  PropertyEntity? property;
  bool isEditPage = false;
  bool checkBoxValue = false;
  String? category;
  int? cityId;
  int? societyId;
  int? blockId;
  int? plotId;
  int maxLines = 5;
  int price = 0;

  String? categoryValue;
  PackageInfo? uaData;

  @override
  void initState() {
    getAppData();
    if (widget.propertyId == null)
      getCity();
    else
      getPropertyDetail(widget.propertyId);
    sendAnalyticsScreenEvent(
        screenName: widget.propertyId == null ? 'AddMarketInventoryPage' : 'UpdateMarketInventoryPage');
    super.initState();
  }


  getPropertyDetail(id) async {
    await API.getPropertyDetail(id).then((value) {
      if (value.status!.status == ApiStatus.OK) {
        setState(() {
          property = value.data!;
          property?.price == 0 ? checkBoxValue = true : checkBoxValue = false;
          priceController.text = property!.price.toString() == "0" ? "" : property!.price.toString();
        });
      }
    });
  }

  getCity() async {
    await API.getCitiesList().then((value) {
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          cities.addAll(value.data!);
        });
    });
  }

  getSociety(val) {
    ProgressHUD.of(context)!.show();
    API.getSocietiesList(cityId: val).then((value) {
      ProgressHUD.of(context)!.dismiss();
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          societies = value.data;
        });
    });
  }

  getBlock(id) {
    ProgressHUD.of(context)!.show();
    final ScaffoldMessengerState scaffoldMessenger = ScaffoldMessenger.of(context);
    API.blocksList(id, cityId: 0, category: type, inActive: "&inactive=1").then((value) {
      ProgressHUD.of(context)!.dismiss();
      if (value.data!.isEmpty && societyId != null)
        return scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text("$type Blocks ${value.status!.message}"),
            action: SnackBarAction(label: 'OK', onPressed: () {}),
          ),
        );
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          blocks = value.data;
        });
    });
  }

  getPlot(blockId) {
    ProgressHUD.of(context)!.show();
    final ScaffoldMessengerState scaffoldMessenger = ScaffoldMessenger.of(context);
    API.getPlotList(blockId).then((value) {
      ProgressHUD.of(context)!.dismiss();
      if (value.data!.isEmpty && blockId != null)
        return scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text("$type Plots ${value.status!.message}"),
            action: SnackBarAction(label: 'OK', onPressed: () {}),
          ),
        );
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          plots = value.data;
        });
    });
  }

  convertToRupee() {
    var length = price.toString().length;
    if (length >= 6 && length <= 7) {
      return (price / 100000).toPrecision(2).toString() + ' Lakh';
    } else if (length >= 8) {
      return (price / 100000).toStringAsFixed(0) + ' Lakh';
    } else if (length >= 4 && length <= 5) {
      return (price / 1000).toPrecision(2).toString() + ' Thousand';
    } else {
      return price;
    }
  }

  Future<PackageInfo?> getAppData() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      uaData = packageInfo;
    });
    return uaData;
  }
  @override
  Widget build(BuildContext context) {
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(title: Text(widget.propertyId == null ? 'Add Market Inventory' : 'Update Market Inventory')),
      body: widget.propertyId != null && property == null
          ? Center(child: Loader())
          : SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: sWidth * .05, ),
          child: FormBuilder(
            key: _fbKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widget.propertyId == null)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Type'),
                      SizedBox(height: 5.0),
                      FormBuilderRadioGroup(
                        activeColor: Theme.of(context).primaryColor,
                        initialValue: typesList[0],
                        name: 'type',
                        onChanged: (dynamic v) {
                          setState(() {
                            type = v;
                            _dropdownBlock.currentState?.clear();
                            blocks = [];
                            blockId = null;
                            if (societyId != null) getBlock(societyId);
                          });
                        },
                        decoration: InputStyle().copyWith(contentPadding: EdgeInsets.symmetric(vertical: 3)),
                        options:
                        typesList.map((e) => FormBuilderFieldOption(value: e, child: Text('$e'))).toList(),
                        validator: FormBuilderValidators.required(errorText: 'Type is required'),
                      ),
                      InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Category'),
                      SizedBox(height: 5.0),
                      FormBuilderDropdown(
                          initialValue:  categories[0],
                          name: 'category',
                          decoration: InputStyle(hint: 'Select category'),
                          onChanged: (dynamic value) {
                            setState(() {
                              categoryValue = value;
                            });
                          },
                          items: categories.map((e) => DropdownMenuItem(value: e, child: Text('$e'))).toList(),
                          validator: FormBuilderValidators.required(errorText: 'Category is required')),
                      InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'City'),
                      SizedBox(height: 5.0),
                      FormBuilderDropdown(
                          initialValue: widget.propertyId != null ? property?.city ?? '' : null,
                          onChanged: (dynamic value) {
                            setState(() {
                              societyId = null;
                              societies = null;
                              _fbKey.currentState!.fields['society_id']!.reset();
                              societies = [];
                              blocks = null;
                              _dropdownBlock.currentState!.clear();
                              blocks = [];
                              plots = null;
                              _dropdownKey.currentState!.clear();
                              plots = [];
                            });
                            cityId = value;
                            getSociety(cityId);
                          },
                          decoration: InputStyle(hint: 'Select city'),
                          name: 'city_id',
                          items: cities.map((e) => DropdownMenuItem(value: e!.id, child: Text('${e.name}'))).toList(),
                          validator: FormBuilderValidators.required(errorText: 'City is required')),
                      InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Society'),
                      SizedBox(height: 5.0),
                      FormBuilderDropdown(
                          initialValue: widget.propertyId != null ? property?.society ?? '' : null,
                          onChanged: (dynamic value) {
                            setState(() {
                              _dropdownBlock.currentState!.clear();
                              blocks = [];
                              _dropdownKey.currentState!.clear();
                              plots = [];
                            });
                            societyId = value;
                            getBlock(societyId);
                          },
                          decoration: InputStyle(hint: 'Select society'),
                          name: 'society_id',
                          items: societies!.map((e) => DropdownMenuItem(value: e!.id, child: Text('${e.name}'))).toList(),
                          validator: FormBuilderValidators.required(errorText: 'Society is required')),
                      InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Block'),
                      SizedBox(height: 5.0),
                      Form(
                        key: key,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            DropdownSearch<BlockEntity?>(
                              // selectedItem: widget.propertyId != null ? property?.block!.name ?? '' : null,
                              enabled: blocks == null || blocks!.isEmpty ? false : blocks!.length > 0,
                              key: _dropdownBlock,
                              popupProps: PopupProps.dialog(
                                searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                                showSearchBox: true,
                              ),
                              dropdownDecoratorProps: DropDownDecoratorProps(
                                dropdownSearchDecoration: InputStyle()
                                    .copyWith(contentPadding: EdgeInsets.only(left: 15, right: 2), hintText: 'Select block'),
                              ),
                              onChanged: (var val) {
                                // blockId = val;
                                setState(() {
                                  _dropdownKey.currentState!.clear();
                                  plots = [];
                                });
                                blockId = val == null ? null : val.id;
                                getPlot(blockId);
                              },
                              items: blocks == null || blocks!.isEmpty ? [] : blocks!,
                              itemAsString: (item) => item == null ? '' : item.name!,
                              validator: (dynamic val) {
                                if (val == null)
                                  return "block is required";
                                else
                                  return null;
                              },
                            ),
                            InputText(
                                screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Plot'),
                            SizedBox(height: 5.0),
                            DropdownSearch<PlotEntity?>(
                              popupProps: PopupProps.dialog(
                                searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                                showSearchBox: true,
                              ),
                              dropdownDecoratorProps: DropDownDecoratorProps(
                                dropdownSearchDecoration: InputStyle()
                                    .copyWith(contentPadding: EdgeInsets.only(left: 15, right: 2), hintText: 'Select plot'),
                              ),
                              // selectedItem: widget.propertyId != null ? plots ?? '' : null,
                              enabled: plots == null || plots!.isEmpty ? false : plots!.length > 0,
                              key: _dropdownKey,
                              itemAsString: (item) => item == null ? '' : item.title,
                              onChanged: (dynamic val) {
                                setState(() {
                                  if(val != null){
                                    plotId = val.id;
                                  }
                                });
                                // if (val != null) {
                                //   String? s = val;
                                //   int? idx = s?.indexOf(" ");
                                //   String? pId = s?.substring(0, idx).trim();
                                //   plotId = plots == null || plots!.isEmpty
                                //       ? null
                                //       : plots?.firstWhere((element) => element!.plotNo == pId)?.id;
                                // }
                              },
                              items: plots == null || plots!.isEmpty
                                  ? []
                                  : plots!,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Price (PKR)'),
                SizedBox(height: 5.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: sWidth * .5,
                      child: FormBuilderTextField(
                        controller: priceController,
                        enabled: !checkBoxValue,
                        name: 'price',
                        onChanged: (value) {
                          if (value != '')
                            setState(() {
                              price = int.parse(value!);
                            });
                        },
                        keyboardType: TextInputType.number,
                        decoration: InputStyle(hint: checkBoxValue ? 'On Call' : 'Full price e.g 27000.'),
                        validator: !checkBoxValue
                            ? FormBuilderValidators.compose([
                          FormBuilderValidators.required(errorText: 'Price is required'),
                          FormBuilderValidators.min(1000, errorText: 'Please enter min price 1000')
                        ])
                            : null,
                      ),
                    ),
                    Container(
                      width: sWidth * .37,
                      child: CheckboxListTile(
                        controlAffinity: ListTileControlAffinity.leading,
                        contentPadding: EdgeInsets.all(0),
                        title: Text(
                          'On Call',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                        activeColor: Theme.of(context).primaryColor,
                        value: checkBoxValue,
                        onChanged: (v) {
                          setState(() {
                            _fbKey.currentState!.fields['price']!.reset();
                            if (widget.propertyId != null)
                              priceController.text = (v! ? '' : property?.price.toString())!;
                            else
                              priceController.text = '';
                            price = 0;
                            checkBoxValue = !checkBoxValue;
                          });
                        },
                      ),
                    ),
                  ],
                ),
                if (price.toString().length > 1 && price != 0)
                  Text('${convertToRupee()}', style: TextStyle(color: Colors.black54, fontSize: 12)),
                SizedBox(height: 5.0),
                Padding(
                  padding: const EdgeInsets.only(bottom: 10),
                  child:
                  InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Extra Info'),
                ),
                SizedBox(height: 5.0),
                FormBuilderTextField(
                  name: 'Extra Info',
                  maxLines: 4,
                  maxLength: 100,
                  keyboardType: TextInputType.text,
                  decoration: InputStyle(
                      hint: "Posession Paid, Utility Paid, Extra Land, With Allotment, Not Paid, With Key, Without Key, Heighted Location, Open File",
                      ),
                ),

                SizedBox(height: 20.0),
                GestureDetector(
                  onTap: () {
                    if (_fbKey.currentState!.saveAndValidate()) {
                      addAction();
                    }
                  },
                  child: CommonButton(
                      screenHeight: sHeight,
                      screenWidth: sWidth,
                      txt: widget.propertyId != null ? 'Update' : 'Submit'),
                ),
                SizedBox(height: 10.0),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> addAction() async {
    Notify.progress(context, 'Please Wait...');
    var data = {
      'app_version': '${uaData?.version}.' + '${uaData?.buildNumber}',
      'city_id': cityId,
      'society_id': societyId,
      'block_id': blockId,
      'plot_id': plotId,
      'type': type.toLowerCase() == 'Residential' ? 0 : 1,
      'category': categoryValue!,
      'price':
      (checkBoxValue || _fbKey.currentState?.value['price'] == null || _fbKey.currentState!.value['price'].isEmpty)
          ? 0
          : _fbKey.currentState!.value['price'],
      'extra_info': _fbKey.currentState!.value['Extra Info'],

    };
    APIResponse<dynamic> result;
      result = await API.addMarketInventory(data);
print(result);
    if (result.status!.status == ApiStatus.OK) {
      Notify.toast(widget.propertyId != null ? "Inventory Updated" : "Inventory Added", type: 1);
      Notify.hide();
      Nav.back(context);
    } else {
      Notify.hide();
      print(result.status!.message);
      Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
    }
    sendAnalyticsEvent(
        eventName: widget.propertyId != null ? 'update_inventory_button_click' : 'add_inventory_button_click');
  }
}

