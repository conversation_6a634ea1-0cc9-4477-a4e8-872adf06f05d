import 'package:flutter/material.dart';

import '../../data/api.dart';
import '../../models/market_inventory_entity.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../../utils/widgets.dart';

class MarketInventoryListPage2 extends StatefulWidget {
  const MarketInventoryListPage2({Key? key}) : super(key: key);

  @override
  State<MarketInventoryListPage2> createState() => _MarketInventoryListPage2State();
}

class _MarketInventoryListPage2State extends BaseState<MarketInventoryListPage2, List<MarketInventoryEntity>> {
  MarketInventoryEntity marketInventory = MarketInventoryEntity();
  @override
  Widget build(BuildContext context) {
    super.build(context);

    return Scaffold(
      body: body(),
    );
  }

  @override
  Widget body() {
   return  SafeArea(
     child: ListView.builder(
         itemBuilder: (context, index){
       MarketInventoryEntity marketInventory = data![index];
       return   Table(
         border: TableBorder.all(color: Colors.green, width: 1.5),
         children:  [
           TableRow(children: [
             Text(marketInventory.price, style: TextStyle(fontSize: 15.0),),
             Text(marketInventory.societyShort, style: TextStyle(fontSize: 15.0),),
             Text(marketInventory.price, style: TextStyle(fontSize: 15.0),),
           ]),

         ],
       );
     }),
   );
  }

  @override
  void getData() {
    @override
    void getData() {
      // API.getMarketInventoryList().then(this.handleResponse);
      sendAnalyticsScreenEvent(screenName: 'MarketInventoryPage');
    }
  }
}
