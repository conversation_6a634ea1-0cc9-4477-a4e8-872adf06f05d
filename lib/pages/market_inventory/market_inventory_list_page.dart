import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:provider/provider.dart';

import '../../controllers/city_selected.dart';
import '../../data/api.dart';
import '../../models/market_inventory_entity.dart';
import '../../models/market_inventory_short_entity.dart';
import '../../nav.dart';
import '../../utils/functions.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/sliver_list_base_state.dart';
import '../../utils/widgets.dart';
import '../../widgets/not_available.dart';
import '../classified_property/filters_provider.dart';
import 'market_inventory_filter.dart';


class MarketInventoryList extends StatefulWidget {
  final int? id;
  final String? title;
  final int? staffId;
  final int? agencyId;
  final String currentAd;

   MarketInventoryList({
     this.staffId, this.agencyId = 0, this.currentAd = "0", this.id = 0,
    this.title,
  });

  @override
  _MarketInventoryListState createState() => _MarketInventoryListState();
}

class _MarketInventoryListState extends SliverListBaseState<MarketInventoryList, List<MarketInventoryShortEntity>> {
  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  @override
  void getData() {
    Map search = context.read<FiltersProvider>().searchMarketInventory;
    if (widget.id == 0 && widget.agencyId == 0 && widget.currentAd == "0"){
      search['city_id'] = search['city_id'] ?? obProvider.city.id;
    }
    if (widget.currentAd != "0") {
      search['ad_id'] = widget.currentAd;
    }
    API.getMarketInventoryList(page: page, staffId: widget.staffId, agencyId: widget.agencyId,extra: search).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'MarketInventoryPage');
  }
  void applyFilters(){
    if(pullToRefresh)
      refreshController.requestRefresh();
  }

  @override
  bool get paginate => true;


  @override
  double get expandedHeight => getMarketInventoryHeight(context);
  @override
  Widget get customAppBar => PreferredSize(
    preferredSize: Size.fromHeight(130),
    child: AppBar(
      elevation: 2,
      centerTitle: true,
      title: Padding(
        padding: const EdgeInsets.only(left: 10.0, right: 10.0),
        child: Row(
          children: [
            Expanded(child: Text("Market Inventory")),
            IconButton(
              onPressed: (){
                Nav.addMarketInventory(context);
                },
              icon: Icon(Icons.library_add),
            ),
          ],
        ),
      ),
      titleSpacing: 0,
      shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
      flexibleSpace: Padding(
        padding: kIsWeb ? EdgeInsets.only(top: 50, left: 10, right: 10, bottom: 5) :
        (Platform.isIOS ? EdgeInsets.only(top: 60, left: 10, right: 10, bottom: 5)
            : EdgeInsets.only(top: MediaQuery.of(context).size.height * .100, left: 10, right: 10, bottom: 5)),
        child: Column(
          children: [
            kIsWeb ? SizedBox(height: 10.0) : (Platform.isIOS ? SizedBox(height: getIosSizedBoxHeight(context)) : SizedBox(height: 10.0)),
            MarketInventoryFilter(callback: applyFilters),
            Padding(
              padding: const EdgeInsets.only(left: 10.0, right: 10.0),
              child: Row(
                children: [
                  Expanded(
                      flex: 3,
                      child: Text("Plot", style: TextStyle(fontSize: 18, color: Colors.white),)),
                  SizedBox(width: 10),
                  Expanded(
                      flex: 4,
                      child: Text("Location", style: TextStyle(fontSize: 18, color: Colors.white),)),
                  SizedBox(width: 10),
                  Expanded(
                      flex: 2,
                      child: Text("Price", style: TextStyle(fontSize: 18, color: Colors.white),)),
                ],
              ),
            ),
          ],
        ),
      ),
    ),
  );

  @override
  Widget listItem(int index) {
    MarketInventoryShortEntity marketInventory = data![index];
    return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 5),
        child: InkWell(
          onTap: (){
            Nav.marketInventoryDetailPage(context, marketInventory.id);
          },
          child: SizedBox(
            height: 45,
            child: TileCard(
              elevation: 2,
              child: Row(
                children: [
                  Expanded(
                      flex: 3,
                      child: Text("Plot-${marketInventory.plotName}".toString(),)),
                  SizedBox(width: 10),
                  Expanded(
                      flex: 4,
                      child: Text("${marketInventory.societyShort}>${marketInventory.blockShort}",)),
                  SizedBox(width: 10),
                  Expanded(
                      flex: 2,
                      child: Text(marketInventory.price,)),
                ],
              ),
            ),
          ),
        ),
        );
  }

  @override
  Widget get notFound => Container(
    alignment: Alignment.center,
    child: NotAvailable(
      'No ad found for your criteria',
      title: 'Not Found',
      buttonText: 'Submit Your ad',
      callback: (BuildContext _context) {
        Nav.addMarketInventory(_context);
      },
    ),
  );
}



