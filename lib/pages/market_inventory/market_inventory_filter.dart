import 'package:flutter/material.dart';
import 'package:find_dropdown/find_dropdown.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/customized_lib/form_builder/find_dropdown_field.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:provider/provider.dart';

import '../../common/main_textField.dart';
import '../../controllers/city_selected.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../utils/base_state.dart';
import '../../utils/widgets.dart';
import '../maps_v2/drop_down_container.dart';

class MarketInventoryFilter extends StatefulWidget {

  VoidCallback callback;
  MarketInventoryFilter({Key? key, required this.callback}) : super(key: key);

  @override
  State<MarketInventoryFilter> createState() => _MarketInventoryFilterState();
}

class _MarketInventoryFilterState extends BaseState<MarketInventoryFilter, List<CityEntity>> {
  final GlobalKey<FindDropdownState> _dpKeySociety = GlobalKey<FindDropdownState>();
  final GlobalKey<FindDropdownState> _dpKeyBlock = GlobalKey<FindDropdownState>();
  final _formKey = GlobalKey<FormBuilderState>();

  List categoryList = ['Residential', 'Commercial'];
  String category = 'Residential';
  @override
  bool get pullToRefresh => false;

  @override
  bool get withScaffold => false;

  @override
  bool get showAd => false;

  @override
  Widget get pageLoader => SizedBox.shrink();

  @override
  void getData() {
    setState(() {
      context.read<FiltersProvider>().city = obProvider.city;
      context.read<FiltersProvider>().societies = obProvider.city.societies;
    });
    API.getCitiesList().then(this.handleResponse);
  }

  List<BlockEntity> blocks = [];

  getBocks() {
    API.blocksList(_formKey.currentState!.fields['society_id']!.value.id, category: category, cityId:_formKey.currentState!.fields['city_id']!.value.id ).then((value) {
      setState(() {
        if (value.data == null) {
          //TODO Toast error
        } else {
          blocks = value.data!;
        }
      });
    });
  }

  @override
  Widget body() {
    final classifiedProvider = context.watch<FiltersProvider>();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 0),
      child: FormBuilder(
        key: _formKey,
        child: Row(
          children: [
            Expanded(
              child: FindDropDownField(
                // key: UniqueKey(),
                name: 'city_id',
                // label: 'City',
                hint: 'Search City',
                initialValue: obProvider.city,
                context: context,
                items: data,
                resetField: 'society_id',
                onChanged: (item) {
                  context.read<FiltersProvider>().citySelected(data!, item!.selectedLabel);
                  widget.callback();
                  setState(() {
                    // _formKey.currentState!.fields['society_id']!.didChange(null);
                    _dpKeySociety.currentState!.setSelectedItem(null);
                    _dpKeyBlock.currentState!.setSelectedItem(null);
                    var city = item as CityEntity;
                    if( city.societies?.length == 1){
                      _formKey.currentState!.fields["society_id"]!.didChange(city.societies!.first);
                      _dpKeySociety.currentState!.setSelectedItem(city.societies!.first);
                    }
                  });
                },
              ),
            ),
            SizedBox(width: 10),
            Expanded(
              child: FindDropDownField(
                // key: UniqueKey(),
                key: _dpKeySociety,
                // selectedItem: null,
                name: 'society_id',
                // label: 'Society',
                hint: 'Search Society',
                context: context,
                items: _formKey.currentState == null ? [] : _formKey.currentState!.fields['city_id']!.value!.societies,
                resetField: 'block_id',
                onChanged: (item) {
                  if (item == null) {
                    blocks = [];
                  } else {
                    context.read<FiltersProvider>().societySelected(item.selectedLabel, category.toLowerCase());
                    widget.callback();
                  }
                  print('Society changed');
                  setState(() {
                    // _formKey.currentState!.fields['block_id']!.didChange(null);
                    _dpKeyBlock.currentState!.setSelectedItem(null);
                    if (item == null)
                      blocks = [];
                    else
                      getBocks();
                  });
                },
              ),
            ),
            SizedBox(width: 10),
            Expanded(
              child: IgnorePointer(
                ignoring:blocks.isEmpty,
                child: FindDropDownField(
                  key: _dpKeyBlock,
                  name: 'block_id',
                  // label: 'Block',
                  hint: 'Search Block',
                  context: context,
                  items: blocks,
                  onChanged: (item) {
                    if (item == null) {
                      blocks = [];
                    } else {
                      context.read<FiltersProvider>().blockSelected(item.dropDownText);
                      widget.callback();
                    }
                    setState(() {
                    });
                    // print('Block changed');
                    //
                    // if (item == null) return;
                    // BlockEntity block = item as BlockEntity;
                    // print(block.name);
                  },
                ),
              ),
            ),

            // ElevatedButton(onPressed: (){
            //   _formKey.currentState!.saveAndValidate();
            //   print(_formKey.currentState!.value);
            // }, child: Text('Click'))
          ],
          // SizedBox(height: 100,),
        ),
      ),
    );
    // return Padding(
    //   padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 0),
    //   child: Padding(
    //     padding: const EdgeInsets.only(bottom: 4),
    //     child: Column(
    //       children: [
    //         Row(
    //           children: [
    //             Expanded(
    //               // flex: 2,
    //               child: FindDropdown<CityEntity?>(
    //                 selectedItem: cityController.city,
    //                 constraints: BoxConstraints.tight(MediaQuery.of(context).size * .5),
    //                 // label: "Select City",
    //                 labelStyle: TextStyle(fontSize: 12, color: Colors.white),
    //                 // labelVisible: false,
    //                 searchBoxDecoration: InputStyle(hint: 'Search Here'),
    //                 items: data!,
    //                 onFind: (value) async {
    //                   if (value.isEmpty) return data!;
    //                   value = value.toLowerCase();
    //                   return data!.where((city) {
    //                     String name = city.name.toLowerCase();
    //                     return name.contains(value);
    //                   }).toList();
    //                 },
    //                 emptyBuilder: (context) {
    //                   return FullLoader();
    //                 },
    //                 dropdownItemBuilder: (BuildContext context, CityEntity? city, bool isSelected) {
    //                   return ListTile(selected: isSelected, title: Text(city!.name));
    //                 },
    //                 dropdownBuilder: (BuildContext context, item) {
    //                   return DropDownContainer(item: item?.name, hint: "Search City");
    //                 },
    //                 onChanged: (v) async {
    //                   context.read<FiltersProvider>().citySelected(data!, v?.name);
    //                   widget.callback();
    //                   setState(() {
    //                     if(classifiedProvider.societies.length == 1){
    //                       classifiedProvider.societySelected(classifiedProvider.societies.first!.name, category);
    //                       _dpKeySociety.currentState?.setSelectedItem(classifiedProvider.societies.first);
    //                     } else{
    //                       _dpKeySociety.currentState?.setSelectedItem(null);
    //                     }
    //                     _dpKeyBlock.currentState?.setSelectedItem(null);
    //                   });
    //                 },
    //               ),
    //             ),
    //             const SizedBox(width: 10),
    //             Expanded(
    //               // flex: 2,
    //               child: FindDropdown<CityEntity?>(
    //                 key: _dpKeySociety,
    //                 constraints: BoxConstraints.tight(MediaQuery.of(context).size * .5),
    //                 // label: "Select Society",
    //                 labelStyle: TextStyle(fontSize: 12, color: Colors.white),
    //                 // labelVisible: false,
    //                 searchBoxDecoration: InputStyle(hint: 'Search Here'),
    //                 items: classifiedProvider.societies,
    //                 onFind: (value) async {
    //                   if (value.isEmpty) return classifiedProvider.societies;
    //                   value = value.toLowerCase();
    //                   return classifiedProvider.societies.where((society) {
    //                     String name = society!.name.toLowerCase();
    //                     return name.contains(value);
    //                   }).toList();
    //                 },
    //                 emptyBuilder: (context) {
    //                   return FullLoader();
    //                 },
    //                 dropdownItemBuilder: (BuildContext context, CityEntity? society, bool isSelected) {
    //                   return ListTile(selected: isSelected, title: Text(society!.name));
    //                 },
    //                 dropdownBuilder: (BuildContext context, item) {
    //                   return DropDownContainer(item: item?.name, hint: "Search Society");
    //                 },
    //                 onChanged: (value) async {
    //                   // ProgressHUD.of(context)!.show();
    //                   context.read<FiltersProvider>().societySelected(value?.name, category.toLowerCase());
    //                   widget.callback();
    //                   // ProgressHUD.of(context)!.dismiss();
    //                   setState(() {
    //                     _dpKeyBlock.currentState?.setSelectedItem(null);
    //                   });
    //                 },
    //               ),
    //             ),
    //             const SizedBox(width: 10),
    //             Expanded(
    //               // flex: 2,
    //               child: IgnorePointer(
    //                 ignoring: classifiedProvider.blocks.isEmpty,
    //                 child: FindDropdown<BlockEntity?>(
    //                   key: _dpKeyBlock,
    //                   constraints: BoxConstraints.tight(MediaQuery.of(context).size * .5),
    //                   // label: "Select Block",
    //                   labelStyle: TextStyle(fontSize: 12, color: Colors.white),
    //                   // labelVisible: false,
    //                   searchBoxDecoration: InputStyle(hint: 'Search Here'),
    //                   items: classifiedProvider.blocks,
    //                   onFind: (value) async {
    //                     if (value.isEmpty) return classifiedProvider.blocks;
    //                     value = value.toLowerCase();
    //                     return classifiedProvider.blocks.where((block) {
    //                       String name = block!.name!.toLowerCase();
    //                       return name.contains(value);
    //                     }).toList();
    //                   },
    //                   emptyBuilder: (context) {
    //                     return FullLoader();
    //                   },
    //                   dropdownItemBuilder: (BuildContext context, BlockEntity? block, bool isSelected) {
    //                     return ListTile(selected: isSelected, title: Text(block!.name!));
    //                   },
    //                   dropdownBuilder: (BuildContext context, item) {
    //                     return DropDownContainer(item: item?.shortName, hint: "Search Block");
    //                   },
    //                   onChanged: (value) async {
    //                     widget.callback();
    //                     context.read<FiltersProvider>().blockSelected(value?.name);
    //                     setState(() {
    //                     });
    //                   },
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //       ],
    //     ),
    //   ),
    // );
  }

}




