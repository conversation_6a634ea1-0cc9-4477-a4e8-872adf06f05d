import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:find_dropdown/find_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sized_context/sized_context.dart';

import '../data/api.dart';
import '../models/city_entity.dart';
import '../nav.dart';
import '../utils/Notify.dart';
import '../utils/functions.dart';

class AddPropertyPage extends StatefulWidget {
  @override
  _AddPropertyPageState createState() => _AddPropertyPageState();
}

class _AddPropertyPageState extends State<AddPropertyPage> {
  CityEntity? city;
  CityEntity? society;
  String? type;

  List<CityEntity>? cities;
  List<CityEntity?>? societies;

  TextEditingController text = TextEditingController();

  bool valid = false;

  @override
  void initState() {
    getCities();
    text.addListener(checkValid);
    sendAnalyticsScreenEvent(screenName: 'AddPropertyPage');
    super.initState();
  }

  @override
  void dispose() {
    text.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Add Inventory'),
      ),
      body: Container(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Column(
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FindDropdown<CityEntity>(
                    label: "City",
                    items: cities,
                    showClearButton: true,
                    onChanged: (CityEntity? _city) {
                      setState(() {
                        city = _city;
                        society = null;
                        societies = _city!.societies;
                      });
//                      getSociety();
                      checkValid();
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FindDropdown<CityEntity?>(
                    label: "Society",
                    showClearButton: true,
                    items: societies,
                    onChanged: (CityEntity? _society) {
                      setState(() {
                        society = _society;
                      });
                      checkValid();
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: FindDropdown(
                    label: "Type",
                    items: ["Requirement", "Plot", "Commercial", "Homes", "Rent", "Villa", "Apartment"],
                    onChanged: (dynamic _type) {
                      setState(() {
                        type = _type;
                      });
                      checkValid();
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    'اپنا نام، نمبر اور آفس کا نام لکھنے کی ضرورت نہیں ہے ',
                    textDirection: TextDirection.rtl,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TextField(
                    controller: text,
                    minLines: 5,
                    maxLines: 10,
                    maxLength: 300,
                    textCapitalization: TextCapitalization.sentences,
                    maxLengthEnforcement: MaxLengthEnforcement.enforced,
                    decoration: InputDecoration(
                      labelText: 'Details',
                      hintText: 'Details of the inventory',
                      border: OutlineInputBorder(),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(18.0),
                  child: Container(
                    width: context.widthPx / 2,
                    height: 40.0,
                    child: ElevatedButton(
                      onPressed: valid ? addAction : null,
                      child: Text('Add'),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  getCities() async {
    APIResponse<dynamic> result = await API.getCitiesList();
    setState(() {
      cities = result.data as List<CityEntity>?;
    });
  }

  getSociety() async {
    APIResponse<dynamic> result = await API.getSocietiesList(cityId: city?.id, all: 1);
    setState(() {
      societies = result.data as List<CityEntity>?;
    });
  }

  void checkValid() {
    bool _valid = true;
    if (city == null) _valid = false;
    if (society == null) _valid = false;
    if (type == null) _valid = false;
    if (text.text.length < 1) _valid = false;
    setState(() {
      valid = _valid;
    });
  }

  Future<void> addAction() async {
    Notify.progress(context, 'Please Wait...');
    var data = {
      'city_id': city?.id.toString(),
      'society_id': society?.id.toString(),
      'type': type!.toLowerCase(),
      'note': text.text.isEmpty ? null : text.text,
    };
    APIResponse<dynamic> result = await API.addProperty(data);

    if (result.status!.status == ApiStatus.OK) {
      Notify.toast("Inventory Added", type: 1);
      Notify.hide();
      Nav.back(context);
    } else {
      Notify.hide();
      Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
    }
    sendAnalyticsEvent(eventName: 'inventory_add_button_click');
  }
}
