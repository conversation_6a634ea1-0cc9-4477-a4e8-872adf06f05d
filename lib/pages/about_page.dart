import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../config.dart';
import '../nav.dart';

class AboutPage extends StatefulWidget {
  @override
  State<AboutPage> createState() => _AboutPageState();
}

class _AboutPageState extends State<AboutPage> {
  PackageInfo? uaData;

  @override
  void initState() {
    getAppData();
    super.initState();
  }

  Future<PackageInfo?> getAppData() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      uaData = packageInfo;
    });
    return uaData;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: GestureDetector(
              onTap: () {
                Nav.back(context);
              },
              child: Container(
                margin: EdgeInsets.only(top: 30),
                child: Icon(Icons.clear, color: Colors.blue, size: 35),
              ),
            ),
          ),
          Align(
            alignment: Alignment.centerRight,
            child: Container(
              child: Text(
                'About',
                textAlign: TextAlign.right,
                style: TextStyle(fontSize: 80, fontWeight: FontWeight.bold, color: Colors.black12),
              ),
            ),
          ),
          SizedBox(
            height: 60,
            child: Stack(
              children: <Widget>[
                Container(height: 60, width: 60, color: Colors.blueAccent),
                Positioned(
                  child: Container(
                      margin: EdgeInsets.only(left: 40, top: 30),
                      child: Text(
                        'About Us.',
                        style: TextStyle(fontWeight: FontWeight.bold, fontSize: 30, color: Colors.black),
                      )),
                )
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(60.0),
            child: Container(
              child: Text(
                Config.ABOUT_TEXT,
                textAlign: TextAlign.justify,
                maxLines: 30,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15, height: 1.5),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(10),
            child: Align(
              alignment: Alignment.center,
              child: Text(
                'Version ${uaData?.version}.' + '${uaData?.buildNumber}',
                textAlign: TextAlign.center,
                style: TextStyle(fontSize: 16, color: Colors.black54),
              ),
            ),
          )
        ],
      ),
    );
  }
}
