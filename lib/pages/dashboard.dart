import 'dart:io';

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_progress_hud/flutter_progress_hud.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:property_dealers_directory/my_app.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/theme.dart';
import 'package:property_dealers_directory/utils/Notify.dart';
import 'package:property_dealers_directory/utils/helpers.dart';
import 'package:property_dealers_directory/utils/text.dart';
import 'package:property_dealers_directory/widgets/custom_ad_banner.dart';
import 'package:provider/provider.dart';
import 'package:get/get.dart';


import '../controllers/admob_controller.dart';
import '../controllers/login_controller.dart';
import '../nav.dart';
import '../pages/classified_property/classified_property_list_page.dart';
import '../pages/maps_v2/MapProvider.dart';
import '../pages/maps_v2/maps_v2.dart';
import '../pages/properties_list/properties_list_page.dart';
import '../services/remote_config.dart';
import '../utils/custom_double_back.dart';
import '../utils/functions.dart';
import '../utils/widgets.dart';
import '../widgets/bottom_nav_bar.dart';
import '../widgets/property_selection_dialog.dart';
import 'home/home_page.dart';
import 'plot_finder/plot_selection.dart';

class DashBoardPage extends StatefulWidget {
  @override
  _DashBoardPageState createState() => _DashBoardPageState();
}

class _DashBoardPageState extends State<DashBoardPage> {
  final LoginController loginController = Get.put<LoginController>(LoginController());


  _subscribeDefaultTopics() {
    if(kIsWeb) return; // topics are not supported on web
    MyApp.fcm.subscribeToTopic('urgent');
    MyApp.fcm.subscribeToTopic('dev');
    MyApp.fcm.subscribeToTopic('campaigns');
    if(loginController.isLoggedIn && loginController.user?.staff?.username == 'ioi'){
      MyApp.fcm.subscribeToTopic('user_ioi');
    }
  }


  PageController _pageController = PageController();
  int _selectedIndex = 0;
  List<Widget> _buildScreens = [
    HomePage(),

    ChangeNotifierProvider(
      key: Key('classifieds'),
          create: (_) => FiltersProvider(),
          child: ClassifiedPropertyListPage(),
      ),
    ChangeNotifierProvider(
      key: Key('properties'),
      create: (_) => FiltersProvider(),
      child: PropertiesListPage(),
    ),
    ProgressHUD(
      barrierEnabled: false,
      child: RemoteConfigService.showMapsV2
          ? ChangeNotifierProvider(create: (_) => MapProvider(), child: MapsV2())
          : PlotSelectionPage(),
    ),
  ];

  void _onPageChanged(int index) {
    print_('------- Changed index: $index');
    if (index == 0) sendAnalyticsEvent(eventName: 'bottom_navigation_home_click');
    if (index == 2) sendAnalyticsEvent(eventName: 'bottom_navigation_inventory_click');
    if (index == 1) sendAnalyticsEvent(eventName: 'bottom_navigation_classifieds_click');
    if (index == 3) sendAnalyticsEvent(eventName: 'bottom_navigation_maps_click');
    setState(() {
      _selectedIndex = index;
    });
    if(adsController.canLoad){
      AdBannerPopup.show(context);
    }

    // if(adsController.canLoad && _isAdLoaded){
    //   ad!.show();
    //   _createInterstitialAd();
    // }

  }

  void _onItemTapped(int selectedIndex) {
    if (selectedIndex == 2 && !loginController.isLoggedIn && RemoteConfigService.isInventoryBehindLogin) {
      showDialog(
          context: context,
          builder: (context) => InventoryLoginAlert());
      return;
    }
    _pageController.jumpToPage(selectedIndex);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: FABBottomAppBar(
        color: Colors.grey,
        backgroundColor: Colors.white,
        selectedColor: Theme.of(context).primaryColor,
        notchedShape: CircularNotchedRectangle(),
        onTabSelected: _onItemTapped,
        items: [
          FABBottomAppBarItem(iconData: Icons.home, text: 'Home'),
          FABBottomAppBarItem(iconData: Icons.playlist_add_check, text: 'Classifieds'),
          FABBottomAppBarItem(iconData: Icons.list_alt, text: 'Inventory'),
          FABBottomAppBarItem(iconData: Icons.map, text: 'Maps'),
        ],
      ),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
      floatingActionButton: FloatingActionButton(
        backgroundColor: Theme.of(context).primaryColor,
        onPressed: () {
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return PropertySelectionDialog(
                fTap: () {
                  sendAnalyticsEvent(eventName: 'home_inventory_post_button_click');
                  Nav.back(context);
                  Nav.addPropertyPage(context);
                },
                sTap: () {
                  sendAnalyticsEvent(eventName: 'home_classified_post_button_click');
                  Nav.back(context);
                  Nav.addClassifiedProperty(context);
                },
                mTap: () {
                  sendAnalyticsEvent(eventName: 'home_market_inventory_post_button_click');
                  Nav.back(context);
                  Nav.addMarketInventory(context);
                },
              );
            },
          );
        },
        child: Icon(Icons.add),
        elevation: 2.0,
      ),
      body: CustomDoubleBack(
        snackBar: SnackBar(
          content: Text("Tap back again to exit....."),
        ),
        preCheck: () {
          if (_selectedIndex != 0) {
            _pageController.jumpToPage(0);
            return false;
          }
          return null;
        },
        child: PageView(
          controller: _pageController,
          onPageChanged: _onPageChanged,
          children: _buildScreens,
          physics: NeverScrollableScrollPhysics(),
        ),
      ),
    );
  }


  AdmobController adsController = Get.find();

  InterstitialAd? ad;
  bool _isAdLoaded = false;

  @override
  void initState() {
    _createInterstitialAd();
    super.initState();
    MyApp.fcm.requestPermission(sound: true, badge: true, alert: true, provisional: false);
    MyApp.fcm.getToken().then((String? token) {
      if (token == null) return;
      loginController.fcm = token;
      loginController.updateToken();
      print("FCM Token: $token");
    });
    if(kIsWeb) return;
    _subscribeDefaultTopics();
  }

  void _createInterstitialAd() {
    if (kIsWeb) return;
    InterstitialAd.load(
        adUnitId:
        Platform.isAndroid ? 'ca-app-pub-8557684672668225/4730283009' : 'ca-app-pub-8557684672668225/3603732074',
        request: AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd interstitialAd) {
            ad = interstitialAd;
            _isAdLoaded = true;
            print('---------------------------- InterstitialAd loaded');
          },
          onAdFailedToLoad: (LoadAdError error) {
            ad = null;
            _isAdLoaded = false;
            print('------------------- InterstitialAd failed to load: $error.');
          },
        ));
  }
}

class InventoryLoginAlert extends StatelessWidget {
  const InventoryLoginAlert({super.key});

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      // title: Text('Login is required'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          RichHeading(
              start: 'Account', middle: ' Login ', end: 'Required'),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'You need an active account to access the inventories, if you already have an account, Please login here.',
              style: TextStyle(fontSize: 14, color: MyColors.primary),
            ),
          ),
          SizedBox(height: 10),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              child: Text('Login'),
              onPressed: () {
                Nav.loginPage(context);
              },
            ),
          ),
          RichHeading(start: 'Get', middle: ' FREE ', end: 'Account'),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'If you do not have an account, you can ask any of our registered agent to create one for you in seconds from their app.',
              style: TextStyle(fontSize: 14, color: MyColors.primary),
            ),
          ),
          SizedBox(height: 10),
          RichHeading(start: 'List', middle: ' Your ', end: 'Agency'),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            child: Text(
              'If you want to list your agency with us, Please submit agency information here.',
              style: TextStyle(fontSize: 14, color: MyColors.primary),
            ),
          ),
          SizedBox(height: 10),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Nav.submitAgency(context);
              },
              child: Text('Add Agency'),
            ),
          ),
        ],
      ),
    );
  }
}

