import 'package:flutter/material.dart';

import '../../data/api.dart';
import '../../models/explore_short_entity.dart';
import '../../utils/sliver_list_base_state.dart';
import 'category_list_item.dart';

class CategoryListPage extends StatefulWidget {
  const CategoryListPage({Key? key}) : super(key: key);

  @override
  _CategoryListPageState createState() => _CategoryListPageState();
}

class _CategoryListPageState extends SliverListBaseState<CategoryListPage, List<ExploreShortCategory>> {
  @override
  String get title => 'Category List';

  @override
  void getData() {
    API.getExploreData("categories").then(this.handleResponse);
  }

  @override
  Widget listItem(index) {
    return CategoryListItem(data![index]);
  }
}
