import 'package:flutter/material.dart';

import '../../models/explore_short_entity.dart';
import '../../nav.dart';
import '../../utils/widgets.dart';

class CategoryListItem extends StatelessWidget {
  final ExploreShortCategory category;

  CategoryListItem(this.category);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Nav.categoryItemListPage(context, category: category);
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: TileCard(
          horizontal: 0,
          vertical: 0,
          elevation: 4,
          child: Row(
            children: <Widget>[
              NetworkImageWithPlaceholder(
                category.image!,
                height: 60,
                width: 60,
                text: category.name,
              ),
              SizedBox(width: 20),
              Text(category.name, style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
            ],
          ),
        ),
      ),
    );
  }
}
