import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../controllers/explore_controller.dart';
import '../../data/api.dart';
import '../../models/explore_short_entity.dart';
import '../../nav.dart';
import '../../utils/Notify.dart';
import '../../utils/base_state.dart';
import '../../widgets/custom_bottom_sheet.dart';
import 'common_category_item.dart';

class CategoryItemListPage extends StatefulWidget {
  final ExploreShortCategory? category;

  const CategoryItemListPage({Key? key, this.category}) : super(key: key);

  @override
  _CategoryItemListPageState createState() => _CategoryItemListPageState();
}

class _CategoryItemListPageState extends BaseState<CategoryItemListPage, List<ExploreShortEntity>> {
  final ExploreController exploreController = Get.find();

  @override
  String get title => widget.category != null ? widget.category!.name : "Explore";

  @override
  get actions => [
        exploreController.locationFilter
            ? _buildIconButton(Icons.location_on, _locationPress)
            : IconButton(icon: Icon(Icons.location_on), onPressed: _locationPress),
        exploreController.categoryFilter
            ? _buildIconButton(Icons.search, () => Nav.categorySearchPage(context, callBack: _pressSearch))
            : IconButton(
                icon: Icon(Icons.search), onPressed: () => Nav.categorySearchPage(context, callBack: _pressSearch)),
      ];

  Padding _buildIconButton(IconData icon, onPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10),
      child: ElevatedButton(
        onPressed: onPressed,
        style: ButtonStyle(
            shape: MaterialStateProperty.all(CircleBorder()), backgroundColor: MaterialStateProperty.all(Colors.white)),
        child: Icon(icon, color: Theme.of(context).primaryColor),
      ),
    );
  }

  _locationPress() {
    return showModalBottomSheet(
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(topRight: Radius.circular(30), topLeft: Radius.circular(30)),
      ),
      context: context,
      builder: (context) {
        return CustomBottomSheet(controller: exploreController, callBack: _pressSearch);
      },
    );
  }

  @override
  void getData() {
    API.getExploreList(/*societyId: society?.id, page: page, query: query*/).then(this.handleResponse);
  }

  _pressSearch() {
    print(exploreController.categories);
    // print(exploreController.cityId);
    // print(exploreController.societyId);
    // print(exploreController.blockId);
    // print(exploreController.query);
    data = null;
    setState(() {
      loading = true;
      page = 1;
    });
    Notify.showSnack(context, 'Searching...');
    getData();
  }

  @override
  Widget body() {
    return GridView.builder(
      shrinkWrap: true,
      itemCount: data!.length,
      physics: NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 2, childAspectRatio: 0.76),
      itemBuilder: (context, index) {
        ExploreShortEntity item = data![index];
        return CommonCategoryItem(item: item);
      },
    );
  }
}

// @override
// get actions => [
//   Stack(
//     alignment: Alignment.center,
//     children: [
//       exploreController.locationFilter
//           ? _buildIcon(Icons.location_on_outlined, 30, Colors.green, _locationPress)
//           : SizedBox.shrink(),
//       _buildIcon(Icons.location_on, 24, Colors.white, _locationPress),
//     ],
//   ),
//   Stack(
//     alignment: Alignment.center,
//     children: [
//       exploreController.categoryFilter
//           ? _buildIcon(Icons.search_outlined, 30, Colors.green,
//               () => Nav.categorySearchPage(context, callBack: _pressSearch))
//           : SizedBox.shrink(),
//       _buildIcon(Icons.search, 24, Colors.white, () => Nav.categorySearchPage(context, callBack: _pressSearch)),
//     ],
//   ),
// ];
//
// Widget _buildIcon(IconData icon, double size, Color color, onPressed) {
//   return IconButton(icon: Icon(icon, color: color, size: size), onPressed: onPressed);
// }
