import 'package:flutter/material.dart';

import '../../models/explore_short_entity.dart';
import '../../nav.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class CommonCategoryItem extends StatelessWidget {
  final ExploreShortEntity item;

  const CommonCategoryItem({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(10),
      child: TileCard(
        horizontal: 0,
        vertical: 0,
        elevation: 2,
        onTap: () => Nav.categoryItemDetailPage(context, item.id, item.category.image!),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            NetworkImageWithPlaceholder(item.businessLogo!, height: 150, width: 200),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Heading(item.category.name, font: 12),
                  Heading.big(item.businessName, font: 18),
                  if (item.block != null) SizedBox(height: 5),
                  if (item.block != null)
                    FittedBox(child: Text(item.block!, style: TextStyle(color: Colors.black54, fontSize: 12))),
                  SizedBox(height: 5),
                  FittedBox(
                    child: Text(
                      "${item.society}, ${item.city}",
                      style: TextStyle(color: Colors.black54, fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
