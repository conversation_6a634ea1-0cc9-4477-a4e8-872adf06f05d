import 'package:flutter/material.dart';
import 'package:flutter_multi_select_items/flutter_multi_select_items.dart';

import '../../models/explore_short_entity.dart';

class CommonMultiSelectContainer extends StatelessWidget {
  final List<ExploreShortCategory> selected;
  final List<ExploreShortCategory> list;
  final void Function(List<ExploreShortCategory>, ExploreShortCategory) onChanged;

  const CommonMultiSelectContainer({Key? key, this.selected = const [], required this.list, required this.onChanged})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiSelectContainer<ExploreShortCategory>(
      itemsDecoration: MultiSelectDecorations(
        decoration: BoxDecoration(color: Colors.grey.shade300, borderRadius: BorderRadius.circular(50.0)),
      ),
      prefix: MultiSelectPrefix(
        selectedPrefix:
            const Padding(padding: EdgeInsets.only(right: 5), child: Icon(Icons.check, color: Colors.white, size: 14)),
      ),
      items: [
        for (ExploreShortCategory item in list)
          MultiSelectCard(selected: selected.any((element) => item.id == element.id), value: item, label: item.name),
      ],
      onChange: onChanged,
    );
  }
}
