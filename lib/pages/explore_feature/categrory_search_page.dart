import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

import '../../common/main_textField.dart';
import '../../controllers/explore_controller.dart';
import '../../data/api.dart';
import '../../models/explore_data_entity.dart';
import '../../models/explore_short_entity.dart';
import '../../nav.dart';
import '../../utils/base_state.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import 'common_multiselect_container.dart';

class CategorySearchPage extends StatefulWidget {
  final Function callBack;

  const CategorySearchPage({Key? key, required this.callBack}) : super(key: key);

  @override
  State<CategorySearchPage> createState() => _CategorySearchPageState();
}

class _CategorySearchPageState extends BaseState<CategorySearchPage, ExploreDataEntity> {
  final GlobalKey<FormBuilderFieldState> _fbKey = GlobalKey<FormBuilderFieldState>();
  final ExploreController exploreController = Get.find();
  List<ExploreShortCategory>? servicesList;
  List<ExploreShortCategory>? featuresList;

  @override
  bool get withAppBar => false;

  @override
  bool get pullToRefresh => false;

  @override
  Widget? get fab => PrimaryButton(
      text: 'Apply Filter',
      onPressed: () {
        _fbKey.currentState?.save();
        exploreController.query = _fbKey.currentState?.value;
        Nav.back(context);
        widget.callBack();
      });

  @override
  void getData() {
    API.getAllExploreData().then(this.handleResponse);
  }

  @override
  Widget body() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 20),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              IconButton(onPressed: () => Nav.back(context), icon: Icon(Icons.arrow_back)),
              Spacer(),
              Heading.big("Filtering", font: 22),
              Spacer(),
              Spacer()
            ],
          ),
          SizedBox(height: 20),
          FormBuilderTextField(
            key: _fbKey,
            textInputAction: TextInputAction.search,
            name: 'query',
            decoration: InputStyle(hint: 'Enter query here'),
            onSubmitted: (value) {},
          ),
          SizedBox(height: 20),
          Heading.big("Categories", font: 20),
          SizedBox(height: 10),
          CommonMultiSelectContainer(
            list: data!.categories,
            selected: exploreController.categories,
            onChanged: (allSelectedItems, selectedItem) {
              exploreController.addExploreData(selectedItem, "c");
              exploreController.removeExploreData(allSelectedItems, "c");
            },
          ),
          SizedBox(height: 20),
          Heading.big("Services", font: 20),
          SizedBox(height: 10),
          CommonMultiSelectContainer(
            list: data!.services,
            selected: exploreController.services,
            onChanged: (allSelectedItems, selectedItem) {
              exploreController.removeExploreData(allSelectedItems, "s");
              exploreController.addExploreData(selectedItem, "s");
            },
          ),
          SizedBox(height: 20),
          Heading.big("Features", font: 20),
          SizedBox(height: 10),
          CommonMultiSelectContainer(
            list: data!.features,
            selected: exploreController.features,
            onChanged: (allSelectedItems, selectedItem) {
              exploreController.removeExploreData(allSelectedItems, "f");
              exploreController.addExploreData(selectedItem, "f");
            },
          ),
        ],
      ),
    );
  }
}
