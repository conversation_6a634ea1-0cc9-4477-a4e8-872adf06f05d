import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:latlong2/latlong.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:property_dealers_directory/models/explore_entity.dart';
import 'package:property_dealers_directory/pages/maps_v2/maps_launcher_custom.dart';
import 'package:readmore/readmore.dart';

import '../../data/api.dart';
import '../../models/classified_entity.dart';
import '../../nav.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import '../../widgets/social.dart';
import '../maps_v2/cached_network_tile_provider.dart';

class CategoryItemDetailPage extends StatefulWidget {
  final int propertyId;
  final String logo;
  final int? isHomeClick;

  const CategoryItemDetailPage(this.propertyId, this.logo, {this.isHomeClick});

  @override
  _CategoryItemDetailPageState createState() => _CategoryItemDetailPageState();
}

class _CategoryItemDetailPageState extends BaseState<CategoryItemDetailPage, ExploreEntity> {
  @override
  Widget get fab => data != null
      ? FloatingActionButton(
          backgroundColor: Theme.of(context).primaryColor,
          child: Icon(Icons.share),
          onPressed: () {},
        )
      : Container();

  @override
  bool get withAppBar => false;

  @override
  void getData() {
    API.getExploreDetail(widget.propertyId).then(this.handleResponse);
  }

  @override
  Widget body() {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return SafeArea(
      child: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CarouselSlider(
                  options: CarouselOptions(
                    height: MediaQuery.of(context).size.height * .35,
                    viewportFraction: 1,
                    autoPlay: true,
                    autoPlayInterval: Duration(seconds: 3),
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.fastOutSlowIn,
                  ),
                  items: data!.collections![0].images!.map((i) {
                    return Builder(
                      builder: (BuildContext context) {
                        return GestureDetector(
                            child: NetworkImageWithPlaceholder(i, width: MediaQuery.of(context).size.width * 1));
                      },
                    );
                  }).toList(),
                ),
                SizedBox(height: screenHeight * .08),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: screenWidth * .01, vertical: screenHeight * .01),
                  child: Container(
                      child: Center(
                          child:
                              FittedBox(child: Heading.big('${data!.category.name}', font: 20, color: Colors.black)))),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Heading.big(data!.businessName, color: Colors.black, font: 20),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: TileCard(
                    vertical: 5,
                    horizontal: 5,
                    elevation: 2,
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Row(
                        children: [
                          Icon(Icons.location_on),
                          SizedBox(width: 10),
                          LimitedBox(maxWidth: MediaQuery.of(context).size.width * .77, child: Text(data!.address)),
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 10),
                    child: TileCard(
                        elevation: 4,
                        vertical: 10,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Heading.big("Description:", font: 20),
                            SizedBox(height: 5),
                            ReadMoreText(data!.businessDescription!,
                                trimLength: 200, style: TextStyle(color: Colors.black)),
                          ],
                        ))),
                SizedBox(height: 5),
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: Heading.big("Photos:", font: 20),
                ),
                Container(
                  height: 140,
                  child: ListView.builder(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemCount: data!.collections!.length,
                    itemBuilder: (context, i) {
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: GestureDetector(
                          onTap: () {
                            // Nav.viewClassifiedGallery(context, data!.collections, data!.collections);
                            sendAnalyticsEvent(
                                eventName: 'detail_classified_open_gallery_click',
                                parameters: {'classified_id': widget.propertyId});
                          },
                          child: Hero(
                            tag: '${data!.collections![i].images![i]}',
                            child:
                                NetworkImageWithPlaceholder(data!.collections![i].images![i], width: 140, radius: 14),
                          ),
                        ),
                      );
                    },
                  ),
                ),
                if (data!.features!.length > 0)
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 5),
                      Padding(
                        padding: const EdgeInsets.all(10),
                        child: Heading.big('Property Features:', font: 20),
                      ),
                      for (int i = 0; i < data!.features!.length; i++)
                        Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Row(
                            children: [
                              Icon(Icons.check_box_outlined, color: Theme.of(context).primaryColor),
                              SizedBox(width: 5),
                              Text(
                                "${data!.features![i].name}",
                              )
                            ],
                          ),
                        ),
                    ],
                  ),
                // if (data!.plot != null)
                //   Column(
                //     crossAxisAlignment: CrossAxisAlignment.start,
                //     children: [
                //       SizedBox(height: 5),
                //       Padding(
                //         padding: const EdgeInsets.all(10),
                //         child: Heading.big('Location:', font: 20),
                //       ),
                //       Padding(
                //         padding: const EdgeInsets.symmetric(horizontal: 2.0),
                //         child: Container(
                //           height: 200,
                //           child: ClipRRect(
                //             borderRadius: BorderRadius.circular(10.0),
                //             child: LocationDetailTab(data!),
                //           ),
                //         ),
                //       ),
                //       SizedBox(height: 10),
                //     ],
                //   ),
                SizedBox(height: 80)
              ],
            ),
            Positioned(
              left: 10,
              top: 10,
              child: Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Colors.white),
                child: IconButton(
                    onPressed: () => Nav.back(context),
                    icon: Icon(
                      Icons.arrow_back,
                    )),
              ),
            ),
            // Positioned(
            //   right: 10,
            //   top: 10,
            //   child: Column(
            //     crossAxisAlignment: CrossAxisAlignment.end,
            //     children: [
            //       Container(
            //         height: 30,
            //         width: 80,
            //         decoration:
            //             BoxDecoration(borderRadius: BorderRadius.circular(20), color: Theme.of(context).primaryColor),
            //         child: Center(
            //             child: Text(
            //           'For ${data!.purpose}',
            //           style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            //         )),
            //       ),
            //       SizedBox(height: 5),
            //       Text(data!.price != '0' ? data!.price! : 'Call for price',
            //           style: TextStyle(
            //               shadows: [Shadow(color: Colors.black, blurRadius: 6.0)],
            //               color: Colors.white,
            //               fontSize: 17,
            //               fontWeight: FontWeight.bold)),
            //       SizedBox(height: 5),
            //       Text('${data!.type} ${data!.category}',
            //           style: TextStyle(
            //               shadows: [Shadow(color: Colors.black, blurRadius: 6.0)],
            //               color: Colors.white,
            //               fontSize: 17,
            //               fontWeight: FontWeight.bold)),
            //     ],
            //   ),
            // ),
            Positioned.fill(
              top: screenHeight * .29,
              child: Align(
                  alignment: Alignment.topCenter,
                  child: NetworkImageWithPlaceholder(data!.category.image!, circular: true)),
            ),
            Positioned(
                left: screenWidth * .18,
                top: screenHeight * .36,
                child: Container(
                    height: 42,
                    width: 42,
                    child: FloatingActionButton(
                      elevation: 0,
                      backgroundColor: Theme.of(context).primaryColor,
                      child: Icon(Icons.phone),
                      onPressed: () {},
                    ))),
            Positioned(
              right: screenWidth * .18,
              top: screenHeight * .36,
              child: SocialIcon.type('', SocialIcon.WHATSAPP, onPressed: () {}),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget get pageLoader => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Hero(
              tag: widget.logo,
              child: NetworkImageWithPlaceholder(widget.logo, circular: true, height: 100, width: 100)),
          FullLoader(),
        ],
      );
}

class LocationDetailTab extends StatelessWidget {
  final ClassifiedEntity data;

  LocationDetailTab(this.data);

  @override
  Widget build(BuildContext context) {
    final LatLng _center = LatLng(data.plot!.lat!, data.plot!.long!);

    return Stack(
      children: <Widget>[
        Container(
          child: FlutterMap(
            options: MapOptions(
              initialCenter: _center,
              initialZoom: 17.0,
              // center: _center,
              // zoom: 17.0,
              minZoom: data.block!.minZoom.toDouble(),
              maxZoom: data.block!.maxZoom.toDouble(),
            ),
            children: [
              TileLayer(
                urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                subdomains: ['a', 'b', 'c'],
                tileProvider: CachedNetworkTileProvider(),
              ),
              TileLayer(
                urlTemplate: "${data.block!.mapUrl}/{z}/{x}/{y}.png",
                subdomains: ['a', 'b', 'c'],
                tileProvider: CachedNetworkTileProvider(),
                // backgroundColor: Colors.white.withOpacity(0),
                minZoom: data.block!.minZoom.toDouble(),
                maxZoom: data.block!.maxZoom.toDouble(),
                // opacity: 1,
                tms: false,
              ),
              MarkerLayer(markers: [
                Marker(
                  point: _center,
                  // anchorPos: AnchorPos.align(AnchorAlign.top),
                  child: Icon(
                    Icons.location_on,
                    color: Colors.blue,
                    size: 30.0,
                  ),
                )
              ])
            ],
          ),
        ),
        Positioned(
          right: 0,
          bottom: 0,
          child: Container(
            padding: EdgeInsets.all(8.0),
            child: FloatingActionButton(
              mini: true,
              backgroundColor: Theme.of(context).primaryColor,
              child: Icon(FontAwesomeIcons.diamondTurnRight),
              onPressed: () {
                // MapsLauncher.launchCoordinates(_center.latitude, _center.longitude);
                MapsLauncherCustom.launchCoordinates(_center.latitude, _center.longitude);
                sendAnalyticsEvent(
                    eventName: 'detail_classified_google_map_click', parameters: {'classified_id': data.id});
              },
            ),
          ),
        )
      ],
    );
  }
}
