import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../data/api.dart';
import '../../models/explore_short_entity.dart';
import '../../nav.dart';
import '../../utils/base_state.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import 'common_category_item.dart';

class ExploreMainPage extends StatefulWidget {
  const ExploreMainPage({Key? key}) : super(key: key);

  @override
  _ExploreMainPageState createState() => _ExploreMainPageState();
}

class _ExploreMainPageState extends BaseState<ExploreMainPage, List<ExploreShortCategory>> {
  List<ExploreShortEntity?> popularBusinessList = [];

  @override
  String get title => "Explore";

  @override
  bool get pullToRefresh => false;

  @override
  void getData() {
    API.getExploreData("categories").then(this.handleResponse);
    API.getExploreList().then((value) {
      setState(() {
        popularBusinessList = value.data!;
      });
    });
  }

  @override
  Widget body() {
    return Column(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              child: Heading('Categories', font: 20.0, weight: FontWeight.bold),
            ),
            GridView.builder(
              shrinkWrap: true,
              itemCount: 8,
              physics: NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 4),
              itemBuilder: (context, i) {
                var category = data![i];
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: GestureDetector(
                    onTap: () {
                      if (i == 7)
                        Nav.categoryListPage(context);
                      else
                        Nav.categoryItemListPage(context, category: category);
                    },
                    child: Column(
                      children: [
                        TileCard(
                          horizontal: 0,
                          vertical: 0,
                          elevation: 3,
                          child: Container(
                            height: 45,
                            width: 45,
                            color: Theme.of(context).primaryColor,
                            child: i == 7
                                ? Icon(Icons.more_horiz, color: Colors.white)
                                : Image.network(category.image!, fit: BoxFit.cover),
                          ),
                        ),
                        SizedBox(height: 10),
                        FittedBox(child: Text(i == 7 ? "More" : category.name)),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ),
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Heading('Popular Business', font: 20.0, weight: FontWeight.bold),
                  GestureDetector(
                    onTap: () {
                      Nav.categoryItemListPage(context);
                    },
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                      decoration:
                          BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5)),
                      child: Text("See all", style: TextStyle(color: Colors.white)),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 10),
            CarouselSlider(
              options: CarouselOptions(
                height: 235,
                viewportFraction: getClassifiedFraction(context),
                autoPlay: true,
                autoPlayInterval: Duration(seconds: 3),
                autoPlayAnimationDuration: Duration(milliseconds: 800),
                autoPlayCurve: Curves.fastOutSlowIn,
                enlargeCenterPage: false,
                scrollDirection: Axis.horizontal,
              ),
              items: popularBusinessList.map((item) {
                return CommonCategoryItem(item: item!);
              }).toList(),
            ),
          ],
        ),
      ],
    );
  }
}
