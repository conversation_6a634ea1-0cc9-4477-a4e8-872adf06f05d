import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

import '../config.dart';
import '../data/api.dart';
import '../models/video_entity.dart';
import '../nav.dart';
import '../services/remote_config.dart';
import '../utils/functions.dart';
import '../utils/helpers.dart';
import '../utils/sliver_list_base_state.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class VideoListPage extends StatefulWidget {
  @override
  _VideoListPageState createState() => _VideoListPageState();
}

class _VideoListPageState extends SliverListBaseState<VideoListPage, List<VideoEntity?>> {
  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  String query = '';

  @override
  Widget get fab => ElevatedButton(
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Need more help'),
            <PERSON><PERSON><PERSON><PERSON>(width: 5),
            Icon(Icons.phone_enabled),
          ],
        ),
        style: ButtonStyle(
            backgroundColor: MaterialStateProperty.all<Color>(Theme.of(context).primaryColor),
            textStyle: MaterialStateProperty.all(TextStyle(color: Colors.white))),
        onPressed: () {
          openLink('tel:' + RemoteConfigService.contactPhone);
        },
      );

  @override
  bool get paginate => true;

  @override
  double get expandedHeight => 120;

  @override
  bool get withAppBar => false;

  @override
  String get title {
    if (query.isEmpty)
      return 'How to';
    else
      return 'Search results for "$query"';
  }

  void searchPressed() {
    _fbKey.currentState!.save();
    query = _fbKey.currentState!.value['query'];
    data = null;
    setState(() {
      this.loading = true;
    });
    getData();
    sendAnalyticsEvent(eventName: 'list_how_to_search_button_click');
  }

  @override
  Widget get customAppBar => PreferredSize(
        preferredSize: Size.fromHeight(110),
        child: AppBar(
            elevation: 2,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
            title: Text(title),
            flexibleSpace: Padding(
              padding: const EdgeInsets.only(top: 83, left: 20, right: 20, bottom: 10),
              child: FormBuilder(
                key: _fbKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: <Widget>[
                        Container(
                          height: 42,
                          width: MediaQuery.of(context).size.width * .888,
                          padding: EdgeInsets.symmetric(horizontal: 5.0),
                          decoration: BoxDecoration(
                              color: Colors.white,
                              border: Border.all(
                                color: Colors.grey[400]!,
                                width: 0.8,
                              ),
                              borderRadius: BorderRadius.circular(5.0)),
                          child: Row(
                            children: [
                              Expanded(
                                child: FormBuilderTextField(
                                    textInputAction: TextInputAction.search,
                                    initialValue: '',
                                    name: 'query',
                                    decoration: InputDecoration(hintText: 'Search How to', border: InputBorder.none),
                                    onSubmitted: (value) {
                                      searchPressed();
                                    }),
                              ),
                              InkWell(
                                splashColor: Colors.transparent,
                                highlightColor: Colors.transparent,
                                onTap: searchPressed,
                                child: Container(
                                  padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                                  decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5.0)),
                                  child: Icon(
                                    Icons.search,
                                    size: 18.0,
                                    color: Colors.white,
                                  ),
                                ),
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            )),
      );

  @override
  void getData() {
    API.getVideos(query).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'HowToListPage');
  }

  @override
  Widget listItem(index) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 5),
        child: GestureDetector(
            onTap: () {
              Nav.videoPlayPage(context, data![index]);
              sendAnalyticsEvent(eventName: 'drawer_how_to_video_click', parameters: {'video_id': data![index]!.id});
            },
            child: TileCard(vertical: 20, elevation: 2, child: Heading(data![index]!.title, weight: FontWeight.w600))),
      ),
    );
  }
}
