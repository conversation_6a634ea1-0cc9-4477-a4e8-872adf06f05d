import 'dart:io';

import 'package:dropdown_search/dropdown_search.dart';
import 'package:find_dropdown/find_dropdown.dart';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import '../customized_lib/form_builder/find_dropdown_field.dart';


import '../common/main_textField.dart';
import '../data/api.dart';
import '../models/agency_short_entity.dart';
import '../models/society_entity.dart';
import '../providers/onboarding_provider.dart';
import '../theme.dart';
import '../utils/functions.dart';
import '../utils/height_fraction_page.dart';
import '../utils/sliver_list_base_state.dart';
import '../widgets/agency_list.dart';

class AgencyListPage extends StatefulWidget {
  final String? query;

  AgencyListPage({this.query});

  @override
  _AgencyListPageState createState() => _AgencyListPageState();
}

class _AgencyListPageState extends SliverListBaseState<AgencyListPage, List<AgencyShortEntity>> {
  SocietyEntity? society;
  String query = '';
  List<SocietyEntity> societies = [];

  @override
  Widget listItem(index) {
    return AgencyListItem(data![index]);
  }

  @override
  String get title {
    if (query.isEmpty) if (society == null)
      return 'All Agencies';
    else
      return 'Agencies in ${society!.name}';
    else
      return 'Search results for "$query"';
  }

  @override
  String get errorMessage => 'Currently there is no data available in this location.';

  @override
  void initState() {
    // society = obProvider.onboarding.society;
    API.getSocietiesListWithCity().then((value) {
      if (value.status!.status == ApiStatus.OK) {
        setState(() {
          societies = value.data!;
        });
      }
    });
    super.initState();
  }

  @override
  void getData() {
    API.getAgencyList(societyId: society?.id, page: page, query: query).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'AgencyListPage');
  }

  @override
  bool get paginate => true;

  @override
  bool get showAd => true;

  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();
  //
  @override
  double get expandedHeight => kIsWeb ? 120 : Platform.isIOS ? getAgencyHeight(context) : 110;

  @override
  bool get withAppBar => false;
  Widget get customAppBar => Container(
    decoration: BoxDecoration(
      color: MyColors.primary,
      borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
    ),
    child: Column(
      children: [
        AppBar(
          title: Text(title),
          elevation: 1,
        ),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: FormBuilder(
            key: _fbKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: <Widget>[
                    Expanded(
                      flex: 2,
                      child: FindDropDownField(
                      // key: UniqueKey(),
                      // initialValue: obProvider.onboarding.society,
                      // selectedItem: null,
                      name: 'society_id',
                      // label: 'Society',
                      hint: 'Search Society',
                      context: context,
                      items: societies,
                      resetField: 'query',
                      onChanged: (item) {
                        setState(() {
                          this.loading = true;
                          society = item as SocietyEntity?;
                          query = '';
                          _fbKey.currentState!.fields['query']!.didChange('');
                          data = null;
                        });
                        getData();

                      },
                    ),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      flex: 3,
                      child: Container(
                        height: 30,
                        padding: EdgeInsets.symmetric(horizontal: 5.0),
                        decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: Colors.grey[400]!,
                              width: 0.8,
                            ),
                            borderRadius: BorderRadius.circular(5.0)),
                        child: Row(
                          children: [
                            Expanded(
                              child: FormBuilderTextField(
                                  textInputAction: TextInputAction.search,
                                  initialValue: '',
                                  name: 'query',
                                  decoration:
                                  InputDecoration(hintText: 'Search agencies', border: InputBorder.none),
                                  onSubmitted: (_) {
                                    searchPressed();
                                  }),
                            ),
                            InkWell(
                              splashColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: searchPressed,
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                                decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    borderRadius: BorderRadius.circular(5.0)),
                                child: Icon(
                                  Icons.search,
                                  size: 18.0,
                                  color: Colors.white,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        )
      ],
    ),
  );

  void searchPressed() {
    _fbKey.currentState!.save();
    query = _fbKey.currentState!.value['query'];
    data = null;
    setState(() {
      this.loading = true;
    });
    getData();
    sendAnalyticsEvent(eventName: 'list_agency_search_button_click');
  }
}
