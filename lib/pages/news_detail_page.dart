import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:url_launcher/url_launcher.dart';

import '../data/api.dart';
import '../models/blog_entity.dart';
import '../nav.dart';
import '../utils/base_state.dart';
import '../utils/widgets.dart';

class NewsDetailPage extends StatefulWidget {
  final String id;
  final String banner;

  const NewsDetailPage({required this.id, required this.banner});

  @override
  _NewsDetailPageState createState() => _NewsDetailPageState();
}

class _NewsDetailPageState extends BaseState<NewsDetailPage, BlogEntity> {
  @override
  bool get withAppBar => false;

  @override
  void getData() {
    API.getNewsDetail(widget.id).then(this.handleResponse);
  }

  @override
  Widget get pageLoader => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Hero(
              tag: widget.banner,
              child: NetworkImageWithPlaceholder(widget.banner, circular: true, height: 100, width: 100)),
          FullLoader(),
        ],
      );

  @override
  Widget body() {
    Size size = MediaQuery.of(context).size;
    return SingleChildScrollView(
      child: Column(
        children: [
          Stack(
            children: [
              Hero(
                tag: data!.banner,
                child: NetworkImageWithPlaceholder(
                  data!.banner,
                  height: size.height * .4,
                  width: size.width,
                  text: data!.title,
                  cover: true,
                ),
              ),
              Positioned(
                left: size.width * .03,
                top: size.height * .05,
                child: Container(
                  height: 40,
                  width: 40,
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Colors.white),
                  child: IconButton(
                      onPressed: () => Nav.back(context),
                      icon: Icon(
                        Icons.arrow_back,
                      )),
                ),
              ),
              Positioned(
                bottom: 0,
                child: Container(
                    width: size.width,
                    color: Colors.black54,
                    child: Column(
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Text(
                            '${data!.title}',
                            textAlign: TextAlign.left,
                            style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18.0),
                          ),
                        ),
                      ],
                    )),
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: size.width * .05, vertical: size.height * .03),
            child: HtmlWidget(data!.body, onTapUrl: (url) {
              return launch(url);
            }),
          )
        ],
      ),
    );
  }
}
