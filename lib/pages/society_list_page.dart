import 'package:flutter/material.dart';

import '../data/api.dart';
import '../models/society_entity.dart';
import '../nav.dart';
import '../utils/base_state.dart';
import '../utils/functions.dart';
import '../utils/widgets.dart';

class SocietyListPage extends StatefulWidget {
  SocietyListPage();

  @override
  _SocietyListPageState createState() => _SocietyListPageState();
}

class _SocietyListPageState extends BaseState<SocietyListPage, List<SocietyEntity?>> {
  @override
  void getData() {
    API.getSocietiesListWithCity().then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'SocietyListPage');
  }

  @override
  bool get paginate => false;

  @override
  Widget body() {
    return ListView.builder(
        itemCount: data!.length + 1,
        controller: scrollController,
        physics: ClampingScrollPhysics(),
        itemBuilder: (context, index) {
          if (index == data!.length)
            return paginateLoader();
          else
            return SocietyListItem(data![index]);
        });
  }

  @override
  String get title => 'Rates List';
}

class SocietyListItem extends StatelessWidget {
  final SocietyEntity? society;

  SocietyListItem(this.society);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Nav.rateList(context, society!.id, society!.title);
        sendAnalyticsEvent(eventName: 'drawer_ratesList_society_item_click', parameters: {'society_id': society!.id});
      },
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: TileCard(
          elevation: 2,
          child: Container(
            width: (MediaQuery.of(context).size.width - 150) / 3,
            child: ListTile(
              leading: NetworkImageWithPlaceholder(
                society!.image,
                height: 50.0,
                width: 50.0,
                circular: true,
                text: society!.name,
              ),
              title: Text(
                society!.name,
                maxLines: 2,
                textAlign: TextAlign.left,
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              subtitle: Text(
                society!.cityName,
                maxLines: 2,
                textAlign: TextAlign.left,
                style: TextStyle(fontSize: 16),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
