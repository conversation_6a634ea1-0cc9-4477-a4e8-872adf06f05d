import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import '../common/common_button.dart';
import '../common/main_textField.dart';
import '../controllers/login_controller.dart';
import '../data/api.dart';
import '../nav.dart';
import '../utils/Notify.dart';
import '../utils/functions.dart';
import '../utils/text.dart';

class ResetPasswordPage extends StatefulWidget {
  final LoginController controller = Get.find();

  @override
  _ResetPasswordPageState createState() => _ResetPasswordPageState();
}

class _ResetPasswordPageState extends State<ResetPasswordPage> {
  final _fbKey = GlobalKey<FormBuilderState>();

  @override
  void initState() {
    sendAnalyticsScreenEvent(screenName: 'ResetPasswordPage');
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      body: SingleChildScrollView(
        child: FormBuilder(
          key: _fbKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: screenWidth * 0.05, top: screenHeight * 0.06),
                child: GestureDetector(
                  onTap: () {
                    Nav.back(context);
                  },
                  child: Icon(Icons.close, color: Theme.of(context).primaryColor.withOpacity(0.7), size: 40),
                ),
              ),
              HeadingText(screenWidth: screenWidth, screenHeight: screenHeight, txt: "Reset Password"),
              SubHeadingText(
                screenWidth: screenWidth,
                screenHeight: screenHeight,
                txt: "Request new password on your phone number.",
              ),
              SizedBox(height: screenHeight * 0.04),
              InputText(screenWidth: screenWidth, screenHeight: screenHeight, txt: "Phone"),
              MainTextFieldPadding(
                screenWidth: screenWidth,
                screenHeight: screenHeight,
                textField: FormBuilderTextField(
                  name: 'username',
                  decoration: InputStyle(icon: Icons.person, hint: "Enter phone number"),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(errorText: 'Phone is required'),
                  ]),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.07),
                child: GestureDetector(
                  onTap: () {
                    if (_fbKey.currentState!.saveAndValidate()) {
                      print(_fbKey.currentState!.value);
                      _submitClick();
                      sendAnalyticsEvent(eventName: 'reset_password_button_click');
                    }
                  },
                  child: CommonButton(screenWidth: screenWidth, screenHeight: screenHeight, txt: "Reset Password"),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _submitClick() async {
    if (_fbKey.currentState!.saveAndValidate()) {
      Notify.progress(context, 'Please wait...');
      var result = await API.resetPassword(_fbKey.currentState!.value);
      Notify.hide();
      if (result.status!.status == ApiStatus.OK) {
        Notify.toast('Your password has been send to your registered phone number.', type: 1);
        Nav.back(context);
      } else {
        Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
      }
    }
  }
}
