import 'dart:io';

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:dio/dio.dart' show FormData, MultipartFile;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:im_stepper/stepper.dart';
import 'package:uuid/uuid.dart';

import '../../common/common_button.dart';
import '../../controllers/agency_request_controller.dart';
import '../../data/api.dart';
import '../../models/submit_agency_entity.dart';
import '../../nav.dart';
import '../../services/remote_config.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../../utils/helpers.dart';
import '../../utils/text.dart';
import 'agency_&_address_detail_page.dart';
import 'social_&_about_detail_page.dart';
import 'user_&_agency_logo_page.dart';
import 'user_detail_page.dart';

class AgencyRequestPage extends StatefulWidget {
  @override
  _AgencyRequestPageState createState() => _AgencyRequestPageState();
}

class _AgencyRequestPageState extends State<AgencyRequestPage> {
  final GlobalKey<FormBuilderState> fbKey = GlobalKey<FormBuilderState>();
  AgencyRequestController agency = Get.find();
  ScrollController controller = ScrollController();

  int activeStep = 1;
  Map<String, dynamic> values = {};

  @override
  Widget build(BuildContext context) {
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    values = agency.agencyEntity != null ? agency.agencyEntity!.toJson() : {};
    print("...........local storage...................");
    print(values);
    return Scaffold(
      body: SingleChildScrollView(
        controller: controller,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Padding(
                  padding: EdgeInsets.only(left: sWidth * 0.02, top: sHeight * 0.06),
                  child: GestureDetector(
                    onTap: () {
                      Nav.back(context);
                    },
                    child: Icon(Icons.arrow_back, color: Theme.of(context).primaryColor.withOpacity(0.7), size: 40),
                  ),
                ),
                HeadingText(screenWidth: sWidth * 0.2, screenHeight: sHeight * 1, txt: "Submit your Agency"),
              ],
            ),
            SubHeadingText(
              screenWidth: sWidth * 0.7,
              screenHeight: sHeight,
              txt: "Request your agency to be featured in the app.",
            ),
            SizedBox(height: 10),
            NumberStepper(
              numberStyle: TextStyle(color: Colors.white),
              enableNextPreviousButtons: false,
              activeStepColor: Theme.of(context).primaryColor,
              enableStepTapping: false,
              direction: Axis.horizontal,
              numbers: [1, 2, 3, 4],
              stepRadius: 16,
              activeStep: activeStep - 1,
              onStepReached: (index) {
                setState(() {
                  activeStep = index;
                });
              },
            ),
            SizedBox(height: 10),
            FormBuilder(
              key: fbKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (activeStep == 1) UserDetailPage(),
                  if (activeStep == 2) AgencyAndAddressDetailPage(),
                  if (activeStep == 3) SocialAndAboutDetailPage(),
                  if (activeStep == 4) UserAndAgencyLogoPage(),
                ],
              ),
            ),
            SizedBox(height: 30),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  child: Text('Prev'),
                  onPressed: activeStep > 1
                      ? () {
                          setState(() {
                            activeStep = activeStep - 1;
                            controller.animateTo(0, duration: const Duration(milliseconds: 500), curve: Curves.ease);
                          });
                        }
                      : null,
                ),
                ElevatedButton(
                  child: Text(activeStep == 4 ? " Submit" : 'Next'),
                  onPressed: onPressed,
                ),
              ],
            ),
            SizedBox(height: 10),
            Padding(
              padding: EdgeInsets.only(left: sWidth * 0.08, right: sWidth * 0.07),
              child: GestureDetector(
                onTap: () {
                  sendAnalyticsEvent(eventName: 'submit_agency_call_button_click');
                  openLink('tel:' + RemoteConfigService.contactPhone);
                },
                child: CommonButton(
                    color: Theme.of(context).primaryColor.withOpacity(0.7),
                    screenWidth: sWidth,
                    screenHeight: sHeight,
                    txt: "Having problem? Click to call"),
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
    );
  }

  void onPressed() async {
    String source = '';
    String? fcmId = await FirebaseMessaging.instance.getToken();
    var uuid = Uuid();
    if(kIsWeb){
      source = 'WebFlutter';
    }
    else if (Platform.isAndroid) {
      source = 'Android';
    }
    else if (Platform.isIOS) {
      source = 'IOS';
    }

    if (fbKey.currentState!.saveAndValidate()) {
      values.addAll(fbKey.currentState!.value);
      values['form_key'] = agency.agencyEntity?.formKey == null ? uuid.v4() : agency.agencyEntity?.formKey;
      values['fcm_id'] = fcmId;
      values['source'] = source;
      if (agency.society != null) {
        values['city_id'] = agency.society?.cityId;
        values['society_id'] = agency.society?.id;
        values['society'] = agency.society;
      }

      // Store Data In local Storage
      final agencyObject = SubmitAgencyEntity.fromJson(values);
      agency.writeInfo(agencyObject);
      FormData formData;
      formData = FormData.fromMap({...values});

      // Submit Agency Request for every page.
      if (activeStep != 4) {
        API.submitAgency(formData).then((value) => {
          if(!value.isOK){
            Notify.customDialog(context, desc: 'Error: ${value.status!.message}', type: DialogType.error),
          }
        });
      };

      // final Api call on 4 setup with pictures
      if (activeStep == 4) {
        Notify.progress(context, 'Sending request...');
        FormData formData;
        formData = FormData.fromMap({
          ...values,
          'final_step': true,
          'photo': agency.croppedImage == null ? null : MultipartFile.fromBytes(agency.croppedImage!, filename: "abc"),
          'agency_logo':
              agency.croppedImageA == null ? null : MultipartFile.fromBytes(agency.croppedImageA!, filename: "def"),
        });
        API.submitAgency(formData).then((result) {
          Notify.hide();
          if(result.isOK){
            Notify.toast(result.status?.message, type: 1);
          } else {
            Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
          }
          Nav.back(context);
        });
      } else {
        setState(() {
          activeStep = activeStep + 1;
          controller.animateTo(0, duration: const Duration(milliseconds: 500), curve: Curves.ease);
        });
      }
    }
  }
}
