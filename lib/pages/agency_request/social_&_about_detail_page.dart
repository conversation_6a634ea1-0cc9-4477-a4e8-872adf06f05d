import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

import '../../common/main_textField.dart';
import '../../controllers/agency_request_controller.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class SocialAndAboutDetailPage extends StatefulWidget {
  const SocialAndAboutDetailPage({Key? key}) : super(key: key);

  @override
  State<SocialAndAboutDetailPage> createState() => _SocialAndAboutDetailPageState();
}

class _SocialAndAboutDetailPageState extends State<SocialAndAboutDetailPage> {
  @override
  Widget build(BuildContext context) {
    AgencyRequestController agency = Get.find();
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 10),
          child: TileCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.2, txt: 'Facebook'),
                    InputText(
                        screenWidth: sWidth * 0.1,
                        screenHeight: sHeight * 0.5,
                        txt: 'فیس بک لنک'),
                  ],
                ),
                MainTextFieldPadding(
                  screenWidth: sWidth * 0.1,
                  screenHeight: sHeight * 0.5,
                  textField: FormBuilderTextField(
                    initialValue: agency.agencyEntity?.facebook != null ? agency.agencyEntity?.facebook : null,
                    name: 'facebook',
                    decoration: InputStyle(hint: 'Facebook', icon: FontAwesomeIcons.facebookF),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Youtube'),
                    InputText(
                        screenWidth: sWidth * 0.1,
                        screenHeight: sHeight * 0.5,
                        txt: 'یوٹیوب لنک'),
                  ],
                ),
                MainTextFieldPadding(
                  screenWidth: sWidth * 0.1,
                  screenHeight: sHeight * 0.5,
                  textField: FormBuilderTextField(
                    initialValue: agency.agencyEntity?.youtube != null ? agency.agencyEntity?.youtube : null,
                    name: 'youtube',
                    decoration: InputStyle(hint: 'Youtube', icon: FontAwesomeIcons.youtube),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Twitter'),
                    InputText(
                        screenWidth: sWidth * 0.1,
                        screenHeight: sHeight * 0.5,
                        txt: 'ٹویٹر لنک'),
                  ],
                ),
                MainTextFieldPadding(
                  screenWidth: sWidth * 0.1,
                  screenHeight: sHeight * 0.5,
                  textField: FormBuilderTextField(
                    initialValue: agency.agencyEntity?.twitter != null ? agency.agencyEntity?.twitter : null,
                    name: 'twitter',
                    decoration: InputStyle(hint: 'Twitter', icon: FontAwesomeIcons.twitter),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Instagram'),
                    InputText(
                        screenWidth: sWidth * 0.1,
                        screenHeight: sHeight * 0.5,
                        txt: 'انسٹا گرام لنک'),
                  ],
                ),
                MainTextFieldPadding(
                  screenWidth: sWidth * 0.1,
                  screenHeight: sHeight * 0.5,
                  textField: FormBuilderTextField(
                    initialValue: agency.agencyEntity?.instagram != null ? agency.agencyEntity?.instagram : null,
                    name: 'instagram',
                    decoration: InputStyle(hint: 'Instagram', icon: FontAwesomeIcons.instagram),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Website'),
                    InputText(
                        screenWidth: sWidth * 0.1,
                        screenHeight: sHeight * 0.5,
                        txt: 'ویب سائٹ'),
                  ],
                ),
                MainTextFieldPadding(
                  screenWidth: sWidth * 0.1,
                  screenHeight: sHeight * 0.5,
                  textField: FormBuilderTextField(
                    initialValue: agency.agencyEntity?.website != null ? agency.agencyEntity?.website : null,
                    name: 'website',
                    decoration: InputStyle(hint: 'Website', icon: Icons.web),
                  ),
                ),
                SizedBox(height: 10)
              ],
            ),
          ),
        ),
        // SizedBox(height: 20),
        // Padding(
        //   padding: EdgeInsets.symmetric(horizontal: 10),
        //   child: TileCard(
        //     child: Column(
        //       crossAxisAlignment: CrossAxisAlignment.start,
        //       children: [
        //         Row(
        //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //           children: [
        //             InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.2, txt: 'Message'),
        //             InputText(
        //                 screenWidth: sWidth * 0.1,
        //                 screenHeight: sHeight * 0.5,
        //                 txt: 'آفس کا نام'),
        //           ],
        //         ),
        //         MainTextFieldPadding(
        //           screenWidth: sWidth * 0.1,
        //           screenHeight: sHeight * 0.5,
        //           textField: FormBuilderTextField(
        //             initialValue: agency.agencyEntity?.message != null ? agency.agencyEntity?.message : null,
        //             name: 'message',
        //             minLines: 5,
        //             maxLines: 10,
        //             decoration: InputStyle(hint: 'Message', icon: Icons.message),
        //           ),
        //         ),
        //         Row(
        //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //           children: [
        //             InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Website'),
        //             InputText(
        //                 screenWidth: sWidth * 0.1,
        //                 screenHeight: sHeight * 0.5,
        //                 txt: 'ویب سائٹ'),
        //           ],
        //         ),
        //         MainTextFieldPadding(
        //           screenWidth: sWidth * 0.1,
        //           screenHeight: sHeight * 0.5,
        //           textField: FormBuilderTextField(
        //             initialValue: agency.agencyEntity?.website != null ? agency.agencyEntity?.website : null,
        //             name: 'website',
        //             decoration: InputStyle(hint: 'Website', icon: Icons.web),
        //           ),
        //         ),
        //         Row(
        //           mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //           children: [
        //             InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'About'),
        //             InputText(
        //                 screenWidth: sWidth * 0.1,
        //                 screenHeight: sHeight * 0.5,
        //                 txt: 'آفس کا نام'),
        //           ],
        //         ),
        //         MainTextFieldPadding(
        //           screenWidth: sWidth * 0.1,
        //           screenHeight: sHeight * 0.5,
        //           textField: FormBuilderTextField(
        //             initialValue: agency.agencyEntity?.about != null ? agency.agencyEntity?.about : null,
        //             name: 'about',
        //             decoration: InputStyle(hint: 'About', icon: Icons.info),
        //           ),
        //         ),
        //         SizedBox(height: 10)
        //       ],
        //     ),
        //   ),
        // ),
      ],
    );
  }
}
