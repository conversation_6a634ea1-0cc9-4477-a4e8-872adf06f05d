import 'dart:io';
import 'dart:typed_data';

import 'package:cropperx/cropperx.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

import '../../controllers/agency_request_controller.dart';
import '../../nav.dart';
import '../../utils/text.dart';

class UserAndAgencyLogoPage extends StatefulWidget {
  const UserAndAgencyLogoPage({Key? key}) : super(key: key);

  @override
  State<UserAndAgencyLogoPage> createState() => _UserAndAgencyLogoPageState();
}

class _UserAndAgencyLogoPageState extends State<UserAndAgencyLogoPage> {
  bool isAgency = false;

  @override
  Widget build(BuildContext context) {
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Column(
          children: [
            Padding(
              padding:  EdgeInsets.only(top: sHeight * 0.05),
              child: Text("Photo",  style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500)),
            ),
            Text("آپ کی اہپنی تصویر",  style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500)),
                // InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Photo'),
                // InputText(
                //     screenWidth: sWidth * 0.1,
                //     screenHeight: sHeight * 0.5,
                //     txt: 'آپ کی اہپنی تصویر'),
            Padding(
              padding: EdgeInsets.only(left: sWidth * 0.008, right: sWidth * 0.008, top: sHeight * 0.015),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    isAgency = false;
                  });
                  showModalBottomSheet(
                      isScrollControlled: true,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.only(topRight: Radius.circular(30), topLeft: Radius.circular(30))),
                      context: context,
                      builder: (context) {
                        return Container(
                          height: 160,
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ListTile(
                                  leading: Icon(Icons.camera_alt),
                                  title: Text("Camera"),
                                  onTap: () {
                                    Nav.back(context);
                                    pickImagesFromCamera();
                                  },
                                ),
                                ListTile(
                                  leading: Icon(Icons.photo),
                                  title: Text("Gallery"),
                                  onTap: () {
                                    Nav.back(context);
                                    pickImagesFromGallery();
                                  },
                                )
                              ],
                            ),
                          ),
                        );
                      });
                },
                child: imageFile != null && _croppedImage != null
                    ? Stack(
                        children: [
                          IgnorePointer(
                            ignoring: true,
                            child: Image.memory(_croppedImage!, fit: BoxFit.cover, width: 150, height: 150),
                          ),
                          Positioned(
                            top: 5,
                            right: 5,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  imageFile = null;
                                  _imageToCrop = null;
                                  _croppedImage = null;
                                });
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(0.8), borderRadius: BorderRadius.circular(20)),
                                child: Icon(Icons.close, color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(
                        width: 150,
                        height: 150,
                        color: Theme.of(context).primaryColor.withOpacity(0.2),
                        child: Center(child: Icon(Icons.camera_alt)),
                      ),
              ),
            ),
          ],
        ),
        Column(
          children: [
            Padding(
              padding:  EdgeInsets.only(top: sHeight * 0.05),
              child: Text("Agency Logo",  style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500)),
            ),
            Text( 'آفس کا لوگو ضروری ہے',  style: TextStyle(color: Colors.red, fontWeight: FontWeight.w600)),
                // InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Agency Logo'),
                // InputText(
                //     screenWidth: sWidth * 0.1,
                //     screenHeight: sHeight * 0.5,
                //     txt: 'آفس کا لوگو ضروری ہے'),
            Padding(
              padding: EdgeInsets.only(left: sWidth * 0.008, right: sWidth * 0.008, top: sHeight * 0.015),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    isAgency = true;
                  });
                  showModalBottomSheet(
                      isScrollControlled: true,
                      shape: RoundedRectangleBorder(
                          borderRadius:
                              BorderRadius.only(topRight: Radius.circular(30), topLeft: Radius.circular(30))),
                      context: context,
                      builder: (context) {
                        return Container(
                          height: 160,
                          child: Padding(
                            padding: const EdgeInsets.all(20),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                ListTile(
                                  leading: Icon(Icons.camera_alt),
                                  title: Text("Camera"),
                                  onTap: () {
                                    Nav.back(context);
                                    pickImagesFromCamera();
                                  },
                                ),
                                ListTile(
                                  leading: Icon(Icons.photo),
                                  title: Text("Gallery"),
                                  onTap: () {
                                    Nav.back(context);
                                    pickImagesFromGallery();
                                  },
                                )
                              ],
                            ),
                          ),
                        );
                      });
                },
                child: imageFileA != null && _croppedImageA != null
                    ? Stack(
                        children: [
                          IgnorePointer(
                            ignoring: true,
                            child: Image.memory(_croppedImageA!, fit: BoxFit.cover, width: 150, height: 150),
                          ),
                          Positioned(
                            top: 5,
                            right: 5,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  imageFileA = null;
                                  _imageToCropA = null;
                                  _croppedImageA = null;
                                });
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(0.8), borderRadius: BorderRadius.circular(20)),
                                child: Icon(Icons.close, color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      )
                    : Container(
                        width: 150,
                        height: 150,
                        color: Theme.of(context).primaryColor.withOpacity(0.2),
                        child: Center(child: Icon(Icons.camera_alt)),
                      ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  final GlobalKey _cropperKeyA = GlobalKey(debugLabel: 'cropperKey');
  final GlobalKey _cropperKey = GlobalKey(debugLabel: 'cropperKey');
  final ImagePicker picker = ImagePicker();
  Uint8List? _imageToCropA;
  Uint8List? _imageToCrop;
  Uint8List? _croppedImageA;
  Uint8List? _croppedImage;
  File? imageFileA;
  File? imageFile;
  AgencyRequestController agency = Get.find();

  Future<void> pickImagesFromCamera() async {
    XFile? result;
    try {
      result = await picker.pickImage(source: ImageSource.camera);
    } on Exception catch (e) {
      print(e);
    }
    if (isAgency) {
      _imageToCropA = await result?.readAsBytes();
      setState(() {
        imageFileA = File(result!.path);
        _croppedImageA = imageFileA!.readAsBytesSync();
        agency.croppedImageA = _croppedImageA;
        // showDialog(
        //     context: context,
        //     builder: (BuildContext context) {
        //       return _cropperScreenA();
        //     });
      });
    } else {
      _imageToCrop = await result?.readAsBytes();
      setState(() {
        imageFile = File(result!.path);
        showDialog(
            context: context,
            builder: (BuildContext context) {
              return _cropperScreen();
            });
      });
    }
  }

  Future<void> pickImagesFromGallery() async {
    XFile? result;
    try {
      result = await (picker.pickImage(source: ImageSource.gallery));
    } on Exception catch (e) {
      print(e);
    }
    if (isAgency) {
      _imageToCropA = await result?.readAsBytes();
      setState(() {
        imageFileA = File(result!.path);
        _croppedImageA = imageFileA!.readAsBytesSync();
        agency.croppedImageA = _croppedImageA;
        // showDialog(
        //     context: context,
        //     builder: (BuildContext context) {
        //       return _cropperScreenA();
        //     });
      });
    } else {
      _imageToCrop = await result?.readAsBytes();
      setState(() {
        imageFile = File(result!.path);
        showDialog(
            context: context,
            builder: (BuildContext context) {
              return _cropperScreen();
            });
      });
    }
  }

  _cropperScreenA() {
    return Scaffold(
      appBar: AppBar(title: Text("Crop Image")),
      body: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            _imageToCropA != null
                ? Cropper(
                    cropperKey: _cropperKeyA,
                    overlayType: OverlayType.rectangle,
                    image: Image.memory(_imageToCropA!),
                  )
                : const ColoredBox(color: Colors.grey),
            const SizedBox(height: 10),
            ElevatedButton(
              child: const Text('Crop Image'),
              onPressed: () async {
                final imageBytes = await Cropper.crop(cropperKey: _cropperKeyA, pixelRatio: 1.5);
                if (imageBytes != null) {
                  setState(() {
                    _croppedImageA = imageBytes;
                    imageFileA = File.fromRawPath(imageBytes);
                    agency.croppedImageA = imageBytes;
                  });
                  Nav.back(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  _cropperScreen() {
    return Scaffold(
      appBar: AppBar(title: Text("Crop Image")),
      body: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            _imageToCrop != null
                ? Cropper(
                    cropperKey: _cropperKey,
                    overlayType: OverlayType.rectangle,
                    image: Image.memory(_imageToCrop!),
                  )
                : const ColoredBox(color: Colors.grey),
            const SizedBox(height: 10),
            ElevatedButton(
              child: const Text('Crop Image'),
              onPressed: () async {
                final imageBytes = await Cropper.crop(cropperKey: _cropperKey, pixelRatio: 1.5);
                if (imageBytes != null) {
                  setState(() {
                    _croppedImage = imageBytes;
                    imageFile = File.fromRawPath(imageBytes);
                    agency.croppedImage = imageBytes;
                  });
                  Nav.back(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }
}
