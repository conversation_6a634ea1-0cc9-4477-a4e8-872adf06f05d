import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import '../../common/main_textField.dart';
import '../../controllers/agency_request_controller.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class AgencyAndAddressDetailPage extends StatefulWidget {
  const AgencyAndAddressDetailPage({Key? key}) : super(key: key);

  @override
  State<AgencyAndAddressDetailPage> createState() => _AgencyAndAddressDetailPageState();
}

class _AgencyAndAddressDetailPageState extends State<AgencyAndAddressDetailPage> {
  @override
  Widget build(BuildContext context) {
    AgencyRequestController agency = Get.find();
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: Tile<PERSON>ard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'CEO Name'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'آفس کے سی ای او کا نام ضروری ہے '),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.ceoName != null ? agency.agencyEntity?.ceoName : null,
                name: 'ceo_name',
                decoration: InputStyle(hint: 'CEO Name', icon: Icons.person),
                enableSuggestions: true,
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: 'Ceo Name is required'),
                  FormBuilderValidators.maxLength(190, errorText: 'Ceo Name is too long'),
                ]),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'CEO Mobile 1'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'سی ای او کا موبائل نمبر 1'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.ceoPhone1 != null ? agency.agencyEntity?.ceoPhone1 : null,
                name: 'ceo_phone_1',
                keyboardType: TextInputType.phone,
                enableSuggestions: true,
                decoration: InputStyle(hint: '03** ********', icon: Icons.phone_android),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: 'Ceo Mobile 1 is required'),
                  FormBuilderValidators.maxLength(190, errorText: 'Ceo Mobile 1 is too long'),
                ]),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'CEO Mobile 2'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'سی ای او کا موبائل نمبر 2'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.ceoPhone2 != null ? agency.agencyEntity?.ceoPhone2 : null,
                name: 'ceo_phone_2',
                enableSuggestions: true,
                keyboardType: TextInputType.phone,
                decoration: InputStyle(hint: '03** ********', icon: Icons.phone_android),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Landline 1'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'لینڈ لائن 1'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.landline1 != null ? agency.agencyEntity?.landline1 : null,
                name: 'landline_1',
                enableSuggestions: true,
                keyboardType: TextInputType.phone,
                decoration: InputStyle(hint: '0** *******', icon: Icons.phone),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Landline 2'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'لینڈ لائن 2'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.landline2 != null ? agency.agencyEntity?.landline2 : null,
                name: 'landline_2',
                enableSuggestions: true,
                keyboardType: TextInputType.phone,
                decoration: InputStyle(hint: '03** ********', icon: Icons.phone),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Whatsapp #'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'سی ای او کا واٹس ایپ نمبر'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.whatsapp != null ? agency.agencyEntity?.whatsapp : null,
                name: 'whatsapp',
                enableSuggestions: true,
                keyboardType: TextInputType.phone,
                decoration: InputStyle(hint: '03** ********', icon: FontAwesomeIcons.whatsapp),

              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Email'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'ای میل'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.email != null ? agency.agencyEntity?.email : null,
                name: 'email',
                enableSuggestions: true,
                decoration: InputStyle(hint: 'Email', icon: Icons.mail),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Fax'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'فیکس نمبر'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.fax != null ? agency.agencyEntity?.fax : null,
                name: 'fax',
                keyboardType: TextInputType.phone,
                enableSuggestions: true,
                decoration: InputStyle(hint: 'Fax', icon: FontAwesomeIcons.fax),
              ),
            ),
            SizedBox(height: 10)
          ],
        ),
      ),
    );
  }
}
