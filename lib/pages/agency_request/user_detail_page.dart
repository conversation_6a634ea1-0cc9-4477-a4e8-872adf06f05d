import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';

import '../../common/main_textField.dart';
import '../../controllers/agency_request_controller.dart';
import '../../data/api.dart';
import '../../models/society_entity.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class UserDetailPage extends StatefulWidget {
  const UserDetailPage({Key? key}) : super(key: key);

  @override
  State<UserDetailPage> createState() => _UserDetailPageState();
}

class _UserDetailPageState extends State<UserDetailPage> {
  List<SocietyEntity?> societies = [];

  @override
  void initState() {
    getSocietiesListWithCity();
    super.initState();
  }

  getSocietiesListWithCity() async {
    await API.getSocietiesListWithCityCached().then((value) {
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          societies.addAll(value.data!);
        });
    });
  }

  @override
  Widget build(BuildContext context) {
    AgencyRequestController agency = Get.find();
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: TileCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.2, txt: 'Agency Name'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'آفس کا نام'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.agencyName != null ? agency.agencyEntity?.agencyName : null,
                name: 'agency_name',
                enableSuggestions: true,
                decoration: InputStyle(hint: 'Agency Name', icon: Icons.person),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: 'Agency Name is required'),
                  FormBuilderValidators.maxLength(190, errorText: 'Agency Name is too long'),
                ]),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Name'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'آپ کا اپنا نام'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.name != null ? agency.agencyEntity?.name : null,
                name: 'name',
                enableSuggestions: true,
                decoration: InputStyle(hint: 'Name', icon: Icons.person),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: 'Name is required'),
                  FormBuilderValidators.minLength(4, errorText: 'Name is too short'),
                  FormBuilderValidators.maxLength(190, errorText: 'Name is too long'),
                ]),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Designation'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: ' آپ کا عہدہ'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.designation != null ? agency.agencyEntity?.designation : null,
                name: 'designation',
                enableSuggestions: true,
                decoration: InputStyle(hint: 'Designation', icon: Icons.description),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: 'Designation is required'),
                  FormBuilderValidators.minLength(1, errorText: 'Designation is too short'),
                  FormBuilderValidators.maxLength(190, errorText: 'Designation is too long'),
                ]),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Phone'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'آپ کا فون نمبر'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.phone != null ? agency.agencyEntity?.phone : null,
                name: 'phone',
                keyboardType: TextInputType.phone,
                enableSuggestions: true,
                decoration: InputStyle(hint: 'Phone', icon: Icons.phone_android),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: 'Phone is required'),
                  FormBuilderValidators.maxLength(190, errorText: 'Phone is too long'),
                ]),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.2, txt: 'Society'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'آپ کا آفس کس سوسائٹی میں ہے'),
              ],
            ),
            Padding(
              padding: EdgeInsets.only(left: sWidth * 0.008, right: sWidth * 0.008, top: sHeight * 0.015),
              child: DropdownSearch<SocietyEntity?>(
                popupProps: PopupProps.dialog(
                  searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                  showSearchBox: true,
                  searchDelay: Duration(milliseconds: 200),
                ),
                dropdownDecoratorProps: DropDownDecoratorProps(
                  dropdownSearchDecoration: InputStyle()
                      .copyWith(contentPadding: EdgeInsets.symmetric(horizontal: 15), hintText: 'Select society'),
                ),
                selectedItem: agency.agencyEntity?.society != null ? agency.agencyEntity?.society : null,
                onChanged: (society) {
                  print(society!.title);
                  agency.society = society;
                },
                itemAsString: (society) => society!.title!,
                items: societies,
                validator: FormBuilderValidators.required(errorText: 'Society is required'),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.3, txt: 'Address'),
                InputText(
                    screenWidth: sWidth * 0.1,
                    screenHeight: sHeight * 0.5,
                    txt: 'آپکے آفس کا پتہ'),
              ],
            ),
            MainTextFieldPadding(
              screenWidth: sWidth * 0.1,
              screenHeight: sHeight * 0.5,
              textField: FormBuilderTextField(
                initialValue: agency.agencyEntity?.address != null ? agency.agencyEntity?.address : null,
                name: 'address',
                enableSuggestions: true,
                decoration: InputStyle(hint: 'Address', icon: FontAwesomeIcons.addressCard),
                validator: FormBuilderValidators.compose([
                  FormBuilderValidators.required(errorText: 'Address is required'),
                  FormBuilderValidators.maxLength(190, errorText: 'Address is too long'),
                ]),
              ),
            ),
            SizedBox(height: 10),
          ],
        ),
      ),
    );
  }
}
