import 'package:auto_size_text/auto_size_text.dart';
import 'package:find_dropdown/find_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import 'package:property_dealers_directory/nav.dart';
import 'package:provider/provider.dart';

import '../../data/api.dart';
import '../../providers/onboarding_provider.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class UserLocationSelection extends StatefulWidget {
  const UserLocationSelection({Key? key}) : super(key: key);

  @override
  _UserLocationSelectionState createState() => _UserLocationSelectionState();
}

class _UserLocationSelectionState extends State<UserLocationSelection> {
  UserTypes type = UserTypes.none;
  CityEntity selectedCity = CityEntity();
  CityEntity selectedSociety = CityEntity();
  List<CityEntity> cities = [];
  bool citiesLoaded = false;
  bool enableNotifications = false;
  var societyDropdown = GlobalKey<FindDropdownState>();

  get selectedAll =>
      type != UserTypes.none && selectedCity.id != -1 && selectedSociety.id != -1;

  @override
  void initState() {
    API.getCitiesList().then((value) {
      setState(() {
        cities.addAll(value.data as List<CityEntity>);
        citiesLoaded = true;
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Center(child: Image.asset('images/logo.png', height: 120)),
                RichHeading(start: 'Select', middle: ' your', end: ' location'),
                SizedBox(
                  height: 110,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      SelectionItem(
                        title: 'Dealer',
                        onTap: () {
                          setState(() {
                            type = UserTypes.agent;
                            enableNotifications = true;
                          });
                        },
                        icon: 'images/icons/dealer.png',
                        isSelected: type == UserTypes.agent,
                      ),
                      SelectionItem(
                        title: 'Buyer/Seller',
                        onTap: () {
                          setState(() {
                            type = UserTypes.individual;
                            enableNotifications = false;
                          });
                        },
                        icon: 'images/icons/individual.png',
                        isSelected: type == UserTypes.individual,
                      ),
                      SelectionItem(
                        title: 'Resident',
                        onTap: () {
                          setState(() {
                            type = UserTypes.resident;
                            enableNotifications = false;
                          });
                        },
                        icon: 'images/icons/resident.png',
                        isSelected: type == UserTypes.resident,
                      ),
                      SelectionItem(
                        title: 'Delivery',
                        onTap: () {
                          setState(() {
                            type = UserTypes.delivery;
                            enableNotifications = false;
                          });
                        },
                        icon: 'images/icons/delivery.png',
                        isSelected: type == UserTypes.delivery,
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 10),
                if (citiesLoaded && type != UserTypes.none)
                  AnimatedContainer(
                    duration: Duration(milliseconds: 500),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichHeading(
                            start: 'Select',
                            middle: 'Your preferred',
                            end: 'location'),
                        SizedBox(
                          height: 110,
                          child: ListView.builder(
                            shrinkWrap: true,
                            // physics: ClampingScrollPhysics(),
                            itemCount: cities.length,
                            scrollDirection: Axis.horizontal,
                            itemBuilder: (context, index) {
                              var city = cities[index];
                              return SelectionItem(
                                title: city.name,
                                onTap: () {
                                  setState(() {
                                    selectedCity = city;
                                    selectedSociety = CityEntity();
                                    societyDropdown.currentState
                                        ?.setSelectedItem(selectedSociety);
                                  });
                                },
                                icon: city.image,
                                isSelected: selectedCity.id == city.id,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                if (selectedCity.id != -1)
                  AnimatedContainer(
                    duration: Duration(milliseconds: 500),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        RichHeading(
                          start: 'Select',
                          middle: ' Your',
                          end: ' Preferred Society',
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: FindDropdown<CityEntity>(
                            key: societyDropdown,
                            searchBoxDecoration: InputDecoration(
                              hintText: 'Search Society',
                              border: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Theme.of(context).primaryColor,
                                ),
                                borderRadius: BorderRadius.circular(5),
                              ),
                            ),
                            items: selectedCity.societies!,
                            selectedItem: selectedSociety,
                            onChanged: (CityEntity? data) {
                              setState(() {
                                selectedSociety = data!;
                              });
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                if (selectedSociety.id != -1)
                  AnimatedContainer(
                    duration: Duration(milliseconds: 500),
                    child: Column(
                      children: [
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            RichHeading(
                              start: 'Enable',
                              middle: ' Society',
                              end: ' Notifications',
                              padding: 10,
                            ),
                            Switch(
                                value: enableNotifications,
                                onChanged: (value) {
                                  setState(() {
                                    enableNotifications = value;
                                  });
                                }),
                          ],
                        ),
                        Text('You can change this later in settings'),
                      ],
                    ),
                  ),
              ],
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.all(12.0),
                child: ElevatedButton(
                  onPressed: selectedAll
                      ? () {
                          obProvider.selectCity(selectedCity);
                          obProvider.selectSociety(selectedSociety);
                          obProvider.selectUserType(type);
                          obProvider.setSocietyNotify(enableNotifications, selectedSociety.id);
                          obProvider.setHot(true);
                          Nav.homePage(context);
                        }
                      : null,
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Text(
                      'Next',
                      style: TextStyle(fontSize: 18),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class SelectionItem extends StatelessWidget {
  const SelectionItem({
    super.key,
    required this.title,
    required this.onTap,
    required this.icon,
    this.isSelected = false,
  });

  final String title;
  final Function onTap;
  final String icon;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap as void Function()?,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).primaryColor),
            borderRadius: BorderRadius.circular(5),
          ),
          height: 100,
          width: 80,
          child: ClipRRect(
            borderRadius: BorderRadius.circular(5),
            child: Stack(
              children: [
                icon.isEmpty || icon.startsWith('http')
                    ? NetworkImageWithPlaceholder(
                        icon,
                        height: 67,
                        // width: 80,
                      )
                    : Image.asset(
                        icon,
                        height: 67,
                        // width: 80,
                      ),
                Positioned(
                  bottom: 0,
                  left: 0,
                  right: 0,
                  child: Container(
                    color: Theme.of(context).primaryColor,
                    // width: 80,
                    height: 25,
                    padding: EdgeInsets.all(5),
                    child: Center(
                      child: AutoSizeText(
                        title,
                        maxLines: 1,
                        minFontSize: 8,
                        maxFontSize: 12,
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ),
                ),
                if (isSelected)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.green,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
