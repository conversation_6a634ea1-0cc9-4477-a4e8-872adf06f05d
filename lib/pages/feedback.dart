import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/controllers/feedback_controller.dart';

import '../common/main_textField.dart';
import '../data/api.dart';
import '../nav.dart';
import '../utils/Notify.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class FeedbackPage extends StatefulWidget {
  FeedbackPage({Key? key}) : super(key: key);

  @override
  State<FeedbackPage> createState() => _FeedbackPageState();
}

class _FeedbackPageState extends State<FeedbackPage> {
   GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  FeedbackController feedback = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Positioned(
            bottom: 0,
            child: Image.asset('images/landmark.png', width: context.width),
          ),
          Padding(
            padding: const EdgeInsets.only(right: 20, left: 20.0, top: 30.0, ),
            child: Column(
              children: [
                Column(
                  children: [
                    Center(child: Image.asset('images/bp_logo_horizontal.png', width: 200, height: 80)),
                    const SizedBox(height: 10),
                    Text("You've been using the app for a while now, We would love to get a feedback from you about the app's functionality and quality of the data. (optional)",
                      textAlign: TextAlign.justify,
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                Expanded(
                  child: SingleChildScrollView(
                    child: Container(
                      child: FormBuilder(
                        key: _fbKey,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Name")),
                            FormBuilderTextField(
                              name: 'name',
                              keyboardType: TextInputType.name,
                              decoration: InputStyle(hint: "Enter name", icon: Icons.person),
                              autovalidateMode: AutovalidateMode.onUserInteraction,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(errorText: 'Name is required'),
                              ]),
                            ),
                            const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Mobile #")),
                            FormBuilderTextField(
                              name: 'phone',
                              keyboardType: TextInputType.number,
                              decoration: InputStyle(hint: "Enter mobile", icon: Icons.phone),
                              autovalidateMode: AutovalidateMode.onUserInteraction,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(errorText: 'Mobile is required'),
                              ]),
                            ),
                            const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Current City")),
                            FormBuilderTextField(
                              name: 'city',
                              keyboardType: TextInputType.text,
                              decoration: InputStyle(hint: "Enter current city", icon: Icons.location_city),
                              autovalidateMode: AutovalidateMode.onUserInteraction,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(errorText: 'City is required'),
                              ]),
                            ),
                            const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Feedback")),
                            FormBuilderTextField(
                              name: 'feedback',
                              maxLines: 4,
                              keyboardType: TextInputType.text,
                              decoration: InputStyle(hint: "Enter message", icon: Icons.message),
                              autovalidateMode: AutovalidateMode.onUserInteraction,
                              validator: FormBuilderValidators.compose([
                                FormBuilderValidators.required(errorText: 'Message is required'),
                              ]),
                            ),
                            const SizedBox(height: 20),
                            Center(child: PrimaryButton(text: "Submit", onPressed: () async{
                              _submitClick(context);
                              // if (_fbKey.currentState!.saveAndValidate()) {
                              //   Notify.progress(context, 'Please wait...');
                              //   APIResponse<dynamic> result = await API.feedbackFormSubmit(_fbKey.currentState!.value);
                              //   if (result.isOK) {
                              //     print(result);
                              //     Notify.hide();
                              //     Notify.toast('Request Submit Successfully', type: 1);
                              //     feedback.submit();
                              //     Nav.homePage(context);
                              //   } else {
                              //     Notify.hide();
                              //     Notify.customDialog(context, desc: result.status!.message, type: DialogType.ERROR);
                              //   }
                              // }
                            })),
                            Align(
                              alignment: Alignment.bottomRight,
                              child: GestureDetector(
                                  onTap: (){
                                    Nav.homePage(context);
                                  },
                                  child: Text("Skip")),
                            ),
                            const SizedBox(height: 5.0),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),


        ],
      ),
    );

    //   SafeArea(
    //   child: Scaffold(
    //     body: SingleChildScrollView(
    //       child: Padding(
    //         padding: const EdgeInsets.all(10.0),
    //         child: Column(
    //           crossAxisAlignment: CrossAxisAlignment.start,
    //           children: [
    //             const SizedBox(height: 10),
    //             Center(child: Image.asset('images/bp_logo_city_selection.png', width: 200, height: 100)),
    //             const SizedBox(height: 10),
    //             Text("You've been using the app for a while now, We would love to get a feedback from you about the app's functionality and quality of the data. (optional)",
    //             textAlign: TextAlign.justify,
    //             ),
    //             const SizedBox(height: 10),
    //             Container(
    //               height: MediaQuery.of(context).size.height * .75,
    //               decoration: BoxDecoration(
    //                 border: Border.all(width: 1, color: Theme.of(context).colorScheme.primary),
    //                 borderRadius: const BorderRadius.all(Radius.circular(20)),
    //               ),
    //               child:SingleChildScrollView(
    //                 child: FormBuilder(
    //                   key: _fbKey,
    //                   child: Padding(
    //                     padding: const EdgeInsets.symmetric(horizontal: 20),
    //                     child: Column(
    //                       mainAxisAlignment: MainAxisAlignment.center,
    //                       crossAxisAlignment: CrossAxisAlignment.start,
    //                       children: [
    //                         const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Name")),
    //                         FormBuilderTextField(
    //                           name: 'name',
    //                           keyboardType: TextInputType.name,
    //                           decoration: InputStyle(hint: "Enter name", icon: Icons.person),
    //                           autovalidateMode: AutovalidateMode.onUserInteraction,
    //                           validator: FormBuilderValidators.compose([
    //                             FormBuilderValidators.required(errorText: 'Name is required'),
    //                           ]),
    //                         ),
    //                         const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Mobile #")),
    //                         FormBuilderTextField(
    //                           name: 'phone',
    //                           keyboardType: TextInputType.number,
    //                           decoration: InputStyle(hint: "Enter mobile", icon: Icons.phone),
    //                           autovalidateMode: AutovalidateMode.onUserInteraction,
    //                           validator: FormBuilderValidators.compose([
    //                             FormBuilderValidators.required(errorText: 'Mobile is required'),
    //                           ]),
    //                         ),
    //                         const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Current City")),
    //                         FormBuilderTextField(
    //                           name: 'city',
    //                           keyboardType: TextInputType.text,
    //                           decoration: InputStyle(hint: "Enter current city", icon: Icons.location_city),
    //                           autovalidateMode: AutovalidateMode.onUserInteraction,
    //                           validator: FormBuilderValidators.compose([
    //                             FormBuilderValidators.required(errorText: 'City is required'),
    //                           ]),
    //                         ),
    //                         const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Feedback")),
    //                         FormBuilderTextField(
    //                           name: 'feedback',
    //                           maxLines: 4,
    //                           keyboardType: TextInputType.text,
    //                           decoration: InputStyle(hint: "Enter message", icon: Icons.message),
    //                           autovalidateMode: AutovalidateMode.onUserInteraction,
    //                           validator: FormBuilderValidators.compose([
    //                             FormBuilderValidators.required(errorText: 'Message is required'),
    //                           ]),
    //                         ),
    //                         const SizedBox(height: 20),
    //                         Center(child: PrimaryButton(text: "Submit", onPressed: () async{
    //                           if (_fbKey.currentState!.saveAndValidate()) {
    //                             Notify.progress(context, 'Please wait...');
    //                             APIResponse<dynamic> result = await API.feedbackFormSubmit(_fbKey.currentState!.value);
    //                             if (result.isOK) {
    //                               print(result);
    //                               Notify.hide();
    //                               Notify.toast('Request Submit Successfully', type: 1);
    //                               // feedback.submit();
    //                               Nav.back(context);
    //                             } else {
    //                               Notify.hide();
    //                               Notify.customDialog(context, desc: result.status!.message, type: DialogType.ERROR);
    //                             }
    //                           }
    //                         })),
    //                         Align(
    //                           alignment: Alignment.bottomRight,
    //                           child: GestureDetector(
    //                               onTap: (){
    //                                 Nav.homePage(context);
    //                               },
    //                               child: Text("Skip")),
    //                         ),
    //                       ],
    //                     ),
    //                   ),
    //                 ),
    //               ),
    //             ),
    //           ],
    //         ),
    //       ),
    //     ),
    //   ),
    // );
  }

  _submitClick(BuildContext context) async {
    if (_fbKey.currentState!.saveAndValidate()) {
      Notify.progress(context, 'Please wait...');
      APIResponse<dynamic> result = await API.feedbackFormSubmit(_fbKey.currentState!.value);
      if (result.isOK) {
        print(result);
        Notify.hide();
        Notify.toast('Request Submit Successfully', type: 1);
        feedback.submit();
        Nav.homePage(context);
      } else {
        Notify.hide();
        Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
      }
    }
  }
}