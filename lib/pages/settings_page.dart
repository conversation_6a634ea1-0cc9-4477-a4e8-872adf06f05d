import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:provider/provider.dart';
import 'package:form_builder_extra_fields/form_builder_extra_fields.dart';


import '../common/main_textField.dart';
import '../data/api.dart';
import '../models/city_entity.dart';
import '../utils/base_state.dart';
import '../utils/functions.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({Key? key}) : super(key: key);

  @override
  _SettingsPageState createState() => _SettingsPageState();
}

class _SettingsPageState extends BaseState<SettingsPage, List<CityEntity>> {
  @override
  String get title => "Settings";

  @override
  void getData() {
    API.getCitiesList().then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'SettingsPage');
  }

  @override
  Widget body() {
    final ob = context.watch<OnboardingProvider>();
    final societies = data!.firstWhere((e) => e.id == ob.onboarding.city.id).societies;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 20),
      child: ListView(
        children: [
          Text("Change Default City", style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500)),
          SizedBox(height: 10),
          FormBuilderSearchableDropdown<CityEntity>(
            popupProps: const PopupProps.menu(showSearchBox: false),
            name: 'city_id',
            items: data!,
            initialValue: ob.onboarding.city,
            selectedItem: ob.onboarding.city,
            onChanged: (v){
              ob.selectCity(v!);
            },
            decoration: InputStyle(hint: 'Select city'),
            compareFn: CityEntity.compareFn,
            filterFn: CityEntity.filterFn,
          ),
          SizedBox(height: 10),
          Text("Change Default Society", style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500)),
          SizedBox(height: 10),
          FormBuilderSearchableDropdown<CityEntity>(
            popupProps: const PopupProps.modalBottomSheet(showSearchBox: true, searchDelay: Duration(milliseconds: 200)),
            name: 'city_id',
            items: ob.city.societies,
            initialValue: ob.onboarding.society,
            selectedItem: ob.onboarding.society,
            onChanged: (v){
              ob.selectSociety(v!);
            },
            decoration: InputStyle(hint: 'Select Society'),
            compareFn: CityEntity.compareFn,
            filterFn: CityEntity.filterFn,
          ),
          SizedBox(height: 10),
          Text("Change User Type", style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500)),
          SizedBox(height: 10),
          FormBuilderRadioGroup<UserTypes>(
            activeColor: Theme.of(context).primaryColor,
            initialValue: ob.userType,
            name: 'user_type',
            decoration: InputStyle().copyWith(contentPadding: EdgeInsets.all(0)),
            onChanged: (dynamic v) {
              setState(() {
                ob.selectUserType(v);
              });
            },
            options: UserTypes.values
                .where((e) => e != UserTypes.none)
                .map((e) => FormBuilderFieldOption(
                    value: e, child: Text('${e.title}', style: TextStyle(fontSize: 16))))
                .toList(),
          ),
          SizedBox(height: 10),
          Text("Notifications", style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500)),
          SizedBox(height: 10),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 20, vertical: 5),
            decoration: BoxDecoration(
              boxShadow: const [BoxShadow(color: Color(0xff32586d), blurRadius: 2, spreadRadius: 0.5)],
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(10.0),
            ),
            child: ListView(
              shrinkWrap: true,
              physics: NeverScrollableScrollPhysics(),
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text("Hot Property Ads", style: TextStyle(fontSize: 18, color: Colors.black)),
                    Switch(
                        activeColor: Theme.of(context).primaryColor,
                        value: ob.onboarding.notificationHot,
                        onChanged: (v) {
                          setState(() {
                            ob.setHot(v);
                          });
                        }),
                  ],
                ),
                SizedBox(height: 5),
                Text("Select Society to get notifications", style: TextStyle(fontSize: 18, color: Colors.black, fontWeight: FontWeight.w500)),
                SizedBox(height: 5),
                ...societies.map((e) => Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(e.name, style: TextStyle(fontSize: 16, color: Colors.black)),
                            Switch(
                                activeColor: Theme.of(context).primaryColor,
                                value: ob.notifySocieties.contains(e.id),
                                onChanged: (v) {
                                  setState(() {
                                    ob.setSocietyNotify(v, e.id);
                                  });
                                }),
                          ],
                        ))
                    .toList(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
