import 'package:flutter/material.dart';

import '../data/api.dart';
import '../models/agency_entity.dart';
import '../tabs/location_tab.dart';
import '../tabs/social_tab.dart';
import '../tabs/staff_tab.dart';
import '../utils/functions.dart';
import '../utils/sliver_base_state.dart';
import '../utils/text.dart';
import '../widgets/agency_details.dart';

class AgencyDetailPage extends StatefulWidget {
  final int id;

  AgencyDetailPage(this.id);

  @override
  _AgencyDetailPageState createState() => _AgencyDetailPageState();
}

class _AgencyDetailPageState extends SliverBaseState<AgencyDetailPage, AgencyEntity> {
  @override
  String get title => 'Agency Detail';

  @override
  bool get showAd => true;

  @override
  bool get pullToRefresh => false;

  @override
  void getData() {
    API.getAgency(widget.id).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'AgencyDetailPage');
  }

  bool get isStaff => data!.staff != null && data!.staff!.length > 0;

  @override
  Widget body() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            AgencyDetails(data!),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10.0),
              child: Heading('Location'),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 2.0),
              child: Container(
                height: 200,
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10.0),
                  child: LocationDetailTab(data!),
                ),
              ),
            ),
            if (data!.website != null ||
                data!.facebook != null ||
                data!.email != null ||
                data!.instagram != null ||
                data!.twitter != null ||
                data!.youtube != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 10.0),
                child: Heading('Social'),
              ),
            SocialDetailTab(data!),
            if (isStaff)
              Padding(
                padding: const EdgeInsets.only(top: 10.0),
                child: Heading('Team'),
              ),
            if (isStaff)
              Flexible(
                fit: FlexFit.loose,
                child: StaffTab(data!.staff),
              )
          ],
        ),
      ),
    );
  }
}
