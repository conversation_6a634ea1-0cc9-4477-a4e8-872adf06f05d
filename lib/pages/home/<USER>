import 'package:auto_size_text/auto_size_text.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../data/api.dart';
import '../../models/classified_short_entity.dart';
import '../../nav.dart';
import '../../utils/functions.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import '../classified_property/classified_property_list_item.dart';
import 'home_heading_widget.dart';

class TopClassifieds extends StatelessWidget {

  List<ClassifiedShortEntity> property = [];
   TopClassifieds(this.property, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    double viewportFraction = screenWidth < 600 ? 0.9 : 0.2;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HomeHeadingWidget(
          title: 'Hot Classified 🔥🔥',
          onPress: (){
            Nav.classifiedPropertyListPage(context);
            sendAnalyticsEvent(eventName: 'home_classified_all_button_click');
          },
        ),
        SizedBox(height: 2),
        CarouselSlider(
          options: CarouselOptions(
            height: 150,
            viewportFraction: viewportFraction,
            autoPlay: true,
            autoPlayInterval: Duration(seconds: 3),
            autoPlayAnimationDuration: Duration(milliseconds: 800),
            autoPlayCurve: Curves.fastOutSlowIn,
            enlargeCenterPage: false,
            scrollDirection: Axis.horizontal,
          ),
          items: property.map((i) {
            return Builder(
              builder: (BuildContext context) {
                return Transform.scale(
                  scale: 1.0,
                    child: ClassifiedPropertyListItem(i, false),
                );
                return Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
                  child: TileCard(
                    onTap: () {
                      Map<String, dynamic> body = {};
                      body['id'] = i?.id;
                      body['action'] = "Click";
                      body['model'] = "classified_property";
                      API.postAnalytics(body);
                      sendAnalyticsEvent(
                          eventName: 'home_top_classified_item_click', parameters: {'classified_id': i!.id});
                      Nav.classifiedPropertyDetailPage(context, i.id, i.photo, isHomeClick: 1);
                    },
                    horizontal: 0,
                    vertical: 0,
                    elevation: 2,
                    child: SizedBox(
                      height: 90,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          NetworkImageWithPlaceholder(
                            i!.photo!,
                            width: screenWidth,
                            radius: 5,
                            height: 100,
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                AutoSizeText(i.price != '0' ? i.price : 'Call for price', maxLines: 1, overflow: TextOverflow.ellipsis,
                                  style: TextStyle(
                                    fontSize: 15,
                                    color: Theme.of(context).primaryColor,
                                  ),),
                                SizedBox(height: 3.0),
                                LimitedBox(
                                  maxWidth: screenWidth * .25,
                                  child: FittedBox(
                                    child: AutoSizeText('${i.block}, \n${i.society}, ${i.city}', maxLines: 2,
                                      style: TextStyle(color: Colors.black54, fontSize: 12),),
                                  ),
                                ),
                                SizedBox(height: 5),
                                LimitedBox(
                                  maxWidth: screenWidth * .25,
                                  child: FittedBox(
                                    child: Row(
                                      children: [
                                        if (i.bedrooms != 0)
                                          Row(
                                            children: [
                                              Text('${i.bedrooms}'),
                                              SizedBox(width: 2),
                                              Text('bds'),
                                              SizedBox(width: 2),
                                              Text(
                                                '|',
                                                style: TextStyle(color: Colors.grey[400]),
                                              ),
                                              SizedBox(width: 2)
                                            ],
                                          ),
                                        if (i.bathrooms != 0)
                                          Row(
                                            children: [
                                              Text('${i.bathrooms}'),
                                              SizedBox(width: 2),
                                              Text('baths'),
                                              SizedBox(width: 2),
                                              Text(
                                                '|',
                                                style: TextStyle(color: Colors.grey[400]),
                                              ),
                                              SizedBox(width: 2)
                                            ],
                                          ),
                                        Text(
                                          '${i.size} ${i.sizeUnit}',
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                                SizedBox(height: 5),
                                LimitedBox(
                                  maxWidth: screenWidth * .25,
                                  child: FittedBox(
                                    child: i.purpose != 'Require'
                                        ? AutoSizeText('${i.type} ${i.category} for ${i.purpose}',maxLines: 1, overflow: TextOverflow.ellipsis,
                                        style: TextStyle(color: Colors.black, fontSize: 12))

                                        : AutoSizeText('${i.purpose} ${i.type} ${i.category}',maxLines: 1, overflow: TextOverflow.ellipsis,
                                      style: TextStyle(color: Colors.black, fontSize: 12),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
      ],
    );
  }
}
