import 'package:auto_size_text/auto_size_text.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:property_dealers_directory/models/project_Short_entity.dart';
import 'package:property_dealers_directory/theme.dart';

import '../../data/api.dart';
import '../../nav.dart';
import '../../utils/functions.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/widgets.dart';
import 'home_heading_widget.dart';

class FeaturedProjects extends StatelessWidget {
  List<ProjectShortEntity> project = [];
   FeaturedProjects(this.project, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    final enlargeCenterPage = screenWidth < 600 ? true : false;
    return
      project.length < 1 ? Container() :
      Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HomeHeadingWidget(
          title: 'Featured Projects',
          onPress: (){
            Nav.projectListPage(context);
            sendAnalyticsEvent(eventName: 'home_project_all_button_click');
          },
        ),
        SizedBox(height: 2),
        CarouselSlider(
          options: CarouselOptions(
            height: 190,
            viewportFraction: getProjectFraction(context),
            autoPlay: true,
            autoPlayInterval: Duration(seconds: 3),
            autoPlayAnimationDuration: Duration(milliseconds: 800),
            autoPlayCurve: Curves.fastOutSlowIn,
            enlargeCenterPage: enlargeCenterPage,
            scrollDirection: Axis.horizontal,
          ),
          items: project.map((i) {
            return Builder(
              builder: (BuildContext context) {
                return Padding(
                  padding: const EdgeInsets.only(left: 2, right: 2, bottom: 10),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: InkWell(
                        onTap: () {
                          Map<String, dynamic> body = {};
                          body['id'] = i.id;
                          body['action'] = "Click";
                          body['model'] = "project";
                          API.postAnalytics(body);
                          sendAnalyticsEvent(
                              eventName: 'home_project_item_click', parameters: {'project_Id': i.id});
                          Nav.projectDetailPage(context, i.id, i.logo);
                        },
                        // horizontal: 0,
                        // vertical: 0,
                        // elevation: 2,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.max,
                          children: [
                            Expanded(
                              child: Hero(
                                tag: i.logo,
                                child: NetworkImageWithPlaceholder(
                                  i.logo,
                                  width: double.infinity,
                                  radius: 0,
                                ),
                              ),
                            ),
                            Container(
                              color: MyColors.primary,
                              width: double.infinity,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                child: Center(
                                  child: AutoSizeText(
                                    i.title,
                                    maxLines: 2,
                                    style: TextStyle(
                                      fontSize: 16,
                                      color: Colors.white,
                                      // fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
      ],
    );
  }
}
