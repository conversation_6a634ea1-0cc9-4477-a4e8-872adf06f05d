import 'package:flutter/material.dart';

import '../../utils/text.dart';

class HomeHeadingWidget extends StatelessWidget {
  String title;
  VoidCallback onPress;
   HomeHeadingWidget({
     required this.title,
     required this.onPress,
     Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  Padding(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          RichHeading.auto(text: title, fontSize: 16.0, padding: 2.0,),
          GestureDetector(
            onTap: onPress,
            child: Container(
                padding: EdgeInsets.symmetric(vertical: 5.0, horizontal: 8.0),
                decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5)),
                child: Text(
                  "See all",
                  style: TextStyle(color: Colors.white),
                )),
          ),
        ],
      ),
    );
  }
}
