import 'package:auto_size_text/auto_size_text.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:double_back_to_close_app/double_back_to_close_app.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/pages/home/<USER>';
import 'package:property_dealers_directory/pages/home/<USER>';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/theme.dart';
import 'package:rate_my_app/rate_my_app.dart';

import '../../config.dart';
import '../../controllers/user_plan_controller.dart';
import '../../data/api.dart';
import '../../models/home_entity.dart';
import '../../nav.dart';
import '../properties_list/properties_hot_list.dart';
import '../../services/remote_config.dart';
import '../../services/update_checker.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import '../../widgets/drawer.dart';
import '../../widgets/featured_agency.dart';
import '../../widgets/property_selection_dialog.dart';
import 'featured_projects.dart';
import 'home_heading_widget.dart';
import 'news.dart';

class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends BaseState<HomePage, HomeEntity> {
  final searchController = TextEditingController();
  int backPress = 0;
  int totalBackPress = 2;


  RateMyApp rateMyApp = RateMyApp(
    minDays: 7,
    minLaunches: 10,
    googlePlayIdentifier: Config.PLAY_STORE_PACKAGE,
    appStoreIdentifier: Config.APPLE_ID,
  );
  // Future<bool> onWillPop() {
  //   if (backPress < 2) {
  //     Fluttertoast.showToast(msg: "Double ");
  //     backPress++;
  //     Future.delayed(Duration(seconds: 1, milliseconds: 500), () {
  //       backPress--;
  //     });
  //     return Future.value(false);
  //   } else {
  //     return Future.value(true);
  //   }
  // }

  @override
  void initState() {
    super.initState();
    SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.light.copyWith(
        statusBarColor: MyColors.primary
    ));
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await rateMyApp.init();
      if (mounted && rateMyApp.shouldOpenDialog) {
        rateMyApp.showRateDialog(context);
      }
      if(mounted){
        UpdateChecker()..check();
        Get.find<UserPlanController>().refresh();
      }

    });
  }

  @override
  void getData() {
    API.getHome(obProvider.city.id).then(this.handleResponse);
  }

  @override
  bool get withAppBar => false;

  @override
  bool get pullToRefresh => true;

  @override
  Widget get drawer => DrawerNavigation();

  @override
  Widget? get fab => RemoteConfigService.isHowToEnabled
      ? FloatingActionButton(
          backgroundColor: Theme.of(context).primaryColor,
          child: Icon(FontAwesomeIcons.question),
          onPressed: () {
            Nav.videoListPage(context);
          },
        )
      : null;

  @override
  Widget get pageLoader {
    return Column(
      children: <Widget>[
        SafeArea(child: _topBar()),
        FullLoader(),
      ],
    );
  }

  @override
  Widget body() {
    // double screenHeight = MediaQuery.of(context).size.height;
    // double screenWidth = MediaQuery.of(context).size.width;
    // print('///////////////////////////////////////////////');
    // print("height $screenHeight");
    // print("width $screenWidth");
    // UpdateChecker()..check();
    return SafeArea(
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            _topBar(),
            FeaturedAgencyList(data!.agencies),
            TopClassifieds(data?.classifieds ?? []),
            if(!RemoteConfigService.isInventoryBehindLogin && obProvider.isAgent)
              ...[
                HomeHeadingWidget(
                  title: 'Hot Inventory 🔥🔥',
                  onPress: (){
                    Nav.propertiesPage(context);
                    sendAnalyticsEvent(eventName: 'home_inventory_all_button_click');
                  },
                ),
                PropertiesHotList()
              ],
            if (RemoteConfigService.featureProjectVisibility)
              FeaturedProjects(data?.projects ?? []),
            News(data?.blogs ?? []),
          ],
        ),
      ),
    );
  }

  Widget _topBar() {
    return Container(
      // height: MediaQuery.of(context).size.height * .07,
      // height: 55,
      padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 7.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: <Widget>[
          Row(
            children: <Widget>[
              GestureDetector(
                onTap: () {
                  scaffold.currentState!.openDrawer();
                },
                child: Icon(Icons.menu, color: Theme.of(context).primaryColor),
              ),
              Expanded(
                child: Center(
                  child: Text(
                    Config.HOME_TITLE,
                    style: Theme.of(context)
                        .textTheme
                        .headlineSmall!
                        .copyWith(fontSize: 24.0, fontWeight: FontWeight.bold, color: MyColors.primary),
                  ),
                ),
              ),
              GestureDetector(
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (BuildContext context) {
                      return PropertySelectionDialog(
                        fTap: () {
                          sendAnalyticsEvent(eventName: 'home_inventory_post_button_click');
                          Nav.back(context);
                          Nav.addPropertyPage(context);
                        },
                        sTap: () {
                          sendAnalyticsEvent(eventName: 'home_classified_post_button_click');
                          Nav.back(context);
                          Nav.addClassifiedProperty(context);
                        },
                        mTap: () {
                          sendAnalyticsEvent(eventName: 'home_market_inventory_post_button_click');
                          Nav.back(context);
                          Nav.addMarketInventory(context);
                        },
                      );
                    },
                  );
                },
                child: Icon(Icons.library_add, color: Theme.of(context).primaryColor),
              ),
            ],
          ),
          SizedBox(height: 5.0),
        ],
      ),
    );
  }
}
