import 'package:flutter/material.dart';

import '../../nav.dart';
import '../../utils/functions.dart';
import '../../utils/text.dart';
import '../properties_list/properties_hot_list.dart';
import 'home_heading_widget.dart';

class TopInventories extends StatelessWidget {
  const TopInventories({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return  Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HomeHeadingWidget(
          title: 'Hot Inventories 🔥🔥',
          onPress: (){
            Nav.propertiesPage(context);
            sendAnalyticsEvent(eventName: 'home_inventory_all_button_click');
          },
        ),
        Sized<PERSON>ox(height: 2),
        PropertiesHotList(),
        SizedBox(height: 2)
      ],
    );
  }
}
