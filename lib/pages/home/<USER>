import 'package:auto_size_text/auto_size_text.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../nav.dart';
import '../../theme.dart';
import '../../utils/functions.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import 'home_heading_widget.dart';

class News extends StatelessWidget {
  List news = [];
   News(this.news, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    return
      news.length < 1 ? Container() :
      Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        HomeHeadingWidget(
          title: 'Latest News',
          onPress: (){
            Nav.newsListPage(context);
            sendAnalyticsEvent(eventName: 'home_news_all_button_click');
          },
        ),
        SizedBox(height: 2),
        CarouselSlider(
          options: CarouselOptions(
            height: 160,
            viewportFraction: getNewsFraction(context),
            autoPlay: true,
            autoPlayInterval: Duration(seconds: 3),
            autoPlayAnimationDuration: Duration(milliseconds: 800),
            autoPlayCurve: Curves.fastOutSlowIn,
            enlargeCenterPage: false,
            scrollDirection: Axis.horizontal,
          ),
          items: news.map((i) {
            return Builder(
              builder: (BuildContext context) {
                return Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10),
                  child: TileCard(
                    onTap: () {
                      sendAnalyticsEvent(eventName: 'home_news_item_click', parameters: {'news_id': i!.id});
                      Nav.newsDetailPage(context, id: i.slug, banner: i.banner);
                    },
                    horizontal: 0,
                    vertical: 0,
                    elevation: 2,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Expanded(
                          child: Hero(
                            tag: i!.banner,
                            child: NetworkImageWithPlaceholder(i.banner, width: double.infinity, radius: 5),
                          ),
                        ),
                        Container(
                          color: MyColors.primary,
                          width: double.infinity,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                            child: Center(
                              child: AutoSizeText(
                                i.title,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                  fontSize: 16,
                                  color: Colors.white,
                                  // fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            );
          }).toList(),
        ),
      ],
    );
  }
}
