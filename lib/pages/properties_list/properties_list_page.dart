
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/theme.dart';
import 'package:property_dealers_directory/widgets/custom_ad_banner.dart';
import 'package:property_dealers_directory/widgets/filters_property.dart';
import 'package:provider/provider.dart';
import '../../controllers/city_selected.dart';
import '../../controllers/locationData_controller.dart';
import '../../controllers/login_controller.dart';
import '../../data/api.dart';
import '../../models/property_entity.dart';
import '../../nav.dart';
import '../../pages/properties_list/properties_hot_list.dart';
import '../../pages/properties_list/properties_single.dart';
import '../../services/addmob_services.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/sliver_list_base_state.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import '../../widgets/admob_widgets.dart';
import '../../widgets/badge_icon.dart';
import '../../widgets/not_available.dart';
import '../classified_property/calssified_select_filter.dart';
import '../classified_property/filters_provider.dart';
import 'app_bar_with_filters.dart';

class PropertiesListPage extends StatefulWidget {
  final int? id;
  final int? agencyId;
  String currentAd;
  final LoginController login = Get.find();
  final bool isFav;

  PropertiesListPage({this.id = 0, this.agencyId = 0, this.currentAd = "0", this.isFav = false});

  @override
  _PropertiesListPageState createState() => _PropertiesListPageState();
}

class _PropertiesListPageState extends SliverListBaseState<PropertiesListPage, List<PropertyEntity>> {

  @override
  String get title => isMyProperties ? 'My Inventories' : 'Inventories';
  bool get isMyProperties => widget.id == widget.login.staff?.id;
  void applyFilters(){
    if(pullToRefresh)
      refreshController.requestRefresh();
    else {
      getData();
    }
  }

  @override
  Widget get customAppBar => AppBarWithFilters(
    type: 'inventory',
    title: title,
    applyFilters: applyFilters,
    filtersProvider: context.read<FiltersProvider>(),
  );

  @override
  bool get paginate => true;

  @override
  bool get showAd => true;

  @override
  bool get withAppBar => false;

  @override
  double get expandedHeight => 110;

  double get getTopAdditionalForAd => AdMobService.canDisplay(AdMobService.LOCATION_INVENTORY_TOP) ? 50 : 0;

  @override
  Widget? get header => null;

  @override
  Widget listItem(index) {
    PropertyEntity property = data![index];
    return Column(
      key: Key('property_list_item$index'),
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if(index == 0)
          CustomAdBanner(position: AdBannerPosition.inv_top),
        Padding(
          padding: const EdgeInsets.all(8.0),
          child: PropertiesSingle(
            property: property,
            query: context.read<FiltersProvider>().query,
            refreshData: () => setState(() {
              getData();
            }),
            deleted: () => setState(() {
              data!.removeAt(index);
            }),
            showSnack: (text) => Notify.showSnack(context, text),
          ),
        ),
        if( (index + 1) % 5 == 0 && AdMobService.canDisplay(AdMobService.LOCATION_INVENTORY_LIST))
          // Center(child: AdmobBanner(size: AdSize.largeBanner, location: AdMobService.LOCATION_INVENTORY_LIST)),
          CustomAdBanner(key: Key('inv_list_banner$index'),position: AdBannerPosition.inv_list),
      ],
    );
  }

  @override
  void getData() {
    Map search = context.read<FiltersProvider>().searchInventory;
    if (widget.id == 0 && widget.agencyId == 0 && widget.currentAd == "0"){
      search['city_id'] = search['city_id'] ?? obProvider.city.id;
    }
    if (widget.currentAd != "0") {
      search['ad_id'] = widget.currentAd;
    }
    API.getProperties(id: widget.id, agencyId: widget.agencyId, page: page, extra: search).then((v) {
      handleResponse(v);
      //TODO add a debouncer for the analytics and send batched impressions events on the ad visible.
      // Remove Impression Analytics
      // List<int> propertyIdList = [];
      // if (v.data != null) {
      //   for (var property in v.data!) propertyIdList.add(property.id);
      //   if (propertyIdList.isNotEmpty) {
      //     Map<String, dynamic> body = {};
      //     body['id'] = propertyIdList.toString().replaceFirst("[", "").replaceFirst("]", "");
      //     body['action'] = "Impression";
      //     body['model'] = "property";
      //     API.postAnalytics(body);
      //     propertyIdList = [];
      //   }
      // }
    });
    sendAnalyticsScreenEvent(screenName: 'InventoryListPage');
  }

  @override
  Widget get notFound => SingleChildScrollView(
        child: Container(
          child: NotAvailable(
            'No ad found for your criteria',
            title: 'Not Found',
            buttonText: 'Submit Your ad',
            callback: (BuildContext _context) {
              Nav.addPropertyPage(_context);
            },
          ),
        ),
      );
}
