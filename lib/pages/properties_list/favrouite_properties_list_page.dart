import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/nav.dart';
import 'package:property_dealers_directory/utils/sliver_list_base_state.dart';

import '../../controllers/favourite_property_controller.dart';
import '../../data/api.dart';
import '../../models/property_entity.dart';
import '../../utils/Notify.dart';
import '../../widgets/not_available.dart';
import 'properties_single.dart';

class FavouritePropertiesListPage extends StatefulWidget {
  const FavouritePropertiesListPage({Key? key}) : super(key: key);

  @override
  State<FavouritePropertiesListPage> createState() => _FavouritePropertiesListPageState();
}

class _FavouritePropertiesListPageState extends SliverListBaseState<FavouritePropertiesListPage, List<PropertyEntity>> {
  FavouritePropertyController favController = Get.find();

  @override
  String get title => "Favourite Inventories";

  @override
  bool get paginate => true;

  @override
  void getData() {
    API.getFavrouitePropertyList(favController.ids.isNotEmpty ? favController.ids : 0, page).then(this.handleResponse);
  }

  @override
  Widget listItem(int index) {
    PropertyEntity property = data![index];
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: PropertiesSingle(
        property: property,
        deleted: () => setState(() {
          data!.removeAt(index);
        }
        ),
        showSnack: (text) => Notify.showSnack(context, text),
      ),
    );
  }

  @override
  Widget get notFound => NotAvailable(
        'Go ahead and favourite some ads for future',
        title: 'No Favourite ad found',
        buttonText: 'Inventory list',
        callback: (BuildContext _context) {
          Nav.propertiesPage(_context, isFav: true);
        },
      );
}
