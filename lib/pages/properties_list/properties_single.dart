import 'dart:math' as math;

import 'package:auto_size_text/auto_size_text.dart';
import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:highlight_text/highlight_text.dart';
import 'package:path_provider/path_provider.dart';
import 'package:property_dealers_directory/controllers/feedback_controller.dart';
import 'package:share/share.dart';
import 'package:sized_context/sized_context.dart';
import 'package:substring_highlight/substring_highlight.dart';

import '../../common/main_textField.dart';
import '../../controllers/favourite_property_controller.dart';
import '../../controllers/login_controller.dart';
import '../../data/api.dart';
import '../../models/property_entity.dart';
import '../../nav.dart';
import '../../pages/properties_list/properties_report_form.dart';
import '../../pages/properties_list/properties_single_heading.dart';
import '../../services/remote_config.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../../utils/helpers.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

// ignore: must_be_immutable
class PropertiesSingle extends StatefulWidget {
  final PropertyEntity property;
  final String? query;
  final VoidCallback? deleted;
  final VoidCallback? refreshData;
  final Function(String text)? showSnack;

  PropertiesSingle({Key? key, required this.property, this.query = '', this.deleted, this.showSnack, this.refreshData})
      : super(key: key);

  @override
  State<PropertiesSingle> createState() => _PropertiesSingleState();
}

class _PropertiesSingleState extends State<PropertiesSingle> {
  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();
  final LoginController login = Get.find();

  GetStorage storage = GetStorage();

  bool get isOwner => login.isLoggedIn && widget.property.staffId == login.staff!.id;

  Map<String, String> soldStatus = {
    'rent' : 'Rented',
    'sale' : 'Sold',
    'require' :'Done',
  };

  int click = 0;
  final showDateTime = DateTime.now();
  final newTime = DateTime.now().add(Duration(seconds: 5));

  final formKey = 'formKey';

  // final showFromNow = showDialogDateTime.add(Duration(seconds: 15));
  // DateTime date = Da;

   showFeedbackDialog() {
     FeedbackController feedback = Get.find();
     if(feedback.canShowFeedback){
       feedback.showDialog();
     }
  }

  final TextStyle textStyle = const TextStyle(
    color: Colors.red,
    fontSize: 14.0,
  );
  late Map<String , HighlightedWord> words;
  @override
  void initState() {
    super.initState();
    words = {
      widget.property.highlight(widget.query ?? '').trim(): HighlightedWord(
        textStyle: textStyle,
      ),
    };
  }

    @override
  Widget build(BuildContext context) {
    Color border = widget.property.isHot == 1 ? Colors.redAccent : Theme.of(context).primaryColor;
    double sWidth = MediaQuery.of(context).size.width;
    Map<String, dynamic> analyticsParameter = {
      'inventory_id': widget.property.id,
      'agency_id': widget.property.agencyId,
      'staff_id': widget.property.staffId,
      'agency_name': widget.property.agencyName,
      'staff_name': widget.property.staffName,
      'city_name': widget.property.cityName,
      'society_name': widget.property.societyName,
    };
    String? category = widget.property.category == 'all' ? 'property' : widget.property.category;
    return Builder(
      builder: (_context) => GestureDetector(
        onTap: () {
          Map<String, dynamic> body = {};
          body['id'] = widget.property.id;
          body['action'] = "Click";
          body['model'] = "property";
          API.postAnalytics(body);
          if (!isOwner) API.clickCounterOnProperty(widget.property.id, {});
          _openDialog(widget.property, _context);
          sendAnalyticsEvent(
              eventName: widget.property.isHot == 1 ? 'list_hot_inventory_item_click' : 'list_inventory_item_click',
              parameters: analyticsParameter);
        },
        child: TileCard(
          elevation: 5.0,
          horizontal: 0.0,
          vertical: 0.0,
          radius: 10.0,
          halfRadius: true,
          borderColor: border,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Container(
                child: Column(
                  mainAxisSize: widget.property.hotList! ? MainAxisSize.max : MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    widget.property.hotList!
                        ? PropertiesHeadingHot(property: widget.property)
                        : PropertiesHeadingNormal(property: widget.property, isOwner: isOwner),
                    if (widget.property.hotList!)
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(left: 10, right: 10, bottom: 5, top: 3),
                          child: AutoSizeText(
                            widget.property.note,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 6,
                            minFontSize: 11,
                            textAlign: TextAlign.left,
                          ),
                        ),
                      ),
                    if (!widget.property.hotList!)
                      SizedBox(
                        width: _context.widthPx,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 10, right: 10, bottom: 10, top: 3),
                          // child: TextHighlight(
                          //   text: widget.property.note,
                          //   words: words,
                          // ),
                          child: SubstringHighlight(
                            text: widget.property.note,
                            term: widget.property.highlight(widget.query ?? '').trim(),
                          ),
                        ),
                      ),
                    SizedBox(
                      height: 32.0,
                      child: Container(
                        decoration: BoxDecoration(border: Border.fromBorderSide(BorderSide(color: border))),
                        child: Row(
                          children: [
                            Flexible(
                              flex: 2,
                              child: Container(
                                color: border,
                                height: _context.heightPx,
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 10, top: 6.0, bottom: 6.0, right: 10.0),
                                  child: Center(
                                    child: widget.property.purpose != null
                                        ? LimitedBox(
                                            maxWidth: widget.property.isHot == 1
                                                ? (kIsWeb ? (sWidth <= 500 ? sWidth * .15 : sWidth * .04) : sWidth * .15)
                                                : sWidth * .4,
                                            child: FittedBox(
                                                child: widget.property.purpose != 'require'
                                                    ? Text(
                                                        widget.property.isHot == 1
                                                            ? '${widget.property.category} for ${widget.property.purpose}'.capitalize!
                                                            : '${widget.property.typeV2} ${widget.property.category} for ${widget.property.purpose}'
                                                                .capitalize!,
                                                        style: TextStyle(color: Colors.white))
                                                    : Text(
                                                        widget.property.isHot == 1
                                                            ? '${widget.property.purpose} $category'.capitalize!
                                                            : '${widget.property.purpose} ${widget.property.typeV2} $category'
                                                                .capitalize!,
                                                        style: TextStyle(color: Colors.white))))
                                        : Text(upperFirst(widget.property.type), style: TextStyle(color: Colors.white)),
                                  ),
                                ),
                              ),
                            ),
                            Flexible(
                              flex: 3,
                              child: Padding(
                                padding: const EdgeInsets.only(left: 10.0, right: 5.0),
                                child: SizedBox(
                                  child: Center(
                                    child: AutoSizeText(
                                      widget.property.block != null
                                          ? '${widget.property.block!.shortName}, \n${widget.property.societyName} - ${widget.property.cityName}'
                                          : '${widget.property.societyName} - ${widget.property.cityName}',
                                      textAlign: TextAlign.center,
                                      maxLines: 2,
                                      style: TextStyle(fontSize: 12),
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
              if (widget.property.status == "Sold")
                Opacity(
                  opacity: 0.8,
                  child: Transform.rotate(
                    angle: -math.pi / 4,
                    child: Text(
                      soldStatus[widget.property.purpose].toString(),
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: Colors.red.shade900,
                          fontFamily: 'Comfortaa',
                          fontSize: 40, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _openDialog(PropertyEntity property, context) {
    FavouritePropertyController favController = Get.find();
    Map<String, dynamic> analyticsParameter = {
      'inventory_id': property.id,
      'agency_id': property.agencyId,
      'staff_id': property.staffId,
      'agency_name': property.agencyName,
      'staff_name': property.staffName,
      'city_name': property.cityName,
      'society_name': property.societyName,
    };
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (_context) {
          return Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isOwner && (property.isHot == null || property.isHot == 0) && property.status == "Active")
                  ListTile(
                    title: Text('Make it hot'),
                    leading: Icon(Icons.whatshot),
                    onTap: () {
                      Get.back();
                      _makeItHot(property, context);
                      sendAnalyticsEvent(
                          eventName: 'list_my_inventory_makeHotAd_click', parameters: analyticsParameter);
                    },
                  ),
                if (isOwner && property.status == "Active")
                  ListTile(
                    title: Text('Refresh this ad'),
                    leading: Icon(Icons.refresh),
                    onTap: () async {
                      Get.back();
                      var result = await API.makeInventoryRefresh(property.id, {});
                      result.status!.status == ApiStatus.OK
                          ? widget.showSnack!('Inventory Ad Refreshed')
                          : widget.showSnack!(result.status!.message);
                      sendAnalyticsEvent(
                          eventName: 'list_my_inventory_refreshAd_click', parameters: {'inventory_id': property.id});
                    },
                  ),
                if (isOwner && property.status == "Active")
                  ListTile(
                    title: Text('Edit this ad'),
                    leading: Icon(Icons.edit),
                    onTap: () {
                      Get.back();
                      Nav.addPropertyPage(context, id: property.id);
                      sendAnalyticsEvent(
                          eventName: 'list_my_inventory_editAd_click', parameters: {'inventory_id': property.id});
                    },
                  ),
                if (isOwner && property.status == "Active")
                  ListTile(
                    title: Text('Delete ad'),
                    leading: Icon(Icons.delete_forever),
                    onTap: () {
                      Get.back();
                      _deleteItem(_context);
                      sendAnalyticsEvent(eventName: 'list_my_inventory_deleteAd_click', parameters: analyticsParameter);
                    },
                  ),
                if (isOwner && property.status == "Sold")
                  ListTile(
                    title: Text('Active ad'),
                    leading: Icon(Icons.restore_from_trash),
                    onTap: () {
                      Get.back();
                      _activeItem(_context);
                      sendAnalyticsEvent(eventName: 'list_my_inventory_activeAd_click', parameters: analyticsParameter);
                    },
                  ),
                if (property.staffPhone != null)
                  ListTile(
                    title: Text('Call the agent'),
                    subtitle: Text(property.staffPhone!),
                    leading: Icon(Icons.call),
                    onTap: () {
                      Map<String, dynamic> body = {};
                      body['id'] = property.id;
                      body['action'] = "Phone Call";
                      body['model'] = "property";
                      API.postAnalytics(body);
                      Get.back();
                      showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              title: Text("Alert"),
                              content: Text("Please Do Not Forget to Mention ilaaqa.com app when calling!"),
                              actions: [
                                CancelButton(),
                                ElevatedButton(
                                  child: Text("Call"),
                                  onPressed: () => openLink("tel:+92${property.staffPhone!.substring(1)}"),
                                )
                              ],
                            );
                          });
                      sendAnalyticsEvent(
                          eventName: 'list_my_inventory_callAgent_click', parameters: analyticsParameter);
                      showFeedbackDialog();
                    },
                  ),
                if (property.staffPhone != null)
                  ListTile(
                    title: Text('WhatsApp to the agent'),
                    leading: Icon(FontAwesomeIcons.whatsapp),
                    onTap: () {
                      Map<String, dynamic> body = {};
                      body['id'] = property.id;
                      body['action'] = "Whatsapp Call";
                      body['model'] = "property";
                      API.postAnalytics(body);
                      Get.back();
                      openWhatsApp(
                        property.staffPhone!,
                        message: '${property.title} \n ${RemoteConfigService.webUrl}/p/${property.id}',
                      );
                      // openLink(
                      //     'https://wa.me/92${property.staffPhone!.substring(1)}?text=${property.title} \n https://bahriaplus.com/p/${property.id}');
                      // print('https://wa.me/92${property.staffPhone!.substring(1)}?text=${property.title} \n https://bahriaplus.com/p/${property.id}');
                      sendAnalyticsEvent(
                          eventName: 'list_my_inventory_whatsAppAgent_click', parameters: analyticsParameter);
                        showFeedbackDialog();
                    },
                  ),
                ListTile(
                  title: Text('Goto agency profile'),
                  leading: Icon(Icons.home),
                  onTap: () {
                    Get.back();
                    Nav.agency(context, int.tryParse(property.agencyId));
                    sendAnalyticsEvent(
                        eventName: 'list_my_inventory_agencyProfile_click', parameters: analyticsParameter);
                  },
                ),
                ListTile(
                  title: Text(favController.isFav(property.id) ? 'Remove from favrouite' : "Add to favourite"),
                  leading: Icon(favController.isFav(property.id) ? Icons.favorite : Icons.favorite_border),
                  onTap: () {
                    Get.back();
                    if (favController.isFav(property.id)) {
                      favController.remove(property.id);
                      widget.deleted!();
                      Notify.toast("Property remove from favourite", type: 1);
                    } else {
                      favController.add(property.id);
                      Notify.toast("Property Added to favourite", type: 1);
                    }
                    sendAnalyticsEvent(eventName: 'list_my_inventory_favourite_click', parameters: analyticsParameter);
                  },
                ),
                ListTile(
                  title: Text('Copy text'),
                  leading: Icon(Icons.content_copy),
                  onTap: () {
                    Get.back();
                    Clipboard.setData(ClipboardData(text: property.noteCopy));
                    widget.showSnack!("Ad Copied to your Clipboard.");
                    sendAnalyticsEvent(eventName: 'list_my_inventory_copyText_click', parameters: analyticsParameter);
                  },
                ),
                if (!kIsWeb)
                  ListTile(
                      title: Text('Share Image'),
                      leading: Icon(Icons.share),
                      onTap: () async {
                        Get.back();
                        Notify.progress(context, 'Downloading...');
                        var tempDir = await getTemporaryDirectory();
                        String savePath = tempDir.path + '/${property.id}.jpg';
                        var response =
                            await Dio().download('${RemoteConfigService.storageUrl}fb-images/${property.id}.jpg?t=${property.updatedAt.hashCode}', savePath);
                        Notify.hide();
                        Share.shareFiles([savePath], text: "${RemoteConfigService.webUrl}/p/${property.id}");
                        sendAnalyticsEvent(
                            eventName: 'list_my_inventory_shareImage_click', parameters: analyticsParameter);
                      }),
                ListTile(
                  title: Text('Open this ad on website'),
                  leading: Icon(Icons.open_in_new),
                  onTap: () {
                    Get.back();
                    openLink(property.url);
                    sendAnalyticsEvent(
                        eventName: 'list_my_inventory_openAdWebsite_click', parameters: analyticsParameter);
                  },
                ),
                ListTile(
                  title: Text('Report this ad'),
                  leading: Icon(Icons.assistant_photo),
                  onTap: () {
                    Get.back();
                    showReportForm(property, _context);
                    sendAnalyticsEvent(eventName: 'list_my_inventory_reportAd_click', parameters: analyticsParameter);
                  },
                ),
              ],
            ),
          );
        });
  }

  void _makeItHot(PropertyEntity property, context) {
    showDialog(
      context: context,
      builder: (BuildContext _context) {
        return AlertDialog(
          title: const Text("Confirm"),
          content: const Text("Are you sure you want to make this ad hot?"),
          actions: <Widget>[
            TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                  Notify.progress(context, 'Please wait...');
                  API.makeItHotApi(property.id, {}).then((value) {
                    Notify.hide();
                    Notify.toast(value.data['message'], type: 1);
                  });
                },
                child: const Text("YES")),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text("NO"),
            ),
          ],
        );
      },
    );
  }

  void showReportForm(PropertyEntity property, context) {
    showModalBottomSheet(
        isScrollControlled: true,
        context: context,
        builder: (_context) {
          return Padding(
            padding: MediaQuery.of(_context).viewInsets,
            child: Container(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: PropertiesReportForm(property),
              ),
            ),
          );
        });
  }

  int gValue = 1;

  void _deleteItem(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Confirm"),
          content: StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text("Are you sure you wish to mark this item as sold?",
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 20),
                const Text("Is this property sold from ilaaqa.com app?"),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    SizedBox(
                      width: 100,
                      child: RadioListTile(
                        activeColor: Theme.of(context).primaryColor,
                        contentPadding: EdgeInsets.zero,
                        title: Text("Yes"),
                        value: 1,
                        groupValue: gValue,
                        onChanged: (dynamic v) => setState(() {
                          gValue = v;
                        }),
                      ),
                    ),
                    SizedBox(
                      width: 100,
                      child: RadioListTile(
                        activeColor: Theme.of(context).primaryColor,
                        contentPadding: EdgeInsets.zero,
                        title: Text("No"),
                        value: 0,
                        groupValue: gValue,
                        onChanged: (dynamic v) => setState(() {
                          gValue = v;
                        }),
                      ),
                    ),
                  ],
                )
              ],
            );
          }),
          actions: <Widget>[
            TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                  API.deleteProperty(widget.property.id, gValue);
                  // deleted!();
                  widget.refreshData!();
                  widget.showSnack!('Ad Sold');
                },
                child: const Text("SOLD")),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text("CANCEL"),
            ),
          ],
        );
      },
    );
  }

  void _activeItem(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Confirm"),
          content: const Text("Are you sure you wish to active this item?"),
          actions: <Widget>[
            TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                  API.activateProperty(widget.property.id);
                  widget.refreshData!();
                  widget.showSnack!('Ad Activated');
                },
                child: const Text("ACTIVE")),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text("CANCEL"),
            ),
          ],
        );
      },
    );
  }

  void _showDialog(context) {
    showDialog<void>(
      context: context,
      // barrierDismissible: true, // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        FeedbackController feedback = Get.find();
        return SimpleDialog(
          title: Text('Feedback'),
          children: [
            Container(
              height: MediaQuery.of(context).size.height * .7,
              child: SingleChildScrollView(
                child: FormBuilder(
                  key: _fbKey,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Name")),
                        FormBuilderTextField(
                          name: 'name',
                          keyboardType: TextInputType.name,
                          decoration: InputStyle(hint: "Enter name", icon: Icons.person),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Name is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Mobile #")),
                        FormBuilderTextField(
                          name: 'phone',
                          keyboardType: TextInputType.number,
                          decoration: InputStyle(hint: "Enter mobile", icon: Icons.phone),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Mobile is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Current City")),
                        FormBuilderTextField(
                          name: 'city',
                          keyboardType: TextInputType.text,
                          decoration: InputStyle(hint: "Enter current city", icon: Icons.location_city),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'City is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Feedback")),
                        FormBuilderTextField(
                          name: 'feedback',
                          maxLines: 4,
                          keyboardType: TextInputType.text,
                          decoration: InputStyle(hint: "Enter message", icon: Icons.message),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Message is required'),
                          ]),
                        ),
                        const SizedBox(height: 20),
                        Center(child: PrimaryButton(text: "Submit", onPressed: () async{
                          if (_fbKey.currentState!.saveAndValidate()) {
                            Notify.progress(context, 'Please wait...');
                            APIResponse<dynamic> result = await API.feedbackFormSubmit(_fbKey.currentState!.value);
                            if (result.isOK) {
                              print(result);
                              Notify.hide();
                              Notify.toast('Request Submit Successfully', type: 1);
                              feedback.submit();
                              Nav.back(context);
                            } else {
                              Notify.hide();
                              Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
                            }
                          }
                        })),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: GestureDetector(
                              onTap: (){
                                Nav.back(context);
                              },
                              child: Text("Skip")),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
