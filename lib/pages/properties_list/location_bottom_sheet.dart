import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';

import '../../controllers/city_selected.dart';
import '../../controllers/locationData_controller.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../nav.dart';
import '../../pages/properties_list/custom_expansion_tile.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class LocationBottomSheet extends StatefulWidget {
  final Function? onSearch;

  const LocationBottomSheet({this.onSearch});

  @override
  _LocationBottomSheetState createState() => _LocationBottomSheetState();
}

class _LocationBottomSheetState extends State<LocationBottomSheet> with SingleTickerProviderStateMixin {
  TextEditingController queryC = TextEditingController();
  LocationDataController locationController = Get.find();
  GlobalKey<CustomExpansionTileState> _key = GlobalKey();

  CityEntity? city;
  int? cityId;
  int? societyId;
  int? blockId;
  String query = '';
  bool isSearch = true;

  List<CityEntity?> cities = [];
  List<CityEntity?> societies = [];
  List<BlockEntity?> blocks = [];

  @override
  void initState() {
    getCities();
    if (locationController.city == null) locationController.city = obProvider.city.id;
    if (locationController.currentCity != null) {
      societies = locationController.currentCity!.societies!;
    } else
      societies = obProvider.city.societies;
    if (locationController.society != null) blockFilter(locationController.society);

    super.initState();
  }

  getCities() async {
    APIResponse<List<CityEntity>> result = await API.getCitiesList();
    if (result.status!.status == ApiStatus.OK) {
      CityEntity all = CityEntity();
      all.id = 0;
      all.name = 'All';
      result.data?.forEach((e) {
        e.societies?.insert(0, all);
      });
      setState(() {
        cities = result.data!;
      });
    }
  }

  void blockFilter(id) {
    setState(() {
      blocks = locationController.societyBlocks = locationController.allBlocks
          .where((e) =>
              (e!.societyId == id || e.id == 0) &&
              (locationController.type != 'All' ? e.type == locationController.type : true))
          .toList();
    });
  }

  void searchPressed() {
    query = queryC.text.toString().toLowerCase();
    query = query.trim();
    if (isSearch)
      setState(() {
        if (query.isEmpty) {
          var list = (locationController.currentCity?.societies != null
              ? locationController.currentCity?.societies!
              : obProvider.city.societies)!;
          societies = list;
        } else {
          var list = (locationController.currentCity?.societies != null
              ? locationController.currentCity?.societies!
              : obProvider.city.societies)!;
          societies = list.where((text) => text!.name.toLowerCase().contains(query)).toList();
        }
      });
    if (blocks.isNotEmpty || locationController.society != null)
      setState(() {
        if (query.isEmpty) {
          blockFilter(locationController.society);
        } else {
          blocks = locationController.societyBlocks.where((text) => text!.name!.toLowerCase().contains(query)).toList();
        }
      });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return Stack(
      children: [
        Container(
            height: screenHeight * 0.9,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20),
                  Heading.big('Cities', font: 20),
                  SizedBox(height: 10),
                  Container(
                    height: screenHeight * .12,
                    width: screenWidth * .9,
                    child: ListView.builder(
                        shrinkWrap: true,
                        scrollDirection: Axis.horizontal,
                        itemCount: cities.length,
                        itemBuilder: (BuildContext context, i) {
                          CityEntity cityObj = cities[i]!;
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  locationController.currentCity = cityObj;
                                  city = cityObj;
                                  societies = cityObj.societies!;
                                  cityId = cityObj.id;
                                  locationController.city = cityId;
                                  locationController.currentSociety = null;
                                  locationController.society = 0;
                                  locationController.block = 0;
                                  societyId = null;
                                  blocks = [];
                                  locationController.showBlockSection = false;
                                });
                              },
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(Radius.circular(30)),
                                      color: (locationController.city == null
                                                  ? obProvider.city.id
                                                  : locationController.city) ==
                                              cityObj.id
                                          ? Colors.green
                                          : Colors.transparent,
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(2.0),
                                      child: NetworkImageWithPlaceholder(
                                        cityObj.image,
                                        height: 52.0,
                                        width: 52.0,
                                        circular: true,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    cityObj.name,
                                    style: TextStyle(
                                        fontSize: 18,
                                        color: (locationController.city == null
                                                    ? obProvider.city.id
                                                    : locationController.city) ==
                                                cityObj.id
                                            ? Colors.green
                                            : Theme.of(context).primaryColor),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                  ),
                  SizedBox(height: 10),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 5.0),
                    height: 42.0,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.grey[400]!,
                          width: 0.8,
                        ),
                        borderRadius: BorderRadius.circular(5.0)),
                    child: Row(
                      children: [
                        Expanded(
                          child: FormBuilderTextField(
                            textInputAction: TextInputAction.search,
                            name: 'query',
                            controller: queryC,
                            decoration: InputDecoration(hintText: 'Search society or block', border: InputBorder.none),
                            onChanged: (value) {
                              setState(() {
                                searchPressed();
                              });
                            },
                          ),
                        ),
                        InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: searchPressed,
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                            decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5.0)),
                            child: Icon(Icons.search, size: 18.0, color: Colors.white),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Expanded(
                    child: Scrollbar(
                      thumbVisibility: blocks.isEmpty ? false : true,
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Heading.big('Societies', font: 20),
                            societies.isNotEmpty
                                ? CustomExpansionTile(
                                    initiallyExpanded: true,
                                    onExpansionChanged: (v) {
                                      if (v) {
                                        setState(() {
                                          isSearch = true;
                                          societies = (locationController.currentCity?.societies != null
                                              ? locationController.currentCity?.societies!
                                              : obProvider.city.societies)!;
                                        });
                                      }
                                    },
                                    key: _key,
                                    textColor: Colors.green,
                                    iconColor: Colors.green,
                                    title: Text((locationController.currentSociety?.name != null
                                        ? locationController.currentSociety?.name
                                        : 'Select Society')!),
                                    children: [
                                      ListView.builder(
                                          physics: NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount:
                                              societies == null ? obProvider.city.societies.length : societies.length,
                                          itemBuilder: (BuildContext context, i) {
                                            CityEntity society =
                                                societies == null ? obProvider.city.societies[i]! : societies[i]!;
                                            return GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  isSearch = false;
                                                  societyId = society.id;
                                                  locationController.currentCity == null
                                                      ? locationController.currentCity = obProvider.city
                                                      : null;
                                                  locationController.currentSociety = society;
                                                  locationController.society = societyId;
                                                  locationController.block = 0;
                                                  blockFilter(societyId);
                                                  if (locationController.block == 0) locationController.currentBlock = BlockEntity();
                                                  locationController.showBlockSection = true;
                                                  _key.currentState!.toggle();
                                                });
                                                if (society.id == 0) {
                                                  locationController.currentBlock = null;
                                                  // Nav.back(context);
                                                  // widget.onSearch();
                                                }
                                              },
                                              child: Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7.5),
                                                child: Text(
                                                  society.name,
                                                  style: TextStyle(
                                                      fontSize: 18,
                                                      color: (locationController.society != null
                                                                  ? locationController.society
                                                                  : societyId) ==
                                                              society.id
                                                          ? Colors.green
                                                          : Colors.black),
                                                ),
                                              ),
                                            );
                                          })
                                    ],
                                  )
                                : Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(20),
                                      child: Text('Society not found.'),
                                    ),
                                  ),
                            SizedBox(height: 10),
                            locationController.showBlockSection
                                ? blocks != []
                                    ? Heading.big('Blocks', font: 20)
                                    : Container()
                                : Container(),
                            locationController.showBlockSection
                                ? blocks.isNotEmpty
                                    ? ListView.builder(
                                        physics: NeverScrollableScrollPhysics(),
                                        shrinkWrap: true,
                                        itemCount: blocks.length,
                                        itemBuilder: (BuildContext context, i) {
                                          BlockEntity block = blocks[i]!;
                                          return GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  blockId = block.id;
                                                  locationController.currentBlock = block;
                                                  locationController.block = block.id;
                                                  // Nav.back(context);
                                                  // widget.onSearch();
                                                });
                                              },
                                              child: Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7.5),
                                                child: Text(
                                                  block.name!,
                                                  style: TextStyle(
                                                      fontSize: 18,
                                                      color: (locationController.block != null
                                                                  ? locationController.block
                                                                  : blockId) ==
                                                              block.id
                                                          ? Colors.green
                                                          : Colors.black),
                                                ),
                                              ));
                                        })
                                    : Center(
                                        child: Padding(
                                          padding: const EdgeInsets.all(20),
                                          child: Text('Block not found.'),
                                        ),
                                      )
                                : Container(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )),
        Positioned(
            right: 10,
            bottom: 10,
            child: PrimaryButton(
                text: 'Apply Filter',
                onPressed: () {
                  Nav.back(context);
                  widget.onSearch!();
                }))
      ],
    );
  }
}
