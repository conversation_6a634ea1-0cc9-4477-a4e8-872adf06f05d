import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../data/api.dart';
import '../../models/property_entity.dart';
import '../../pages/properties_list/properties_single.dart';
import '../../utils/Notify.dart';
import '../../utils/base_state.dart';
import '../../utils/height_fraction_page.dart';

class PropertiesHotList extends StatefulWidget {
  @override
  _PropertiesHotListState createState() => _PropertiesHotListState();
}

class _PropertiesHotListState extends BaseState<PropertiesHotList, List<PropertyEntity?>> {
  @override
  bool get withScaffold => false;

  @override
  bool get pullToRefresh => false;

  @override
  bool get paginate => true;

  @override
  Widget body() {
    return CarouselSlider(
      options: CarouselOptions(
        height: getTopPropertiesHeight(context),
        viewportFraction: getTopPropertiesFraction(context),
        autoPlay: true,
        autoPlayInterval: Duration(seconds: 3),
        autoPlayAnimationDuration: Duration(milliseconds: 800),
        autoPlayCurve: Curves.fastOutSlowIn,
        enlargeCenterPage: false,
        scrollDirection: Axis.horizontal,
      ),
      items: data!.map((index) {
        return Builder(
          builder: (BuildContext context) {
            return Container(
              width: 280,
              padding: EdgeInsets.all(5.0),
              child: PropertiesSingle(
                property: index!,
                query: '',
                deleted: () => setState(() {
                  data!.remove(index);
                }),
                showSnack: (text) => Notify.showSnack(context, text),
              ),
            );
          },
        );
      }).toList(),
    );
  }

  @override
  void getData() {
    API.getHotProperties().then(this.handleResponse);
  }

  @override
  void dataLoadedCallback() {
    setState(() {
      data!.forEach((item) => item!.hotList = true);
      this.data!.shuffle();
    });
  }
}
