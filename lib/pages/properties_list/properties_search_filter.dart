import 'package:auto_size_text/auto_size_text.dart';
import 'package:find_dropdown/find_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';

import '../../common/main_textField.dart';
import '../../controllers/city_selected.dart';
import '../../controllers/locationData_controller.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../pages/properties_list/location_bottom_sheet.dart';

class PropertiesFilter extends StatefulWidget {
  final String? note;
  final Function? onSearch;

  PropertiesFilter({this.onSearch, this.note});

  @override
  _PropertiesFilterState createState() => _PropertiesFilterState();
}

class _PropertiesFilterState extends State<PropertiesFilter> {
  LocationDataController locationController = Get.find();
  String type = "All";
  String? purpose;
  String? category;

  List<BlockEntity?> blocks = [];
  List<dynamic>? categories;

  @override
  void initState() {
    getCategories();
    getBlocks();
    super.initState();
  }

  getBlocks() {
    API.blocksList(0, cityId: 0, category: "", inActive: "&inactive=1").then((value) {
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          blocks = value.data!;
          BlockEntity all = BlockEntity();
          all.id = 0;
          all.name = 'All';
          all.type = 'All';
          blocks.insert(0, all);
          locationController.allBlocks = blocks;
        });
    });
  }

  getCategories() async {
    APIResponse<dynamic> result = await API.getInventoryCategories();
    if (result.status!.status == ApiStatus.OK)
      setState(() {
        categories = result.data;
      });
  }

  pressSearch() {
    if (widget.onSearch != null) {
      widget.onSearch!({
        'city_id': locationController.city ?? obProvider.city.id,
        'society_id': locationController.society ?? 0,
        'block_id': locationController.block ?? 0,
        'type_v2': type != 'All' ? type.toLowerCase() : null,
        'purpose': purpose != 'All' ? purpose?.toLowerCase() : null,
        'category': category != 'All' ? category?.toLowerCase() : null,
        'note': widget.note == null ? null : widget.note?.toLowerCase(),
      });
    }
  }

  Stack mainContainer(item) {
    return Stack(
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 5.0),
          height: 30.0,
          decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(
                color: Colors.grey[400]!,
                width: 0.8,
              ),
              borderRadius: BorderRadius.circular(5.0)),
          child: Center(child: Text(item ?? '', style: TextStyle(fontSize: 11))),
        ),
        Positioned(right: 0, top: 4, child: Icon(Icons.arrow_drop_down))
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                flex: 2,
                child: FindDropdown(
                  label: "Purpose",
                  constraints: BoxConstraints.tight(MediaQuery.of(context).size * .3),
                  selectedItem: 'All',
                  showSearchBox: false,
                  labelStyle: TextStyle(fontSize: 12, color: Colors.white),
                  searchBoxDecoration: InputStyle(hint: 'Search Here'),
                  items: ["All", "Sale", "Rent", "Require"],
                  dropdownBuilder: (BuildContext context, dynamic item) {
                    return mainContainer(item);
                  },
                  onChanged: (dynamic value) {
                    purpose = value;
                    setState(() {});
                    pressSearch();
                  },
                ),
              ),
              SizedBox(width: 5),
              Expanded(
                flex: 2,
                child: FindDropdown(
                  label: "Category",
                  constraints: BoxConstraints.tight(MediaQuery.of(context).size * .6),
                  selectedItem: 'All',
                  labelStyle: TextStyle(fontSize: 12, color: Colors.white),
                  showSearchBox: false,
                  searchBoxDecoration: InputStyle(hint: 'Search Here'),
                  items: categories == null || categories!.isEmpty ? [] : categories!.map((e) => e).toList(),
                  dropdownBuilder: (BuildContext context, dynamic item) {
                    return mainContainer(item);
                  },
                  onChanged: (dynamic value) {
                    category = value;
                    setState(() {});
                    pressSearch();
                  },
                ),
              ),
              SizedBox(width: 5),
              Expanded(
                flex: 3,
                child: FindDropdown(
                  label: "Type",
                  constraints: BoxConstraints.tight(MediaQuery.of(context).size * .25),
                  selectedItem: 'All',
                  showSearchBox: false,
                  labelStyle: TextStyle(fontSize: 12, color: Colors.white),
                  searchBoxDecoration: InputStyle(hint: 'Search Here'),
                  items: ["All", "Residential", "Commercial"],
                  dropdownBuilder: (BuildContext context, dynamic item) {
                    return mainContainer(item);
                  },
                  onChanged: (dynamic value) {
                    type = value;
                    locationController.type = value;
                    locationController.currentBlock = null;
                    print(value);
                    setState(() {});
                    pressSearch();
                  },
                ),
              ),
            ],
          ),
          SizedBox(height: 5),
          Row(
            children: [
              Spacer(),
              LimitedBox(
                maxWidth: MediaQuery.of(context).size.width * .6,
                maxHeight: MediaQuery.of(context).size.height * .05,
                child: AutoSizeText(
                  (locationController.currentBlock?.name ?? "") +
                      (locationController.currentBlock?.name != null ? ", " : " ") +
                      (locationController.currentSociety?.name ?? "") +
                      (locationController.currentSociety?.name != null ? ", " : " ") +
                      (locationController.currentCity?.name != null
                          ? locationController.currentCity?.name
                          : ('Default City: ${obProvider.city.name}'))!,
                  maxLines: 2,
                  style: TextStyle(color: Colors.white),
                ),
              ),
              Spacer(),
              SizedBox(width: 5),
              locationController.currentCity?.name != null
                  ? GestureDetector(
                      onTap: () {
                        setState(() {
                          locationController.city = null;
                          locationController.society = null;
                          locationController.block = null;
                          locationController.currentCity = null;
                          locationController.currentSociety = null;
                          locationController.currentBlock = null;
                          locationController.societyBlocks = [];
                          locationController.type = type;
                          locationController.showBlockSection = false;
                          pressSearch();
                        });
                      },
                      child: Icon(Icons.close, color: Colors.white))
                  : Container(),
              SizedBox(width: 5),
              Container(
                height: 30.0,
                decoration: BoxDecoration(
                    color: Colors.white,
                    border: Border.all(
                      color: Colors.grey[400]!,
                      width: 0.8,
                    ),
                    borderRadius: BorderRadius.circular(5.0)),
                child: Center(
                  child: TextButton(
                    child: Text('Select block', style: TextStyle(color: Theme.of(context).primaryColor, fontSize: 10)),
                    style: ButtonStyle(backgroundColor: MaterialStateProperty.all<Color>(Colors.white)),
                    onPressed: () {
                      showModalBottomSheet(
                          isScrollControlled: true,
                          shape: RoundedRectangleBorder(
                              borderRadius:
                                  BorderRadius.only(topRight: Radius.circular(30), topLeft: Radius.circular(30))),
                          context: context,
                          builder: (context) {
                            return LocationBottomSheet(onSearch: pressSearch);
                          });
                    },
                  ),
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
