import 'package:flutter/material.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../utils/Notify.dart';

class InventoryProvider with ChangeNotifier {
  List<CityEntity?> societies = [];
  List<BlockEntity?> blocks = [];

  CityEntity city = CityEntity();
  CityEntity society = CityEntity();
  BlockEntity _defaultBlock = BlockEntity();
  BlockEntity block = BlockEntity();

  String query = '';
  String purpose = 'All';
  String type = 'All';
  String category = 'All';
  String size_min = '';
  String size_max = '';
  String size_unit = '';


  InventoryProvider(){
    block.id = 0;
    _defaultBlock.id = 0;
  }

  int get filterCounter{
    int countFilter = 0;
    if(query != '')
      countFilter ++;
    if(purpose != 'All')
      countFilter ++;
    if(type != 'All')
      countFilter ++;
    if(category != 'All')
      countFilter ++;
    if(size_min != '')
      countFilter ++;


    return countFilter;
  }

  int get inventoryFilterCounter{
    int countFilter = 0;
    if(query != '')
      countFilter ++;
    if(purpose != 'All')
      countFilter ++;
    if(type != 'All')
      countFilter ++;
    if(category != 'All')
      countFilter ++;
    if(size_min != '')
      countFilter ++;


    return countFilter;
  }


  String get searchString{
    String filter = '';
    if(city.id != null)
      filter += '&city_id=${city.id}';
    if(society.id != null)
      filter += '&society_id=${society.id}';
    if(block.id != 0)
      filter += '&block_id=${block.id}';

    if(purpose != 'All')
      filter += '&purpose=$purpose';
    if(type != 'All')
      filter += '&type=$type';
    if(category != 'All')
      filter += '&category=$category';
    if(query != '')
      filter += '&query=$query';

    //TODO add min, max, unit of size

    if(size_min != '')
      filter += '&size_min=$size_min';
    if(size_max != '')
      filter += '&size_max=$size_max';
    if(size_unit != '')
      filter += '&size_unit=$size_unit';

    return filter;
  }

  void reset(){
    query = '';
    purpose = 'All';
    type = 'All';
    category = 'All';
    size_min = '';
    size_max = '';
    size_unit = '';
    notifyListeners();
  }



  defaultBlock(BlockEntity blockEntity) {
    _defaultBlock = blockEntity;
    block = blockEntity;
    notifyListeners();
  }
  Future<void> citySelected(List<CityEntity> cities, String? _city) async {
    societies = [];
    society = CityEntity();
    blocks = [];
    block = _defaultBlock;
    city = cities.firstWhere((e) => e.name == _city);
    societies = cities.firstWhere((e) => e.name == _city).societies!;
    notifyListeners();
  }
  Future<void> societySelected(String? _society, String category) async {
    blocks = [];
    block = _defaultBlock;
    society = societies.firstWhere((e) => e?.name == _society)!;
    await getBlocks(category: category);
    notifyListeners();
  }

  Future<void> blockSelected(String? _block) async {
    block = blocks.firstWhere((e) => e?.name == _block)!;
    notifyListeners();
  }

  Future<void> getBlocks({category = ""}) async {
    var value = await API.blocksList(society.id, cityId: city.id, category: category);
    if (value.status!.status == ApiStatus.OK) {
      blocks = value.data!;
    } else {
      Notify.toast("Blocks ${value.status!.message}");
    }
  }

}