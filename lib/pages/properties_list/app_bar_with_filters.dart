import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:provider/provider.dart';

import '../../theme.dart';
import '../../utils/functions.dart';
import '../../widgets/badge_icon.dart';
import '../../widgets/filters_property.dart';
import '../classified_property/calssified_select_filter.dart';
import '../classified_property/filters_provider.dart';

class AppBarWithFilters extends StatelessWidget {
  const AppBarWithFilters({
    Key? key,
    required this.title,
    required this.applyFilters,
    required this.filtersProvider,
    this.type = 'inventory',
  }) : super(key: key);

  final String title;
  final String type;
  final VoidCallback applyFilters;
  final FiltersProvider filtersProvider;

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: MyColors.primary,
        borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10)),
      ),
      child: Column(
        children: [
          AppBar(
            elevation: 1,
            title: Padding(
              padding: const EdgeInsets.only(left: 10.0),
              child: Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(title),
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(left: 30, right: 10),
                      child: Container(
                        padding: EdgeInsets.only(left: 5.0, right: 0.0),
                        height: 35.0,
                        decoration: BoxDecoration(
                            color: Colors.white,
                            border: Border.all(
                              color: Colors.grey[400]!,
                              width: 0.8,
                            ),
                            borderRadius: BorderRadius.circular(5.0)),
                        child: Row(
                          children: [
                            Expanded(
                              child: FormBuilderTextField(
                                textInputAction: TextInputAction.search,
                                name: 'note',
                                decoration: InputDecoration(
                                    hintText: 'Search',
                                    border: InputBorder.none),
                                enableSuggestions: true,
                                onChanged: (value) {
                                  filtersProvider.setQuery(value);
                                },
                                onSubmitted: (value) {
                                  filtersProvider.setQuery(value);
                                  applyFilters();
                                },
                              ),
                            ),
                            InkWell(
                              splashColor: Colors.transparent,
                              highlightColor: Colors.transparent,
                              onTap: () {
                                applyFilters();
                                FocusScopeNode currentFocus =
                                    FocusScope.of(context);
                                if (!currentFocus.hasPrimaryFocus) {
                                  currentFocus.unfocus();
                                }
                                sendAnalyticsEvent(
                                    eventName:
                                        'list_${type}_search_button_click');
                              },
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    vertical: 8.0, horizontal: 8.0),
                                decoration: BoxDecoration(
                                    color: Theme.of(context).primaryColor,
                                    borderRadius: BorderRadius.circular(5.0)),
                                child: Icon(
                                  Icons.search,
                                  size: 18.0,
                                  color: Colors.white,
                                ),
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  BadgeIcon(
                    text: "Filters",
                    iconData: Icons.filter_list_outlined,
                    notificationCount: filtersProvider.filterCounter,
                    onTap: () async {
                      filtersProvider.saveOld(type);
                      await showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        isDismissible: true,
                        builder: (_context) {
                          return ListenableProvider.value(
                            value: filtersProvider,
                            child: Container(
                              height: 510,
                              child: SelectClassifiedFilters(),
                            ),
                          );
                        },
                      );
                      if (filtersProvider.isChanged(type)) {
                        filtersProvider.notifyListeners();
                        applyFilters();
                      }
                    },
                  ),
                ],
              ),
            ),
            titleSpacing: 0,
          ),
          FilterProperty(callback: applyFilters),
        ],
      ),
    );
  }
}
