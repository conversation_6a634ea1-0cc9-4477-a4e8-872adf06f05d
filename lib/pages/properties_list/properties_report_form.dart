import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/controllers/login_controller.dart';
import 'package:property_dealers_directory/data/api.dart';
import 'package:property_dealers_directory/models/property_entity.dart';
import 'package:property_dealers_directory/utils/Notify.dart';
import 'package:property_dealers_directory/utils/text.dart';
import 'package:property_dealers_directory/utils/widgets.dart';

class PropertiesReportForm extends StatefulWidget {
  final PropertyEntity property;

  PropertiesReportForm(this.property);

  @override
  _PropertiesReportFormState createState() => _PropertiesReportFormState();
}

class _PropertiesReportFormState extends State<PropertiesReportForm> {
  final LoginController login = Get.find();
  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return FormBuilder(
      key: _fb<PERSON><PERSON>,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Heading('Report Ad'),
          FormBuilderDropdown(
            name: 'reason',
            validator: FormBuilderValidators.compose([FormBuilderValidators.required()]),
            decoration: InputDecoration(labelText: 'Reason', icon: Icon(Icons.report_problem)),
            items: ['Already Sold', 'Inappropriate', 'Fake Coding', 'Other']
                .map((reason) => DropdownMenuItem(
                      value: reason,
                      child: Text("$reason"),
                    ))
                .toList(),
          ),
          if (!login.isLoggedIn)
            FormBuilderTextField(
              name: 'user_name',
              decoration: InputDecoration(labelText: 'Name', icon: Icon(Icons.person)),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(errorText: 'Name is required'),
                FormBuilderValidators.minLength(4, errorText: 'Name is too short'),
              ]),
            ),
          if (!login.isLoggedIn)
            FormBuilderTextField(
              name: 'phone',
              decoration: InputDecoration(labelText: 'Phone', icon: Icon(Icons.phone_iphone)),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(errorText: 'Phone is required'),
                FormBuilderValidators.minLength(11, errorText: 'Phone is too short'),
              ]),
            ),
          FormBuilderTextField(
            name: 'message',
            minLines: 5,
            maxLines: 10,
            decoration: InputDecoration(labelText: 'Message', icon: Icon(Icons.message)),
            validator: FormBuilderValidators.compose([
              FormBuilderValidators.required(errorText: 'Message is required'),
            ]),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: SizedBox(
              width: context.width / 1.5,
              child: PrimaryButton(
                onPressed: () {
                  if (_fbKey.currentState!.saveAndValidate()) {
                    var body = _fbKey.currentState!.value;
                    if (login.isLoggedIn) {
                      body['user_name'] = login.staff!.name;
                      body['phone'] = login.staff!.phone;
                      body['staff_id'] = login.staff!.id;
                    }
                    Get.back();
                    Notify.progress(context, 'Sending Report...');
                    API.reportProperty(widget.property.id, body).then((value) {
                      Notify.hide();
//                      Notify.showSnack(context, 'Ad reported.');
                      //TODO show snack
                    });
                  }
                },
                text: 'Submit',
              ),
            ),
          )
        ],
      ),
    );
  }
}
