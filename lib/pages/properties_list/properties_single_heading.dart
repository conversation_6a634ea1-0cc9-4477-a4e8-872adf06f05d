import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import '../../models/property_entity.dart';
import '../../services/remote_config.dart';
import '../../utils/widgets.dart';
import '../../widgets/counter_widgets.dart';

class PropertiesHeadingNormal extends StatelessWidget {
  final PropertyEntity property;
  final bool? isOwner;

  const PropertiesHeadingNormal({Key? key, required this.property, this.isOwner}) : super(key: key);

  convertToRupee() {
    var length = property.price.toString().length;
    if (length >= 6 && length <= 7) {
      return (property.price / 100000).toStringAsFixed(0) + ' Lakh';
    } else if (length >= 8) {
      return (property.price / 100000).toStringAsFixed(0) + ' Lakh';
    } else if (length >= 4 && length <= 5) {
      return (property.price / 1000).toStringAsFixed(0) + ' Thousand';
    } else {
      return property.price;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.fromLTRB(10.0, 2.0, 0.0, 2.0),
      title: Stack(
        children: [
          Text(property.agencyName),
          if (property.banner)
            Align(
              alignment: Alignment.topRight,
              child: Container(
                color: Colors.redAccent,
                child: Padding(
                  padding: const EdgeInsets.only(left: 10, top: 3, right: 10, bottom: 2),
                  child: Text(
                    'Titanium',
                    style: TextStyle(color: Colors.white, fontSize: 10, fontWeight: FontWeight.bold),
                  ),
                ),
              ),
            ),
        ],
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Stack(
            children: [
              Text(property.staffName),
              if (property.price != 0)
                Align(
                  alignment: Alignment.topRight,
                  child: Container(
                    color: property.isHot == 1 ? Colors.redAccent : Theme.of(context).primaryColor,
                    child: Padding(
                      padding: const EdgeInsets.all(4),
                      child: Text(
                        "RS. ${convertToRupee()}",
                        style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 5.5),
          Row(
            children: [
              Text(property.createdAgo == null ? '' : property.createdAgo!, style: TextStyle(fontSize: 10.0)),
              Spacer(),
              if (isOwner!)
                LimitedBox(
                  maxWidth: MediaQuery.of(context).size.width * 0.5,
                  child: Row(
                    children: [
                      Spacer(),
                      if (RemoteConfigService.showImpressions) ImpressionWidget(property: property),
                      if (RemoteConfigService.showClicks) ClickWidget(property: property),
                      PhoneClicks(property: property),
                      WhatsAppClicks(property: property),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
      leading: Padding(
        padding: const EdgeInsets.only(top: 0.0),
        child: NetworkImageWithPlaceholder(property.image, radius: 5.0, width: 50, height: 50),
      ),
    );
  }
}

class PropertiesHeadingHot extends StatelessWidget {
  final PropertyEntity property;

  const PropertiesHeadingHot({Key? key, required this.property}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 50,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: NetworkImageWithPlaceholder(property.image, circular: true, width: 25, height: 25),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 3),
                  child: AutoSizeText(
                    property.agencyName,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: Theme.of(context).textTheme.bodySmall!.copyWith(fontSize: 13),
                  ),
                ),
                Text(property.staffName, style: Theme.of(context).textTheme.bodySmall),
              ],
            ),
          )
        ],
      ),
    );
  }
}
