import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:image/image.dart' as wmImg;
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:provider/provider.dart';
import 'package:share/share.dart';

import '../../models/block_entity.dart';
import '../../models/plot_entity.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../plot_finder/feedback_dialog.dart';
import 'MapProvider.dart';
import 'maps_launcher_custom.dart';

class MapButtons extends StatefulWidget {
  final PlotEntity plot;
  final BlockEntity block;
  final GlobalKey? screenKey;

  MapButtons({Key? key, required this.plot, required this.block, this.screenKey}) : super(key: key);

  @override
  State<MapButtons> createState() => _MapButtonsState();
}

class _MapButtonsState extends State<MapButtons> {
  late PermissionStatus info;

  @override
  Widget build(BuildContext context) {
    final mapP = context.watch<MapProvider>();
    return Align(
      alignment: Alignment.bottomRight,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            child: ButtonDecoration(
              widget: IconButton(
                icon: Icon(Icons.camera_alt, size: 25, color: Colors.white),
                onPressed: mapP.changeMarker ? null : plotScreenShot,
                color: Theme.of(context).primaryColor,
              ),
            ),
          ),
          SizedBox(width: 10),
          ButtonDecoration(
            widget: IconButton(
              icon: Icon(Icons.feedback, size: 25, color: Colors.white),
              onPressed: () {
                showDialog<void>(
                  context: context,
                  barrierDismissible: true,
                  builder: (BuildContext dialogContext) {
                    return FeedbackDialog(id: widget.plot.id!);
                  },
                );
              },
            ),
          ),
          SizedBox(width: 10),
          ButtonDecoration(
            widget: IconButton(
              icon: Icon(FontAwesomeIcons.diamondTurnRight, size: 25, color: Colors.white),
              onPressed: () {
                String? mapLabel;
                if (!kIsWeb) {
                  mapLabel = "${widget.plot.title}, ${widget.block.name}, ${widget.plot.societyName.replaceAll(" -", ",")}";
                }
                // MapsLauncher.launchCoordinates(widget.plot.lat, widget.plot.long, mapLabel);
                MapsLauncherCustom.launchCoordinates(widget.plot.lat, widget.plot.long, mapLabel);
                sendAnalyticsEvent(
                    eventName: 'plot_locator_google_map_button_click', parameters: {'plot_id': widget.plot.id});
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _requestPermission() async {
    Map<Permission, PermissionStatus> statuses = await [Permission.photos].request();
    info = statuses[Permission.photos]!;
    print('permission:........$info');
    print(info.runtimeType);
  }

  void plotScreenShot() async {
    final mapP = context.read<MapProvider>();
    // await _requestPermission();
    // if (info == PermissionStatus.permanentlyDenied || info == PermissionStatus.denied) {
    //   mapP.updateMarker(false);
    //   Notify.toast('Give the permission of storage to BahriaPlus app in Settings to save Screenshot.');
    //   return;
    // }
    mapP.updateMarker(true);
    Future.delayed(Duration(milliseconds: 50)).then((_) => saveScreenShot());
  }

  saveScreenShot() async {
    final mapP = context.read<MapProvider>();
    // if (info == PermissionStatus.granted) {
      RenderRepaintBoundary boundary = widget.screenKey?.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final directory = await getTemporaryDirectory();
      File imgFile = File('${directory.path}/screenshot.png');
      imgFile.writeAsBytesSync(byteData!.buffer.asUint8List());
      //================================WaterMark Code=================================//
      // File watermark = await getImageFileFromAssets('bahriaplus_watermark.png');
      // wmImg.Image? originalImage = wmImg.decodeImage(byteData.buffer.asUint8List());
      // wmImg.Image? watermarkImage = wmImg.decodeImage((watermark.readAsBytesSync()));
      // wmImg.Image imageW = wmImg.Image(width: originalImage!.width, height: originalImage.height);
      // wmImg.compositeImage(imageW, originalImage!);
      // final int positionX = (originalImage.width - watermarkImage!.width) ~/ 2;
      // final int positionY = (originalImage.height - watermarkImage!.height) ~/ 2;
      // wmImg.compositeImage(imageW, watermarkImage, dstX: positionX, dstY: positionY);
      // final File watermarkedFile = File('${(await getTemporaryDirectory()).path}/screenshot2.png');
      // await watermarkedFile.writeAsBytes(wmImg.encodeJpg(originalImage));
      //===================================End========================================//
      Uint8List bytes = imgFile.readAsBytesSync();
      // final exDirectory = await getExternalStorageDirectory();
      // final myImagePath = '${exDirectory?.parent.parent.parent.parent.path}/BahriaPlus';
      // await Directory(myImagePath).create();
      // File("$myImagePath/${DateTime.now()}.jpg")..writeAsBytesSync(bytes);
      await ImageGallerySaverPlus.saveImage(bytes, quality: 100);
      mapP.updateMarker(false);
      Notify.customDialog(
        context,
        title: "ScreenShot Saved",
        desc: "Screenshot saved to gallery Pictures Folder.",
        type: DialogType.success,
        btnCancel: TextButton(
          child: new Text("OK"),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        btnOk: ElevatedButton(
          child: new Text("Share"),
          onPressed: () async {
            Navigator.of(context).pop();
            // SharePlus.shareFiles([imgFile.path]);
            Share.shareFiles([imgFile.path]);
          },
        ),
      );
      return;
    // }
  }

  Future<File> getImageFileFromAssets(String path) async {
    final byteData = await rootBundle.load('images/$path');
    final file = File('${(await getTemporaryDirectory()).path}/$path');
    await file.writeAsBytes(byteData.buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));
    return file;
  }
}

class ButtonDecoration extends StatelessWidget {
  const ButtonDecoration({Key? key, required this.widget}) : super(key: key);

  final Widget widget;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Container(
          height: 40,
          width: 40,
          decoration: BoxDecoration(color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5)),
          child: widget,
        ),
      ],
    );
  }
}
