
import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import '../../common/main_textField.dart';
import '../../models/plot_entity.dart';

class PlotDetailsContainer extends StatelessWidget {
  const PlotDetailsContainer({Key? key, required this.plot, required this.blockName}) : super(key: key);

  final PlotEntity plot;
  final String blockName;
  @override
  Widget build(BuildContext context) {
    List<Color> featureClr = [
      const Color(0xFFff6666),
      const Color(0xFF007f5c),
      const Color(0xFF5f65d3),
      const Color(0xFF19ca21),
      const Color(0xFF60230b)
    ];
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(5),
        boxShadow: const [BoxShadow(color: Colors.black54, blurRadius: 6, spreadRadius: 2)],
      ),
      child: Column(
        children: [
          const SizedBox(height: 10),
          AutoSizeText(plot.societyName, style: const TextStyle(fontSize: 18, color: Colors.black)),
          Padding(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LimitedBox(
                  maxWidth: MediaQuery.of(context).size.width * .4,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FittedBox(child: LabelWithText(txt1: 'Plot No. ', txt2: plot.plotNo)),
                      const SizedBox(height: 10),
                      if (plot.streetType != 'None') LabelWithText(txt1: '${plot.streetType}: ', txt2: plot.streetNo),
                      if (plot.streetType != 'None') const SizedBox(height: 10),
                      FittedBox(child: LabelWithText(txt1: 'Block: ', txt2: blockName))
                    ],
                  ),
                ),
                LimitedBox(
                  maxWidth: MediaQuery.of(context).size.width * .4,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      FittedBox(
                        child: LabelWithText(
                          txt1: 'Category: ',
                          txt2: plot.category,
                          color: plot.category == 'Commercial' ? Colors.red : Colors.black,
                        ),
                      ),
                      const SizedBox(height: 10),
                      FittedBox(child: LabelWithText(txt1: 'Size: ', txt2: '${plot.plotSize} - ${plot.sizeType}')),
                      if(plot.roadWidth.isNotEmpty)
                        FittedBox(child: LabelWithText(txt1: 'Road Width: ', txt2: plot.roadWidth)),
                    ],
                  ),
                )
              ],
            ),
          ),
          if (plot.features!.isNotEmpty)
            Wrap(
              spacing: 8.0,
              children: [
                for (int i = 0; i < plot.features!.length; i++)
                  Chip(
                    label: AutoSizeText(
                      plot.features![i],
                      textAlign: TextAlign.center,
                      style: const TextStyle(fontSize: 12, color: Colors.white),
                    ),
                    backgroundColor: featureClr[i],
                    elevation: 3.0,
                    shadowColor: Colors.grey[60],
                  ),
              ],
            ),
        ],
      ),
    );
  }
}
