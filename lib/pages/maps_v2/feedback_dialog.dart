import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

import '../../common/main_textField.dart';
import '../../data/api.dart';
import '../../nav.dart';
import '../../utils/Notify.dart';

class FeedbackDialog extends StatelessWidget {
  const FeedbackDialog({Key? key, required this.id}) : super(key: key);

  final int id;

  @override
  Widget build(BuildContext context) {
    GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();
    return AlertDialog(
      title: Text('Feedback'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text("Do you find this information correct?"),
          Si<PERSON><PERSON><PERSON>(height: 10),
          FormBuilder(
            key: _fb<PERSON><PERSON>,
            child: FormBuilderTextField(
              name: 'note',
              maxLines: 5,
              decoration: InputStyle(hint: 'Enter reason'),
              validator: FormBuilderValidators.required(errorText: 'Note is required'),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(onPressed: () => Nav.back(context), child: Text("Cancel")),
        SizedBox(width: 40),
        TextButton(onPressed: () => _onPressed(_fbKey, "no", context), child: Text("No")),
        TextButton(onPressed: () => _onPressed(_fbKey, "yes", context), child: Text("Yes"))
      ],
    );
  }

  _onPressed(GlobalKey<FormBuilderState> _fbKey, String value, context) async {
    if (_fbKey.currentState!.saveAndValidate()) {
      Map<String, String?> body = {};
      body['feedback'] = value;
      body['note'] = _fbKey.currentState!.value['note'];
      APIResponse result = await API.feedBack(id, body);
      if (result.status!.status == ApiStatus.OK)
        Nav.back(context);
      else
        Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
    }
  }
}
