import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:property_dealers_directory/utils/text.dart';

import '../../data/api.dart';
import '../../utils/Notify.dart';
import '../../utils/widgets.dart';

class MapAccessRequest extends StatefulWidget {
  const MapAccessRequest({Key? key}) : super(key: key);

  @override
  _MapAccessRequestState createState() => _MapAccessRequestState();

  static openDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: MapAccessRequest(),
        );
      },
    );
  }
}

class _MapAccessRequestState extends State<MapAccessRequest> {
  GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  @override
  Widget build(BuildContext context) {
    return Container(
      child: FormBuilder(
        key: _fb<PERSON><PERSON>,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Heading('Request Access'),
            SizedBox(height: 10),
            Text('Your weekly limit has reached, Please fill the form below to request access to the map.'),

            FormBuilderTextField(
              name: 'phone',
              keyboardType: TextInputType.phone,
              decoration: InputDecoration(
                labelText: 'Phone',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
                FormBuilderValidators.numeric(),
                FormBuilderValidators.minLength(11),
                FormBuilderValidators.maxLength(11),
              ]),
            ),
            FormBuilderTextField(
              name: 'name',
              decoration: InputDecoration(
                labelText: 'Name',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
                FormBuilderValidators.minLength(3),
              ]),
            ),
            FormBuilderTextField(
              name: 'profession',
              decoration: InputDecoration(
                labelText: 'Profession',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
              ]),
            ),
            FormBuilderTextField(
              name: 'message',
              minLines: 3,
              maxLines: 5,
              decoration: InputDecoration(
                labelText: 'Message',
              ),
              validator: FormBuilderValidators.compose([
                FormBuilderValidators.required(),
              ]),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton(
                  onPressed: () async {
                    if (_fbKey.currentState!.saveAndValidate()) {
                      print(_fbKey.currentState!.value);
                      Notify.progress(context, 'Please Wait...');
                      var resp =
                      await API.sendMapAccessRequest(_fbKey.currentState!.value);
                      Notify.hide();
                      if (resp.isOK) {
                        Navigator.pop(context);
                        Notify.toast(resp.status?.message, type: 1);
                      } else {
                        Notify.toast(resp.status?.message);
                      }
                    }
                  },
                  child: Text('Send Request'),
                ),
                CancelButton(),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
