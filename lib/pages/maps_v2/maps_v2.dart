import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:latlong2/latlong.dart';
import 'package:lottie/lottie.dart' hide Marker;
import 'package:property_dealers_directory/models/plot_entity.dart';
import 'package:property_dealers_directory/pages/maps_v2/map_filters.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:provider/provider.dart';

import '../../controllers/city_selected.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../nav.dart';
import '../../pages/maps_v2/MapProvider.dart';
import '../../pages/maps_v2/cached_network_tile_provider.dart';
import '../../services/addmob_services.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import 'map_buttons.dart';
import 'plot_details_container.dart';

class MapsV2 extends StatefulWidget {
  final int? societyId;
  final int? blockId;
  const MapsV2({this.societyId, this.blockId, Key? key}) : super(key: key);

  @override
  State<MapsV2> createState() => _MapsV2State();
}

class _MapsV2State extends BaseState<MapsV2,BlockEntity?> with TickerProviderStateMixin {
  final MapController _mapController = MapController();
  final GlobalKey screenKey = GlobalKey();

  late AnimationController _controller;
  late CurvedAnimation _animation;

  late AnimationController _classifiedController;
  late CurvedAnimation _classifiedAnimation;

  BlockEntity block = BlockEntity();
  PlotEntity plot  = PlotEntity();

  @override
  bool get showAd => false;

  @override
  bool get pullToRefresh => false;

  @override
  bool get withAppBar => false;

  @override
  void getData() {
    API.defaultBlock(obProvider.city.id!).then(this.handleResponse);
    _controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 750));
    _animation = CurvedAnimation(parent: _controller, curve: Curves.elasticOut);
    _controller.repeat(min: 0.15, max: 1.0, reverse: true);
    _classifiedController = AnimationController(vsync: this, duration: const Duration(milliseconds: 2000));
    _classifiedAnimation = CurvedAnimation(parent: _classifiedController, curve: Curves.easeInOut);
    _classifiedController.repeat(min: 0.3, max: 1.0, reverse: true);
  }

  @override
  void initState() {
    super.initState();
    _mapController.mapEventStream.listen((event) {
      context.read<MapProvider>().updateZoom(event.camera.zoom);
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _mapController.dispose();
    _animation.dispose();
    _classifiedController.dispose();
    _classifiedAnimation.dispose();
    super.dispose();
  }

  @override
  void dataLoadedCallback() {
    super.dataLoadedCallback();
    setState(() {
      block = data!;
      context.read<MapProvider>().defaultBlock(data!);
    });
  }

  @override
  Widget body() {
    final mapP = context.watch<MapProvider>();
    final width = MediaQuery.of(context).size.width;
    return SafeArea(
      child: RepaintBoundary(
        key: screenKey,
        child: Stack(
          children: [
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height * .95,
              child: FlutterMap(
                mapController: _mapController,
                options: MapOptions(
                    interactionOptions: const InteractionOptions(
                        flags: InteractiveFlag.all & ~InteractiveFlag.rotate),
                  minZoom: mapP.block.minZoom.toDouble(),
                  maxZoom: mapP.block.maxZoom.toDouble(),
                  initialZoom: mapP.zoom,
                  initialCenter: mapP.center
                ),
                children: [
                  TileLayer(
                    urlTemplate: mapP.baseMapUrl,
                    tileProvider: CachedNetworkTileProvider(),
                  ),
                  if(!mapP.hasMultipleMapBlocks)
                  TileLayer(
                    // opacity: 1,
                    urlTemplate: "${mapP.block.mapUrl}/{z}/{x}/{y}.png",
                    minZoom: mapP.block.minZoom.toDouble(),
                    maxZoom: mapP.block.maxZoom.toDouble(),
                    tms: false,
                    // backgroundColor: Colors.white.withOpacity(0),
                    tileProvider: CachedNetworkTileProvider(),
                    tileBuilder: (context, tile, tileInfo) {
                      return Opacity(
                        opacity: mapP.opacity,
                        child: tile,
                      );
                    },
                    tileBounds: LatLngBounds(
                      LatLng(mapP.block.extentX2.toDouble(), mapP.block.extentY2.toDouble()),
                      LatLng(mapP.block.extentX1.toDouble(), mapP.block.extentY1.toDouble()),
                    ),
                  ),
                  if(mapP.hasMultipleMapBlocks)
                    for (var i = 0; i < mapP.mapBlocks.length; i++)
                      TileLayer(
                        urlTemplate: "${mapP.mapBlocks[i].mapUrl}/{z}/{x}/{y}.png",
                        minZoom: mapP.mapBlocks[i].minZoom.toDouble(),
                        maxZoom: mapP.mapBlocks[i].maxZoom.toDouble(),
                        tms: false,
                        tileProvider: CachedNetworkTileProvider(),
                        tileBuilder: (context, tile, tileInfo) {
                          return Opacity(
                            opacity: mapP.opacity,
                            child: tile,
                          );
                        },
                        tileBounds: LatLngBounds(
                          LatLng(mapP.mapBlocks[i].extentX2.toDouble(), mapP.mapBlocks[i].extentY2.toDouble()),
                          LatLng(mapP.mapBlocks[i].extentX1.toDouble(), mapP.mapBlocks[i].extentY1.toDouble()),
                        ),
                      ),

                    MarkerLayer(
                      markers: [
                        if(mapP.isClassifiedMarkersVisible)
                          for (var i = 0; i < mapP.ads.length; i++)
                            Marker(
                              key: Key('classified_marker_${mapP.ads[i].id}'),
                              height: mapP.classifiedSize,
                              width: mapP.classifiedSize,
                              point: LatLng(mapP.ads[i].lat, mapP.ads[i].long),
                              child: GestureDetector(
                                onTap:(){
                                  sendAnalyticsEvent(
                                    eventName: "classifieds_marker_click",
                                    parameters: {
                                      "plot_id": mapP.plot.id.toString(),
                                      "classified_id": mapP.ads[i].id.toString()
                                    },
                                  );
                                  Map<String, dynamic> body = {};
                                  body['id'] = mapP.ads[i].id;
                                  body['action'] = "Map Click";
                                  body['model'] = "classified_property";
                                  API.postAnalytics(body);

                                  Nav.classifiedPropertyDetailPage(context, mapP.ads[i].id, '');
                                },
                                child: ScaleTransition(
                                  scale: mapP.ads[i].isHot ?
                                  _animation :
                                  AlwaysStoppedAnimation(0.8),
                                  child: Transform.translate(
                                      offset: const Offset(0.0, -25 / 2),
                                      child: Image.asset(
                                          'images/plot-icons/${mapP.ads[i].category}.png',
                                        height: 50,
                                        width: 50,
                                        opacity:
                                        mapP.ads[i].isHot ?
                                        AlwaysStoppedAnimation(0.9) :
                                        AlwaysStoppedAnimation(0.6),
                                      ),
                                  ),
                                ),
                              ),
                            ),
                        if(mapP.isPlotLoaded && !mapP.changeMarker)
                          Marker(
                            height: 100,
                            width: 100,
                            point: LatLng(mapP.plot.lat, mapP.plot.long),
                            alignment: Alignment.lerp(Alignment.center, Alignment.topCenter, 0.5),
                            child: Lottie.asset("animations/location_marker_map.json"),
                          ),
                        if(mapP.isPlotLoaded && mapP.changeMarker)
                          Marker(
                          height: 100,
                          width: 100,
                          point: LatLng(mapP.plot.lat, mapP.plot.long),
                          child: Transform.translate(
                            offset: const Offset(0.0, -70 / 2),
                            child: Icon(
                              Icons.location_pin,
                              color: Theme.of(context).colorScheme.primary,
                              size: 70,
                            ),
                          ),
                        )
                    ],
                    ),
                ],
              ),
            ),
            Positioned(top: 0, child: MapFilters(zoom: mapP.zoom, mapController: _mapController)),
            Positioned(
              child: Center(
                child: Visibility(
                  visible: mapP.changeMarker,
                  child: IgnorePointer(
                    child: Padding(
                      padding: const EdgeInsets.all(30.0),
                      child: Image.asset("images/watermark.png"),
                    ),
                  ),
                ),
              ),
            ),
            if(mapP.isPlotLoaded)
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: width < 768 ? width : 450,
                  child: Stack(
                    children: [
                      Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only( right: 20, left: 20),
                            child: MapButtons(plot: mapP.plot, block: mapP.block, screenKey: screenKey),
                          ),
                          const SizedBox(height: 10),
                          Padding(
                            padding: const EdgeInsets.only(bottom: 20, right: 20, left: 20),
                            child: PlotDetailsContainer(plot: mapP.plot, blockName: mapP.block.shortName),
                          ),
                        ],
                      ),
                      Positioned(
                        top: 38,
                        left: 8,
                        child: GestureDetector(
                            onTap: (){
                              mapP.clearPlot();
                            },
                            child: Icon(Icons.cancel_outlined)),),
                    ],

                  ),
                ),
              )
          ],
        ),
      ),
    );
  }
}