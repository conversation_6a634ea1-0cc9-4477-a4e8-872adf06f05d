// library maps_launcher;

import 'dart:io' show Platform;

import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';

class MapsLauncherCustom {

  static String createQueryUrl(String query) {
    var uri;

    if (kIsWeb) {
      uri = Uri.https(
          'www.google.com', '/maps/search/', {'api': '1', 'query': query});
    } else if (Platform.isAndroid) {
      uri = Uri(scheme: 'geo', host: '0,0', queryParameters: {'q': query});
    } else if (Platform.isIOS) {
      uri = Uri.https(
          'www.google.com', '/maps/search/', {'api': '1', 'query': query});
      // uri = Uri.https('maps.apple.com', '/', {'q': query});
    } else {
      uri = Uri.https(
          'www.google.com', '/maps/search/', {'api': '1', 'query': query});
    }

    return uri.toString();
  }
  static Future<Uri> createCoordinatesUrl(double latitude, double longitude,
      [String? label]) async {
    Uri uri;

    if (kIsWeb) {
      uri = Uri.https('www.google.com', '/maps/search/',
          {'api': '1', 'query': '$latitude,$longitude'});
    } else if (Platform.isAndroid) {
      var query = '$latitude,$longitude';

      if (label != null) query += '($label)';

      uri = Uri(scheme: 'geo', host: '0,0', queryParameters: {'q': query});
    } else if (Platform.isIOS) {
      uri = await createIOSUri(latitude,longitude, label);
    } else {
      uri = Uri.https('www.google.com', '/maps/search/',
          {'api': '1', 'query': '$latitude,$longitude'});
    }

    return uri;
  }

  static Future<bool> launchQuery(String query) {
    return launch(createQueryUrl(query));
  }
  static Future<bool> launchCoordinates(double latitude, double longitude, [String? label]) async{
    try {
      return launchUrl(await createCoordinatesUrl(latitude, longitude, label));
    }catch(e){
      return false;
    }
  }

  static Future<Uri> createIOSUri(double lat, double long, String? label) async {

    String query = '$lat,$long';
    if (label != null) query += '($label)';

    String googleUrl = 'comgooglemaps://?q=$query';
    String appleUrl = 'https://maps.apple.com/?sll=$lat,$long';
    if (await canLaunchUrl(Uri.parse("comgooglemaps://"))) {
      print('launching com googleUrl');
      return Uri.parse(googleUrl);
    }
    else if (await canLaunchUrl(Uri(scheme: 'maps'))) {
      print('launching apple url');
      var params = {'ll': '$lat,$long'};
      if (label != null) params['q'] = label;
      return Uri(scheme: 'maps', queryParameters: params);
    }
    else {
      return Uri.https('www.google.com', '/maps/search/',{'api': '1', 'query': '$lat,$long'});
    }
  }

  static Future<void> openMap(double latitude, double longitude) async {
    String googleUrl = 'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude';
    if (await canLaunch(googleUrl)) {
      await launch(googleUrl);
    } else {
      throw 'Could not open the map.';
    }
  }
}
