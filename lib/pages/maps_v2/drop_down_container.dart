import 'package:flutter/material.dart';

class DropDownContainer extends StatelessWidget {
  const DropDownContainer({Key? key, this.item, required this.hint}) : super(key: key);

  final String? item;
  final String hint;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 30.0,
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey[400]!, width: 0.8),
        borderRadius: BorderRadius.circular(5.0),
      ),
      child: Center(
        child: FittedBox(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              const SizedBox(width: 5),
              Text(item ?? hint, style: const TextStyle(fontSize: 10)),
              const Icon(Icons.arrow_drop_down)
            ],
          ),
        ),
      ),
    );
  }
}
