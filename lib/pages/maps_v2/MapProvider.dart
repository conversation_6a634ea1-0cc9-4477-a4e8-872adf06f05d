import 'package:flutter/foundation.dart';
import 'package:latlong2/latlong.dart';
import 'package:property_dealers_directory/models/classified_short_entity.dart';
import 'package:property_dealers_directory/models/society_entity.dart';
import 'package:property_dealers_directory/services/remote_config.dart';

import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../models/plot_entity.dart';
import '../../utils/Notify.dart';

class MapProvider with ChangeNotifier {

  List<CityEntity> cities = [];

  List<CityEntity> societies = [];
  List<BlockEntity> blocks = [];
  List<PlotEntity> plots = [];

  CityEntity city = CityEntity();
  CityEntity society = CityEntity();
  BlockEntity _defaultBlock = BlockEntity();
  BlockEntity block = BlockEntity();
  PlotEntity plot = PlotEntity();

  bool changeMarker = false;
  double opacity = 1.0;
  bool isSatellite = false;

  String get baseMapUrl => isSatellite
      ? RemoteConfigService.satelliteUrl
      : 'https://tile.openstreetmap.org/{z}/{x}/{y}.png';

  bool get isPlotLoaded => plot.id != null;
  double _zoom = 12.0;

  double get zoom {
    if (isPlotLoaded) return block.maxZoom.toDouble() - (plot.category == 'Commercial' ? 0.0 : 2.0);
    return _zoom; //block.minZoom.toDouble() + 1.0;
  }

  LatLng get center {
    if (this.isPlotLoaded) return LatLng(plot.lat.toDouble(), plot.long.toDouble());
    return LatLng(block.centerLatitude.toDouble(), block.centerLongitude.toDouble());
  }

  double get classifiedSize => currentZoom * 2;
  bool get isClassifiedMarkersVisible => ads.isNotEmpty && !changeMarker && currentZoom > 14.0;

  List<BlockEntity> mapBlocks = [];
  bool get hasMultipleMapBlocks => mapBlocks.length > 1 && RemoteConfigService.mapMultiForSociety;

  double currentZoom = 12.0;
  void updateZoom(double zoom) {
    if(currentZoom == zoom) return;
    currentZoom = zoom;
    notifyListeners();
  }
  List<ClassifiedMarker> ads = [];

  defaultBlock(BlockEntity blockEntity) {
    _defaultBlock = blockEntity;
    block = blockEntity;
    _zoom = block.minZoom + 1.0;
    notifyListeners();
  }

  Future<void> updateMarker(bool value) async {
    changeMarker = value;
    notifyListeners();
  }

  void setCities(List<CityEntity> _cities) {
    cities = _cities;
    notifyListeners();
  }

  Future<void> citySelected(CityEntity? _city) async {
    // societies = [];
    // society = CityEntity();
    // blocks = [];
    // plots = [];
    // plot = PlotEntity();
    city = _city ?? CityEntity();
    block = city.block ?? _defaultBlock;
    _zoom = block.minZoom + 2.0;
    societies = city.societies;
    mapBlocks = [];
    notifyListeners();
  }

  Future<void> societySelected(CityEntity? _society) async {
    blocks = [];
    // block = _defaultBlock;
    // plots = [];
    // plot = PlotEntity();
    society = _society ?? CityEntity();
    block = society.block ?? city.block ?? _defaultBlock;
    _zoom = block.minZoom + 2.0;
    if(_society != null && !society.isEmpty){
      print('SOCIETY UPDATED ${society.id} ${_society.id}');
      societyClassifieds();
      mapBlocks = [];
      getBlocks(category: 'Residential');
    }
    notifyListeners();
  }

  Future<void> blockSelected(BlockEntity? _block) async {
    plots = [];
    // plot = PlotEntity();
    block = _block ?? society.block ?? city.block ?? _defaultBlock;

    if(_block != null){
      _zoom = block.minZoom  + 4.0;
      getClassifieds();
      getPlots();
    } else {
      _zoom = block.minZoom + 2.0;
    }
    notifyListeners();
  }

  void setPlot(PlotEntity _plot) {
    plot = _plot;
    notifyListeners();
  }

  void clearPlot() {
    setPlot(PlotEntity());
  }

  void resetPlot(value) {

  }

  void setOpacity(double value) {
    opacity = value;
    notifyListeners();
  }

  void setSatellite(bool value) {
    isSatellite = value;
    if(value){
      opacity = 0.5;
    }
    notifyListeners();
  }

  // bool get isMarkerLottie => isPlotLoaded && isSearch && !isScreenshot;
  // bool get isMarkerNormal => isPlotLoaded && isSearch && isScreenshot;

  Future<void> getBlocks({category = ""}) async {
    var value = await API.blocksList(society.id, cityId: city.id, category: category);
    if (value.status!.status == ApiStatus.OK) {
      blocks = value.data!;
      if(blocks.length == 1){
        blockSelected(blocks.first);
      } else {
        blocks.forEach((element) {
          if(!mapBlocks.any((e) => e.mapUrl == element.mapUrl)){
            mapBlocks.add(element);
          }
        });
        notifyListeners();
      }
    } else {
      Notify.toast("Blocks ${value.status!.message}");
    }
  }

  Future<void> getPlots() async {
    var value = await API.getPlotList(block.id);
    if (value.status!.status == ApiStatus.OK) {
      plots = value.data!;
      notifyListeners();
    } else {
      Notify.toast("Plots ${value.status!.message}");
    }
  }

  getClassifieds(){
    ads = [];
    API.blockClassifieds(block.id).then((value) {
      ads = value.data!;
      notifyListeners();
    });
  }
  societyClassifieds(){
    ads = [];
    API.societyClassifieds(society.id).then((value) {
      ads = value.data!;
      notifyListeners();
    });
  }
}
