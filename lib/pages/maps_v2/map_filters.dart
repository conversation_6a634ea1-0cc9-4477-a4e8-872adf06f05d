import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:find_dropdown/find_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_progress_hud/flutter_progress_hud.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:property_dealers_directory/customized_lib/form_builder/dropdown_able.dart';
import 'package:property_dealers_directory/pages/maps_v2/map_access_request.dart';
import 'package:form_builder_extra_fields/form_builder_extra_fields.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/services/remote_config.dart';

import 'package:provider/provider.dart';

import '../../common/main_textField.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../models/plot_entity.dart';
import '../../my_app.dart';
import '../../services/admob_reward.dart';
import '../../theme.dart';
import '../../utils/Notify.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import 'MapProvider.dart';

class MapFilters extends StatefulWidget {
  final double? zoom;
  final MapController? mapController;

  const MapFilters({Key? key, this.zoom, this.mapController}) : super(key: key);

  @override
  State<MapFilters> createState() => _MapFiltersState();
}

class _MapFiltersState extends BaseState<MapFilters, List<CityEntity>> {
  final _formKey = GlobalKey<FormBuilderState>();

  String category = 'Residential';

  // List<BlockEntity> blocks = [];
  // List<PlotEntity> plots = [];
  // CityEntity? city;

  @override
  bool get pullToRefresh => false;

  @override
  bool get withScaffold => false;

  @override
  bool get showAd => false;

  @override
  Widget get pageLoader => SizedBox.shrink();

  @override
  void getData() {
    API.getCitiesListWithBlocks().then(this.handleResponse);
  }

  CityEntity initialCity = obProvider.city;
  CityEntity initialSociety = obProvider.onboarding.society;

  bool satellitePopupShown = false;

  @override
  void dataLoadedCallback() {
    setState(() {
      if(data != null){
        final mapP = context.read<MapProvider>();
        mapP.setCities(data!);
        mapP.city = data!.firstWhere((element) => element.id == initialCity.id);
        mapP.societies = context.read<MapProvider>().city.societies;

        if(!mapP.city.isEmpty && obProvider.isSocietySelected){
          initialSociety = mapP.societies.firstWhere((element) => element.id == initialSociety.id);
          mapP.societySelected(initialSociety);
          Future.delayed(Duration(milliseconds: 500), (){
            // _formKey.currentState!.fields['city_id']!.didChange(mapP.city);
            _formKey.currentState!.fields['society_id']!.didChange(mapP.society);
          });
        }
      }
    });
    super.dataLoadedCallback();
  }

  @override
  Widget body() {
    final mapP = context.watch<MapProvider>();
    final width =  MediaQuery.of(context).size.width;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            // height: 120,
            width: width < 768 ? width * .9 : 450,
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            decoration: BoxDecoration(
                color: Colors.white.withOpacity(0.8),
                borderRadius: BorderRadius.circular(5),
                boxShadow: const [BoxShadow(color: Colors.black54, blurRadius: 6, spreadRadius: 2)]),
            child: FormBuilder(
              key: _formKey,
              child: Column(
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Expanded(
                        child:
                        FormBuilderSearchableDropdown<CityEntity>(
                          popupProps: const PopupProps.modalBottomSheet(showSearchBox: true, searchDelay: Duration(milliseconds: 200)),
                          name: 'city_id',
                          items: mapP.cities,
                          initialValue: mapP.city,
                          enabled: mapP.cities.isNotEmpty,
                          onChanged: (v){
                            context.read<MapProvider>().citySelected(v);
                            moveMap();
                            _formKey.currentState!.fields['society_id']!.didChange(null);
                          },
                          decoration: InputStyle.tight(hint: 'Search city'),
                          compareFn: CityEntity.compareFn,
                          filterFn: CityEntity.filterFn,
                        ),
                      ),
                      const SizedBox(width: 10),
                       Expanded(
                         child:
                         FormBuilderSearchableDropdown<CityEntity>(
                           popupProps: PopupProps.modalBottomSheet(
                             showSearchBox: true,
                             itemBuilder: (context, item, selected) {
                               return ListTile(
                                 title: Text(item.name),
                               );
                             },
                             searchDelay: Duration(milliseconds: 200),
                           ),
                           name: 'society_id',
                           initialValue: mapP.society,
                           itemAsString: (item) => item.shortName,
                           items: mapP.societies,
                           selectedItem: mapP.society.get,
                           enabled: mapP.societies.isNotEmpty,
                           onChanged: (v){
                             context.read<MapProvider>().societySelected(v);
                             moveMap();
                             _formKey.currentState!.fields['block_id']!.didChange(null);
                           },
                           decoration: InputStyle.tight(hint: 'Select Society'),
                           compareFn: (item, value) => item.id == value.id,
                           filterFn: (c, filter) => c.name.toLowerCase().contains(filter.toLowerCase()),
                         ),
                       ),
                    ],
                  ),
                  const SizedBox(height: 5),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                       Expanded(
                         child:

                         FormBuilderSearchableDropdown<BlockEntity>(
                          popupProps: PopupProps.modalBottomSheet(
                              showSearchBox: true,
                              itemBuilder: (context, item, selected) {
                                return ListTile(
                                  title: Text(item.name),
                                );
                              },
                              searchDelay: Duration(milliseconds: 200)),
                          name: 'block_id',
                          items: mapP.blocks,
                          selectedItem: mapP.block.get,
                          enabled: mapP.blocks.isNotEmpty,
                          onChanged: (v) {
                            context.read<MapProvider>().blockSelected(v);
                            moveMap();
                            _formKey.currentState!.fields['plot_id']!.reset();
                            _formKey.currentState!.fields['plot_id']!.didChange(null);
                          },
                           itemAsString: (item) => item.shortName,
                          decoration: InputStyle.tight(hint: 'Select Block'),
                          compareFn: (item, value) => item.id == value.id,
                          filterFn: (c, filter) =>
                              c.name.toLowerCase().contains(filter.toLowerCase()),
                        ),
                      ),
                      const SizedBox(width: 10),
                       Expanded(
                         child:
                         FormBuilderSearchableDropdown<PlotEntity>(
                           popupProps: const PopupProps.modalBottomSheet(
                            showSearchBox: true,
                            searchDelay: Duration(milliseconds: 200),
                             searchFieldProps: TextFieldProps(
                               decoration: InputDecoration(
                                 hintText: 'Select or Search Plot',
                                 prefixIcon: Icon(Icons.search),
                               ),
                             ),
                          ),
                          name: 'plot_id',
                           items: mapP.plots,
                           enabled: mapP.plots.isNotEmpty,
                           decoration: InputStyle.tight(hint: 'Select Plot'),
                           // clearButtonProps: ClearButtonProps(
                           //   icon: Icon(Icons.close, color: Colors.black),
                           //   isVisible: true,
                           // ),
                           onChanged: (v) {
                             if(v == null){
                                mapP.clearPlot();
                             } else {
                               _submitClick(v);
                             }
                           },
                           // selectedItem: location.selectedPlot,
                           compareFn: (item, value) => item.id == value.id,
                           filterFn: (c, filter) => c.title.toLowerCase().contains(filter.toLowerCase()),
                         ),
                       ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          if(RemoteConfigService.isSatelliteEnabled)
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: SizedBox(
                    width: 40,
                    height: 40,
                    child: ElevatedButton(
                      onPressed: () {
                          setState(() {
                            satellitePopupShown = !satellitePopupShown;
                          });
                      },
                      child: const Icon(Icons.layers, color: MyColors.primary),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: mapP.isSatellite ? Colors.green : Colors.white.withOpacity(0.8),
                        padding: const EdgeInsets.all(0),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(100),
                        ),
                      ),
                    ),
                  ),
                ),
                if(satellitePopupShown)
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(color: Colors.grey[400]!, width: 0.8),
                      borderRadius: BorderRadius.circular(100.0),
                      color: Colors.white.withOpacity(0.8),
                    ),
                    height: 40,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        const Padding(
                          padding: EdgeInsets.only(left: 5),
                          child: Text("Satellite:"),
                        ),
                        Switch(
                          value: mapP.isSatellite,
                          onChanged: (value) {
                            mapP.setSatellite(value);
                            // widget.mapController?.move(mapP.center, mapP.mZoom);
                          },
                        ),
                        const SizedBox(width: 10),
                        Slider(
                          inactiveColor: Colors.grey,
                          value: mapP.opacity,
                          min: 0.0,
                          max: 1.0,
                          onChanged: (value) {
                            mapP.setOpacity(value);
                          },
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
        ],
      ),
    );
  }

  moveMap(){
    Future.delayed(Duration(milliseconds: 200), (){
      final mapP = context.read<MapProvider>();
      widget.mapController?.move(mapP.center, mapP.zoom);// TODO change to fitCamera for city,society & block for bounds
    });
  }
  Future<void> _submitClick(DropDownAble value) async {
    final mapP = Provider.of<MapProvider>(context, listen: false);
    mapP.clearPlot();
    ProgressHUD.of(context)!.show();
    APIResponse<PlotEntity> result = await API.getPlotDetail(mapP.block.id, value.itemId);
    ProgressHUD.of(context)!.dismiss();
    if (result.status!.status == ApiStatus.OK) {
      var plot = result.data as PlotEntity;
      mapP.setPlot(plot);
      widget.mapController?.move(mapP.center, mapP.zoom);
    } else {
      String token = result.rawResponse.data['token'];
      MyApp.fcm.getToken().then((String? token) {
        if (token == null) return;
        API.updateToken(token);
      });
      AwesomeDialog(
        context: context,
        title: 'Error',
        desc: result.status!.message,
        dialogType: DialogType.error,
        btnOk: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
          ),
          child: new Text("Earn Free Credit"),
          onPressed: () {
            sendAnalyticsEvent(eventName: 'map_plot_error_ok_click');
            Navigator.of(context).pop();
            Notify.progress(context, 'Loading Ad...');
            AdmobRewardService.loadAd(token, (){
              Notify.hide();
              if(AdmobRewardService.rewardedAd == null){
                Notify.toast('Cannot load ad, please try again later.');
                return;
              }
              AdmobRewardService.rewardedAd?.show(onUserEarnedReward: (AdWithoutView  ad, RewardItem reward) {
                sendAnalyticsEvent(eventName: 'map_plot_error_ad_reward');
                Notify.toast('You have received ${reward.amount} free plot searches.');
                _submitClick(value);
              });
            });
          },
        ),
        btnCancel: ElevatedButton(
          child: new Text("Request Access"),
          onPressed: () {
            sendAnalyticsEvent(eventName: 'map_plot_error_call_button_click');
            Navigator.of(context).pop();
            MapAccessRequest.openDialog(context);
          },
        ),
        headerAnimationLoop: false,
      )..show();
      // Notify.customDialog(context, desc: result.status!.message, type: DialogType. error);
    }
  }
}
