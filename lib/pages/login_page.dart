import 'dart:io';

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';

import '../common/common_button.dart';
import '../common/main_textField.dart';
import '../controllers/login_controller.dart';
import '../data/api.dart';
import '../nav.dart';
import '../utils/Notify.dart';
import '../utils/functions.dart';
import '../utils/text.dart';

class LoginPage extends StatefulWidget {
  final LoginController controller = Get.find();

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _fbKey = GlobalKey<FormBuilderState>();
  String? deviceName;

  bool showPass = false;

  @override
  void initState() {
    sendAnalyticsScreenEvent(screenName: 'LoginPage');
    deviceInfo();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      body: SingleChildScrollView(
        child: FormBuilder(
          key: _fbKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: screenWidth * 0.05, top: screenHeight * 0.058),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        Nav.back(context);
                      },
                      child: Icon(Icons.close, color: Theme.of(context).primaryColor.withOpacity(0.7), size: 40),
                    ),
                    Padding(
                      padding: EdgeInsets.only(right: screenWidth * 0.07),
                      child: ElevatedButton(
                        //color: Theme.of(context).primaryColor,
                        onPressed: () {
                          Nav.submitAgency(context);
                          sendAnalyticsEvent(eventName: 'login_submit_agency_button_click');
                        },
                        child: Text("Submit your agency", style: TextStyle(color: Colors.white)),
                        //elevation: 8,
                      ),
                    ),
                  ],
                ),
              ),
              HeadingText(screenWidth: screenWidth, screenHeight: screenHeight, txt: "Login"),
              SubHeadingText(
                screenWidth: screenWidth,
                screenHeight: screenHeight,
                txt: "Login to post your own inventory.",
              ),
              SizedBox(height: screenHeight * 0.04),
              InputText(screenWidth: screenWidth, screenHeight: screenHeight, txt: "Phone"),
              MainTextFieldPadding(
                screenWidth: screenWidth,
                screenHeight: screenHeight,
                textField: FormBuilderTextField(
                  name: 'username',
                  keyboardType: TextInputType.text,
                  decoration: InputStyle(hint: "Enter phone number", icon: Icons.person),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(errorText: 'Phone is required'),
                  ]),
                ),
              ),
              InputText(screenHeight: screenHeight, screenWidth: screenWidth, txt: "Password"),
              MainTextFieldPadding(
                screenWidth: screenWidth,
                screenHeight: screenHeight,
                textField: FormBuilderTextField(
                  name: 'password',
                  maxLines: 1,
                  obscureText: !showPass,
                  decoration: InputStyle(hint: "**********", icon: Icons.lock,
                      suffixIcon: InkWell(
                        onTap: () {
                          setState(() {
                            showPass = !showPass;
                          });
                        },
                        child: Icon(showPass ? Icons.visibility_off : Icons.visibility, size: 20),
                      )
                  ),
                  validator: FormBuilderValidators.compose([
                    FormBuilderValidators.required(errorText: 'Password is required'),
                  ]),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.07),
                child: GestureDetector(
                    onTap: _submitClick,
                    child: CommonButton(screenWidth: screenWidth, screenHeight: screenHeight, txt: "LOGIN")),
              ),
              SizedBox(height: screenHeight * 0.03),
              Center(
                child: GestureDetector(
                  onTap: () {
                    Nav.resetPasswordPage(context);
                    sendAnalyticsEvent(eventName: 'login_reset_password_button_click');
                  },
                  child: Text(
                    'Reset password',
                    style: TextStyle(fontSize: 16, color: Theme.of(context).primaryColor.withOpacity(0.9)),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  deviceInfo() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (!kIsWeb && Platform.isAndroid) {
      AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
      deviceName = '${androidInfo.brand! + '-' + androidInfo.model! + '-' + androidInfo.id!}';
    }
    if (!kIsWeb && Platform.isIOS) {
      IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
      deviceName =
          '${iosInfo.name! + '-' + iosInfo.model! + '-' + iosInfo.identifierForVendor! + '-' + iosInfo.systemVersion!}';
    }
    if (kIsWeb) {
      WebBrowserInfo webBrowserInfo = await deviceInfo.webBrowserInfo;
      deviceName = '${webBrowserInfo.appVersion}';
    }
  }

  Future<void> _submitClick() async {
    sendAnalyticsEvent(eventName: 'login_button_click');
    if (_fbKey.currentState!.saveAndValidate()) {
      Notify.progress(context, 'Please wait...');
      Map<String, dynamic> values = {};
      values.addAll(_fbKey.currentState!.value);
      values['device_id'] = deviceName;
      var result = await API.login(values);
      print(result);
      if (result.status!.status == ApiStatus.OK) {
        await widget.controller.login(result.data!);
        Notify.hide();
        Notify.toast('Login Successful', type: 1);
        Nav.back(context);
        Nav.homePage(context);
        obProvider.selectUserType(UserTypes.agent);
      } else {
        Notify.hide();
        Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
      }
    }
  }
}
