import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../data/api.dart';
import '../models/blog_entity.dart';
import '../nav.dart';
import '../utils/functions.dart';
import '../utils/sliver_list_base_state.dart';
import '../utils/widgets.dart';
import '../widgets/not_available.dart';

class NewsListPage extends StatefulWidget {
  @override
  _NewsListPageState createState() => _NewsListPageState();
}

class _NewsListPageState extends SliverListBaseState<NewsListPage, List<BlogEntity>> {
  @override
  String get title => 'News';

  @override
  bool get paginate => true;

  @override
  void getData() {
    API.getNewsList(page: page).then(this.handleResponse);
  }

  @override
  Widget listItem(int index) {
    DateFormat dateFormat = DateFormat("yyyy-MM-dd");
    DateTime dateTime = dateFormat.parse('${data![index].date}');
    String formattedDate = DateFormat('MMMM dd, yyyy').format(dateTime);
    return GestureDetector(
      onTap: () {
        Nav.newsDetailPage(context, id: data![index].slug, banner: data![index].banner);
        sendAnalyticsEvent(eventName: 'list_news_item_click', parameters: {'news_id': data![index].id});
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: TileCard(
          horizontal: 0,
          vertical: 0,
          elevation: 5,
          child: Container(
            height: 100,
            child: Row(
              children: <Widget>[
                Hero(
                  tag: data![index].banner,
                  child: NetworkImageWithPlaceholder(
                    data![index].banner,
                    width: 100,
                    text: data![index].title,
                  ),
                ),
                SizedBox(width: 10),
                Flexible(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 5),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: <Widget>[
                        Text(
                          data![index].title,
                          maxLines: 2,
                          textAlign: TextAlign.left,
                          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                        ),
                        Text(formattedDate),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget get notFound => Container(
        alignment: Alignment.center,
        child: NotAvailable('Not Found', title: 'Coming Soon'),
      );
}
