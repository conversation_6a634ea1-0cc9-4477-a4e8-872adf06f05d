import 'package:auto_size_text/auto_size_text.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:readmore/readmore.dart';
import 'package:share/share.dart';

import '../../controllers/feedback_controller.dart';
import '../../controllers/login_controller.dart';
import '../../data/api.dart';
import '../../models/classified_entity.dart';
import '../../nav.dart';
import '../../theme.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../../utils/helpers.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import '../../widgets/social.dart';
import 'classified_location_map.dart';

class ClassifiedPropertyDetailPage extends StatefulWidget {
  final int propertyId;
  final String logo;
  final int? isHomeClick;

  const ClassifiedPropertyDetailPage(this.propertyId, this.logo, {this.isHomeClick});

  @override
  _ClassifiedPropertyDetailPageState createState() => _ClassifiedPropertyDetailPageState();
}

class _ClassifiedPropertyDetailPageState extends BaseState<ClassifiedPropertyDetailPage, ClassifiedEntity> {
  final LoginController login = Get.find();

  showFeedbackDialog() {
    FeedbackController feedback = Get.find();
    if(feedback.canShowFeedback){
      // _showDialog(context);
      feedback.showDialog();
    }
  }
  @override
  Widget get fab => data != null
      ? FloatingActionButton(
          backgroundColor: MyColors.primary,
          child: Icon(Icons.share),
          onPressed: () {
            Share.share(data!.title +
                '\n\n' +
                'Address: ' +
                data!.block!.name +
                ' ' +
                data!.society +
                ' ' +
                data!.city +
                '\n\n' +
                'Price: ' +
                data!.price +
                '\n\n' +
                'Click here to check: ' +
                data!.url!);
            sendAnalyticsEvent(
                eventName: 'detail_classified_share_button_click', parameters: {'classified_id': widget.propertyId});
          },
        )
      : Container();

  @override
  bool get withAppBar => false;
  @override
  bool get showAd => true;

  @override
  void getData() {
    API.getClassifiedPropertyDetail(widget.propertyId).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'ClassifiedPropertyDetailPage');
  }

  @override
  Widget get pageLoader => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Hero(
              tag: widget.propertyId,
              child: NetworkImageWithPlaceholder(widget.logo, circular: true, height: 100, width: 100)),
          FullLoader(),
        ],
      );

  @override
  Widget body() {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    String title = Uri.encodeComponent(data!.title);
    sendAnalyticsEvent(
        eventName: widget.isHomeClick == 1 ? 'home_classified_item_click' : 'list_classified_item_click',
        parameters: {
          'classified_id': data?.id,
          'agency_id': data?.agency!.id,
          'staff_id': data?.staff!.id,
          'agency_name': data?.agency!.name,
          'staff_name': data?.staff!.name,
          'city_name': data?.city,
          'society_name': data?.society,
        });
    return SafeArea(
      child: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CarouselSlider(
                  options: CarouselOptions(
                    height: MediaQuery.of(context).size.height * .25,
                    viewportFraction: 1,
                    autoPlay: true,
                    autoPlayInterval: Duration(seconds: 3),
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.fastOutSlowIn,
                  ),
                  items: data!.media.map((i) {
                    return Builder(
                      builder: (BuildContext context) {
                        return GestureDetector(
                            onTap: () => Nav.galleryView(context, data!.media),
                            child: NetworkImageWithPlaceholder(i.thumb, width: MediaQuery.of(context).size.width * 1));
                      },
                    );
                  }).toList(),
                ),
                // SizedBox(height: screenHeight * .08),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: TileCard(
                    color: Color(0xfff8f8f8),
                    vertical: 10,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(5),
                              border: Border.all(color: MyColors.primary, width: 2),
                            ),
                              child: NetworkImageWithPlaceholder(
                                data!.staff.image!,
                                radius: 5,
                                height: 75,
                                width: 75,
                              ),
                          ),
                          SizedBox(height: 5),
                          AutoSizeText(
                              data!.staff.name,
                              style: TextStyle(color: MyColors.primary, fontWeight: FontWeight.bold),
                              maxLines: 1,
                              minFontSize: 8,
                              maxFontSize: 12,
                          ),
                        ],
                      ),
                      SizedBox(width: 10),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('Published: ${data!.createdAt}', style: TextStyle(color: Colors.black45)),
                          SizedBox(height: 5),
                          Text(data!.title, style: TextStyle(color: Colors.black, fontWeight: FontWeight.bold)),
                          SizedBox(height: 3),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            mainAxisSize: MainAxisSize.max,
                            children: [
                              Icon(Icons.location_on, size: 18),
                              SizedBox(width: 5),
                              AutoSizeText(
                                '${data!.block!.name}, ${data!.society} ${data!.city}',
                                maxLines: 1,
                                minFontSize: 8,
                                maxFontSize: 12,
                              ),
                            ],
                          ),
                          SizedBox(height: 3),
                          Row(
                            children: [
                              if (data!.bedrooms != 0)
                                Row(
                                  children: [
                                    Icon(Icons.king_bed_outlined, size: 18),
                                    SizedBox(width: 2),
                                    Text('${data!.bedrooms} bed', style: TextStyle(fontSize: 12)),
                                    SizedBox(width: 5),
                                  ],
                                ),
                              if (data!.bathrooms != 0)
                                Row(
                                  children: [
                                    Icon(Icons.bathtub_outlined, size: 18),
                                    SizedBox(width: 2),
                                    Text('${data!.bathrooms} bath', style: TextStyle(fontSize: 12)),
                                    SizedBox(width: 5),
                                  ],
                                ),
                              Row(
                                children: [
                                  Icon(Icons.area_chart, size: 18),
                                  SizedBox(width: 2),
                                  Text('${data!.size} ${data!.sizeUnit}', style: TextStyle(fontSize: 12)),
                                ],
                              ),
                            ],
                          ),
                          SizedBox(height: 3),
                          Row(
                            children: [
                              Icon(Icons.house, size: 18),
                              SizedBox(width: 2),
                              Text(
                                '${data!.type} ${data!.category}',
                                style: TextStyle(color: Colors.black87, fontSize: 12),
                              ),
                            ],
                          ),

                        ],
                      ),
                    ],
                  ),
                  ),
                ),
                SizedBox(height: 8),
                Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: TileCard(
                        elevation: 3,
                        vertical: 10,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            RichHeading.auto(text: "Description", fontSize: 18, padding: 0),
                            SizedBox(height: 5),
                            ReadMoreText(data!.description, trimLength: 200, style: TextStyle(color: Colors.black)),
                          ],
                        ),
                    ),
                ),
                SizedBox(height: 5),
                Padding(
                  padding: const EdgeInsets.all(10),
                  child: RichHeading.auto(text: "Photos", fontSize: 18, padding: 0),
                ),
                Container(
                  height: 140,
                  child: ListView.builder(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    itemCount: data!.media.length,
                    itemBuilder: (context, i) {
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: GestureDetector(
                          onTap: () {
                            Nav.galleryView(context, data!.media);
                            sendAnalyticsEvent(
                                eventName: 'detail_classified_open_gallery_click',
                                parameters: {'classified_id': widget.propertyId});
                          },
                          child: NetworkImageWithPlaceholder(data!.media[i].thumb, width: 140, radius: 14),
                        ),
                      );
                    },
                  ),
                ),
                if (data!.features.length > 0)
                  FeaturesBlock(features: data!.features, heading: 'Property Features'),
                if(data!.societyFeatures.length > 0)
                  FeaturesBlock(features: data!.societyFeatures, heading: 'Society Features'),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(height: 5),
                      Padding(
                        padding: const EdgeInsets.all(10),
                        child: RichHeading.auto(text: "Location | Maps", fontSize: 18, padding: 0),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: Container(
                          height: 200,
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(10.0),
                            child: ClassifiedLocationMap(
                              lat: data!.plot.lat,
                              long: data!.plot.long,
                              mapURL: data!.block.mapUrl,
                              minZoom: data!.block.minZoom,
                              maxZoom: data!.block.maxZoom,
                              plotName: data!.plot.plotNo,
                              id: data!.id,
                            ),
                          ),
                        ),
                      ),
                      SizedBox(height: 10),
                    ],
                  ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                  child: TileCard(
                    elevation: 2,
                    child: Row(
                      children: [
                        NetworkImageWithPlaceholder(data!.agency!.logo!, radius: 14),
                        SizedBox(width: 10),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            LimitedBox(
                              maxWidth: MediaQuery.of(context).size.width * .5,
                              child: Text('${data!.agency!.name} ', style: TextStyle(color: Colors.black, fontSize: 18)),
                            ),
                            SizedBox(height: 5),
                            Text('${data!.agency!.society}, ${data!.agency!.city}'),
                            PrimaryButton(
                              text: 'Go to Agency page',
                              onPressed: () {
                                Nav.agency(context, data!.agency!.id);
                                sendAnalyticsEvent(
                                    eventName: 'detail_classified_agency_button_click',
                                    parameters: {'classified_id': widget.propertyId});
                              },
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 80)
              ],
            ),
            Positioned(
              top: 0,
              left: 0,
              child:Container(
                height: screenHeight * .08,
                width: screenWidth,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter, end: Alignment.bottomCenter,
                    colors: [
                      Color(0xff01233f),
                      Color(0x00f8f8f8)
                    ],
                  ),
                ),
              ),
            ),
            Positioned(
              left: 10,
              top: 10,
              child: Container(
                height: 40,
                width: 40,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Colors.white),
                child: IconButton(onPressed: () => Nav.back(context), icon: Icon(Icons.arrow_back)),
              ),
            ),
            Positioned(
              right: 10,
              top: 10,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(
                    height: 30,
                    width: 100,
                    decoration:BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Color(0xfff8f8f8),
                        ),
                    child: Center(
                      child: RichHeading.auto(text: 'For ${data!.purpose}', fontSize: 16, padding: 0),
                    ),
                  ),
                  SizedBox(width: 5),
                  Container(
                    height: 30,
                    width: 120,
                    decoration:BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: Color(0xfff8f8f8),
                    ),
                    child: Center(
                      child: RichHeading.auto(text: data!.price != '0' ? data!.price : 'Call for price', fontSize: 16, padding: 0),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              right: 70,
              top: screenHeight * .36,
              child: SocialIcon.type(
                '',
                  SocialIcon.PHONE,
                onPressed: () {
                  Map<String, dynamic> body = {};
                  body['id'] = data!.id;
                  body['action'] = "Phone Call";
                  body['model'] = "classified_property";
                  API.postAnalytics(body);
                  API.clickCounterOnClassified(data!.id, {});
                  openLink('tel: +92${data!.staff!.phone.substring(1)}');
                  sendAnalyticsEvent(
                      eventName: 'detail_classified_callAgent_button_click',
                      parameters: {'classified_id': widget.propertyId});
                    showFeedbackDialog();
                },
              ),
            ),
            Positioned(
              right: 20,
              top: screenHeight * .36,
              child: SocialIcon.type(
                '',
                SocialIcon.WHATSAPP,
                onPressed: () {
                  Map<String, dynamic> body = {};
                  body['id'] = data!.id;
                  body['action'] = "Whatsapp Call";
                  body['model'] = "classified_property";
                  API.postAnalytics(body);
                  API.clickCounterOnClassified(data!.id, {});
                  sendAnalyticsEvent(
                      eventName: 'detail_classified_whatsAppAgent_click',
                      parameters: {'classified_id': widget.propertyId});
                  openLink(
                      'https://wa.me/+92${data!.staff!.phone.substring(1)}?text=I have seen your ad *${data!.title}* on ilaaqa.com app ${data!.url}');
                    showFeedbackDialog();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FeaturesBlock extends StatelessWidget {
  const FeaturesBlock({
    super.key,
    required this.features,
    required this.heading,
  });

  final List<String> features;
  final String heading;

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 5),
        Padding(
          padding: const EdgeInsets.all(10),
          child: RichHeading.auto(
              text: heading, fontSize: 18, padding: 0),
        ),
        Container(
          width: screenWidth,
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: TileCard(
            elevation: 3,
            horizontal: 0,
            child: Wrap(
              children: [
                for (int i = 0; i < features.length; i++)
                  SizedBox(
                    width: screenWidth * .31,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 2),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.check_box_outlined,
                              color: MyColors.primary),
                          SizedBox(width: 5),
                          Flexible(
                            child: AutoSizeText(
                              "${features[i]}",
                              maxLines: 2,
                              wrapWords: true,
                              minFontSize: 8,
                              maxFontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
