import 'dart:io';

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:dio/dio.dart' show FormData, MultipartFile;
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
// import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'package:form_builder_extra_fields/form_builder_extra_fields.dart';
import 'package:property_dealers_directory/theme.dart';
import 'package:provider/provider.dart';

import '../../common/common_button.dart';
import '../../common/main_textField.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../models/classified_entity.dart';
import '../../models/plot_entity.dart';
import '../../models/society_file_entity.dart';
import '../../nav.dart';
import '../../providers/LocationSelectionProvider.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class AddClassifiedProperty extends StatefulWidget {
  final int? propertyId;
  final String? logo;

  const AddClassifiedProperty({this.propertyId, this.logo});

  @override
  _AddClassifiedPropertyState createState() => _AddClassifiedPropertyState();
}

class _AddClassifiedPropertyState extends State<AddClassifiedProperty> {
  final key = new GlobalKey<FormState>();
  final _fbKey = GlobalKey<FormBuilderState>();
  final ImagePicker picker = ImagePicker();
  final priceController = TextEditingController();

  List area = ['Kanal', 'Marla', 'Sq. Feet', 'Sq. Meter', 'Sq. Yards', 'Acre'];
  List typesList = ['Residential', 'Commercial'];
  List purposeList = ['Sale', 'Rent', 'Require'];

  List<String> categoryList = [];
  List<XFile> imageFileList = [];
  XFile? imageFile;

  List<String> featuresList = [];
  ClassifiedEntity? property;
  bool isEditPage = false;
  bool checkBoxValue = false;
  int maxLines = 5;
  int price = 0;

  String? categoryValue;
  String purpose = 'Sale';
  String type = 'Residential';

  PackageInfo? uaData;

  String featured_type = 'images';
  int featured_index = 0;

  List<ClassifiedMedia> societyFiles = [];
  List<int> societyFilesId = [];

  bool get isEdit => widget.propertyId != null;

  @override
  void initState() {
    getAppData();
    getCategories();
    getFeatures();
    if (isEdit)
      getPropertyDetail(widget.propertyId);
    sendAnalyticsScreenEvent(
        screenName: widget.propertyId == null ? 'AddClassifiedPropertyPage' : 'UpdateClassifiedPropertyPage');
    super.initState();
  }

  getCategories() async {
    APIResponse<List<String>> result = await API.getInventoryCategories();
    if (result.status!.status == ApiStatus.OK)
      setState(() {
        categoryList = result.data!;
        categoryList.removeAt(0);
      });
  }
  getFeatures() async {
      APIResponse<List<String>> result = await API.getFeaturesList();
      if (result.status!.status == ApiStatus.OK)
        setState(() {
          featuresList = result.data!;
        });
    }

  getPropertyDetail(id) async {
    await API.getClassifiedPropertyDetail(id).then((value) {
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          property = value.data;
          isEditPage = true;
          property?.priceNumeric == 0 ? checkBoxValue = true : checkBoxValue = false;
          priceController.text = property!.priceNumeric.toString();
          categoryValue = property!.category;
          getSocietyFiles(property!.societyId);
          var location = context.read<LocationSelectionProvider>();
          if(property!.plotId != 0){
            location.setPlotId(property!.plotId);
            location.selectedPlot = PlotEntity.fromClassifiedPlot(property!.plot);
          }
          if(property!.blockId != 0){
            var block = BlockEntity();
            block.id = property!.blockId;
            location.setBlock(block);
          }
          if(property!.featuredImageId != 0){
            featured_type = 'existing_media';
            featured_index = property!.media.indexWhere((element) => element.id == property!.featuredImageId);
          }
        });
    });
  }

  getSocietyFiles(int societyId){
    if(societyId != -1 && categoryValue != null && ['Plot', 'File'].contains(categoryValue)){
      API.getSocietyFiles(societyId).then((value) {
        if(value.status!.status == ApiStatus.OK){
          setState(() {
            societyFiles = value.data!;
          });
        }
      });
    } else {
      setState(() {
        societyFiles = [];
      });
    }
  }

  convertToRupee() {
    var length = price.toString().length;
    if (length >= 8) {
      return (price / 10000000).toStringAsFixed(2) + ' Crore';
    } else if (length >= 6 && length <= 7) {
      return (price / 100000).toStringAsFixed(2) + ' Lakh';
    } else if (length >= 8) {
      return (price / 100000).toStringAsFixed(0) + ' Lakh';
    } else if (length >= 4 && length <= 5) {
      return (price / 1000).toStringAsFixed(2) + ' Thousand';
    } else {
      return price;
    }
  }

  Future<void> pickImagesFromCamera() async {
    XFile? result;
    try {
      result = await picker.pickImage(source: ImageSource.camera);
      setState(() {
        imageFileList.add(result!);
        // imageFile = result;
      });
    } on Exception catch (e) {
      print(e);
    }
  }

  Future<void> pickImagesFromGallery() async {
    List<XFile>? resultList = <XFile>[];
    try {
      resultList = await (picker.pickMultiImage());
    } on Exception catch (e) {
      print(e);
    }
    setState(() {
      imageFile = null;
      if (imageFileList.isNotEmpty)
        imageFileList.addAll(resultList!);
      else
        imageFileList = resultList!;
    });
  }

  Future<PackageInfo?> getAppData() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      uaData = packageInfo;
    });
    return uaData;
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return widget.propertyId != null
        ? property == null && !isEditPage
            ? Scaffold(
                body: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Hero(
                      tag: widget.logo!,
                      child: NetworkImageWithPlaceholder(widget.logo!, circular: true, height: 100, width: 100)),
                  FullLoader(),
                ],
              ))
            : buildScaffold(context, screenWidth, screenHeight)
        : buildScaffold(context, screenWidth, screenHeight);
  }

  buildScaffold(BuildContext context, double screenWidth, double screenHeight) {
    final location = context.watch<LocationSelectionProvider>();
    location.setContext(context);
    return Scaffold(
        body: ListView(children: [
      Stack(
        children: [
          Container(
            height: 250.0,
            width: screenWidth,
            child: Image.asset("images/property.jpg", fit: BoxFit.cover),
          ),
          Positioned(
              top: 20,
              left: 1,
              right: 1,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  IconButton(
                      onPressed: () {
                        Nav.back(context);
                      },
                      icon: Icon(
                        Icons.arrow_back_sharp,
                        color: Colors.white,
                      )),
                  Text(
                    widget.propertyId == null ? "Add Classified Property" : "Update Classified Property",
                    style: TextStyle(fontSize: 20.0, fontWeight: FontWeight.bold, color: Colors.white),
                  ),
                ],
              )),
          Positioned(
              bottom: screenHeight * .07,
              left: screenWidth * .0833,
              right: screenWidth * .0833,
              child: Container(
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    for (int i = 0; i < purposeList.length; i++)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            purpose = purposeList[i];
                          });
                        },
                        child: Container(
                            height: 50,
                            width: 100,
                            decoration: BoxDecoration(
                              color: purpose == purposeList[i] ? Theme.of(context).primaryColor : Colors.white,
                            ),
                            child: Center(
                                child: Text(
                              '${purposeList[i]}',
                              style: TextStyle(
                                  color: purpose == purposeList[i] ? Colors.white : Colors.black,
                                  fontWeight: FontWeight.bold),
                            ))),
                      ),
                  ],
                ),
              )),
        ],
      ),
      Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: FormBuilder(
            key: _fbKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                InputText(screenWidth: screenWidth * 0.1, screenHeight: screenHeight * 0.5, txt: 'Type'),
                SizedBox(height: 10.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    for (int i = 0; i < typesList.length; i++)
                      GestureDetector(
                        onTap: () {
                          widget.propertyId ??
                              setState(() {
                                type = typesList[i];
                              });
                        },
                        child: Container(
                            height: 50,
                            width: 100,
                            decoration: BoxDecoration(
                                color: type == typesList[i]
                                    ? Theme.of(context).primaryColor.withOpacity(0.9)
                                    : Colors.grey[200]),
                            child: Center(
                                child: Text(
                              '${typesList[i]}',
                              style: TextStyle(color: type == typesList[i] ? Colors.white : Colors.black),
                            ))),
                      ),
                  ],
                ),
                SizedBox(height: 15.0),
                InputBlock(
                  title: 'Category',
                  child: FormBuilderSearchableDropdown<String>(
                    popupProps: const PopupProps.modalBottomSheet(showSearchBox: true),
                    name: 'category',
                    initialValue: isEdit ? property?.category ?? '' : null,
                    items: categoryList,
                    enabled: categoryList.isNotEmpty,
                    onChanged: (v){
                      setState(() {
                        categoryValue = v;
                        getSocietyFiles(isEdit ? property!.societyId : location.selectedSociety.id); //TODO on edit send property.society_id
                      });
                    },
                    decoration: InputStyle(hint: 'Select Category'),
                    validator: FormBuilderValidators.required(errorText: 'Category is required'),
                  ),
                ),
                InputBlock(
                  enabled: !isEdit,
                  title: 'City',
                  child: FormBuilderSearchableDropdown<CityEntity>(
                    popupProps: const PopupProps.menu(showSearchBox: false),
                    name: 'city_id',
                    valueTransformer: (value) => value?.id,
                    items: location.cities,
                    enabled: location.cities.isNotEmpty,
                    onChanged: (v){
                      location.setCity(v);
                      _fbKey.currentState!.fields['society_id']!.reset();
                    },
                    decoration: InputStyle(hint: 'Select city'),
                    validator: FormBuilderValidators.required(errorText: 'City is required'),
                    compareFn: CityEntity.compareFn,
                    filterFn: CityEntity.filterFn,
                  ),
                ),
                InputBlock(
                  enabled: !isEdit,
                  title: 'Society',
                  child: FormBuilderSearchableDropdown<CityEntity>(
                    popupProps: const PopupProps.modalBottomSheet(showSearchBox: true, searchDelay: Duration(milliseconds: 200)),
                    name: 'society_id',
                    valueTransformer: (value) => value?.id,
                    items: location.societies,
                    enabled: location.societies.isNotEmpty,
                    onChanged: (v){
                      location.setSociety(v);
                      _fbKey.currentState!.fields['block_id']!.reset();
                      getSocietyFiles(location.selectedSociety.id);
                    },
                    decoration: InputStyle(hint: 'Select Society'),
                    validator: FormBuilderValidators.required(errorText: 'Society is required'),
                    compareFn: (item, value) => item.id == value.id,
                    filterFn: (c, filter) => c.name.toLowerCase().contains(filter.toLowerCase()),
                  ),
                ),
                InputBlock(
                  enabled: !isEdit,
                  title: 'Block',
                  child: FormBuilderSearchableDropdown<BlockEntity>(
                    popupProps: const PopupProps.modalBottomSheet(showSearchBox: true, searchDelay: Duration(milliseconds: 200)),
                    name: 'block_id',
                    valueTransformer: (value) => value?.id,
                    items: location.blocks,
                    enabled: location.blocks.isNotEmpty,
                    onChanged: (v){
                      location.setBlock(v);
                      _fbKey.currentState!.fields['plot_id']!.reset();
                    },
                    decoration: InputStyle(hint: 'Select Block'),
                    validator: FormBuilderValidators.required(errorText: 'Block is required'),
                    compareFn: (item, value) => item.id == value.id,
                    filterFn: (c, filter) => c.name.toLowerCase().contains(filter.toLowerCase()),
                  ),
                ),
                InputBlock(
                  // enabled: !isEdit,
                  title: 'Plot',
                  child: FormBuilderSearchableDropdown<PlotEntity>(
                    popupProps: const PopupProps.modalBottomSheet(showSearchBox: true, searchDelay: Duration(milliseconds: 200)),
                    name: 'plot_id',
                    valueTransformer: (value) => value?.id,
                    items: location.plots,
                    enabled: location.plots.isNotEmpty,
                    initialValue: isEdit && property?.plotId != 0 ? location.selectedPlot : null,
                    decoration: InputStyle(hint: 'Select Plot'),
                    clearButtonProps: ClearButtonProps(
                      icon: Icon(Icons.close, color: Colors.black),
                      isVisible: true,
                    ),
                    // selectedItem: location.selectedPlot,
                    compareFn: (item, value) => item.id == value.id,
                    filterFn: (c, filter) => c.title.toLowerCase().contains(filter.toLowerCase()),
                  ),
                ),
                InputBlock(
                  title: 'Area',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: FormBuilderTextField(
                            initialValue: isEdit ? property?.size.toString() ?? '' : null,
                            name: 'sqfeet',
                            keyboardType: TextInputType.number,
                            enableSuggestions: true,
                            decoration: InputStyle(hint: 'Enter area'),
                            validator: FormBuilderValidators.required(errorText: 'Area is required')
                        ),
                      ),
                      SizedBox(width: 10.0),
                      Expanded(
                        child: FormBuilderDropdown(
                            initialValue: isEdit ? property?.sizeUnit ?? '' : null,
                            name: 'size',
                            decoration: InputStyle(hint: 'e.g Sq.Yard'),
                            // icon: Text('e.g Sq.Yard', style: TextStyle(fontSize: 12)),
                            items: area.map((e) => DropdownMenuItem(value: e, child: Text('$e'))).toList(),
                            validator: FormBuilderValidators.required(errorText: 'Size is required')),
                      ),
                    ],
                  ),
                ),
                InputBlock(
                  title: 'Price (PKR)',
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Expanded(
                        child: FormBuilderTextField(
                            controller: priceController,
                            // initialValue: widget.propertyId != null
                            //     ? (!checkBoxValue ? property.priceNumeric.toString() : null)
                            //     : null,
                            enabled: !checkBoxValue,
                            name: 'price',
                            onChanged: (value) {
                              if (value != '')
                                setState(() {
                                  price = int.parse(value!);
                                });
                            },
                            keyboardType: TextInputType.number,
                            enableSuggestions: true,
                            decoration: InputStyle(hint: checkBoxValue ? 'On Call' : 'Full price e.g 27000.'),
                            validator: !checkBoxValue
                                ? FormBuilderValidators.compose([
                                    FormBuilderValidators.required(errorText: 'Price is required'),
                                    FormBuilderValidators.min(1000, errorText: 'Please enter min price 1000')
                                  ])
                                : null),
                      ),
                      SizedBox(width: 10.0),
                      Expanded(
                        child: CheckboxListTile(
                            controlAffinity: ListTileControlAffinity.leading,
                            contentPadding: EdgeInsets.all(0),
                            title: Text(
                              'On Call',
                              style: TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: MyColors.primary,
                              ),
                            ),
                            activeColor: MyColors.primary,
                            value: checkBoxValue,
                            onChanged: (v) {
                              setState(() {
                                _fbKey.currentState!.fields['price']!.reset();
                                if (isEdit)
                                  priceController.text = (v!
                                      ? ''
                                      : property?.priceNumeric.toString())!;
                                else
                                  priceController.text = '';
                                price = 0;
                                checkBoxValue = !checkBoxValue;
                              });
                            }),
                      )
                    ],
                  ),
                ),
                if (price.toString().length > 1 && price != 0)
                  Text('${convertToRupee()}', style: TextStyle(color: Colors.black54, fontSize: 12)),
                if (!['Plot', 'File'].contains(categoryValue))
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: InputBlock(
                          title: 'Bed Rooms',
                          child: FormBuilderTextField(
                              initialValue: isEdit ? property?.bedrooms.toString() ?? '' : null,
                              name: 'bedrooms',
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.allow(RegExp(r'\d')),
                              ],
                              keyboardType: TextInputType.number,
                              decoration: InputStyle(hint: 'Enter bedrooms')),
                        ),
                      ),
                      SizedBox(width: 10.0),
                      Expanded(
                        child: InputBlock(
                          title: 'Bath Rooms',
                          child: FormBuilderTextField(
                              initialValue: isEdit ? property?.bathrooms.toString() ?? '' : null,
                              name: 'bathrooms',
                              inputFormatters: <TextInputFormatter>[
                                FilteringTextInputFormatter.allow(RegExp(r'\d')),
                              ],
                              keyboardType: TextInputType.number,
                              decoration: InputStyle(hint: 'Enter bathrooms')),
                        ),
                      ),
                    ],
                  ),

                InputBlock(
                  title: 'Features',
                  child:  FormBuilderFilterChip<String>(
                    name: 'features[]',
                    initialValue: widget.propertyId != null ? property?.features ?? [] : [],
                    decoration: InputStyle(hint: 'Select Features'),
                    selectedColor: MyColors.primary,
                    backgroundColor: MyColors.primaryLight,
                    labelStyle: TextStyle(color: Colors.white),
                    checkmarkColor: Colors.white,
                    spacing: 5.0,
                    // validator: FormBuilderValidators.required(errorText: 'Features is required'),
                    options: featuresList.map((e) => FormBuilderChipOption(value: e)).toList(),
                  ),
                ),
                InputBlock(
                  title: 'Title',
                  child: FormBuilderTextField(
                      initialValue: widget.propertyId != null ? property?.title ?? '' : null,
                      name: 'title',
                      decoration: InputStyle(hint: 'Enter title'),
                      textCapitalization: TextCapitalization.words,
                      enableSuggestions: true,
                      validator: FormBuilderValidators.required(errorText: 'Title is required')),
                ),
                InputBlock(
                  title: 'Description',
                  child: Container(
                    height: maxLines * 27.0,
                    child: FormBuilderTextField(
                        initialValue: widget.propertyId != null ? property?.description ?? '' : null,
                        name: 'description',
                        maxLines: maxLines,
                        enableSuggestions: true,
                        decoration: InputStyle(hint: 'Enter description'),
                        validator: FormBuilderValidators.required(errorText: 'Description is required')),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    PrimaryButton(text: 'Generate Ai Description', onPressed: generateAiDescription),
                  ],
                ),
                InputBlock(
                  title: 'Youtube Video URL',
                  child: FormBuilderTextField(
                      initialValue: widget.propertyId != null ? property?.ytUrl ?? '' : null,
                      name: 'yt_url',
                      decoration: InputStyle(hint: 'Youtube Video URL'),
                      enableSuggestions: false,
                      // validator: FormBuilderValidators.required(errorText: 'Title is required'),
                  ),
                ),

                InputBlock(
                  title: 'Select Photos',
                  child: photoBottomSheet(context, screenHeight, screenWidth),
                ),
                InputBlock(
                  enabled: imageFileList.isNotEmpty,
                  title: 'Selected Photos',
                  child: GridView.count(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    crossAxisCount: 3,
                    children: List.generate(imageFileList != null ? imageFileList.length : 0, (index) {
                      XFile asset = imageFileList[index];
                      return Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Container(
                            width: 200,
                            height: 100,
                            child: Stack(
                              clipBehavior: Clip.none,
                              children: [
                                Positioned.fill(child: Image.file(File(asset.path), fit: BoxFit.cover)),
                                Positioned.fill(
                                  child: InkWell(
                                      onTap: () {
                                        setState(() {
                                          featured_type = 'images';
                                          featured_index = index;
                                        });
                                      },
                                      child: featured_type == 'images' && featured_index == index
                                          ? Container(
                                        color: Colors.black.withOpacity(0.5),
                                        child: Column(
                                          mainAxisAlignment: MainAxisAlignment.center,
                                          children: [
                                            Icon(
                                              Icons.check,
                                              color: Colors.white,
                                            ),
                                            Text(
                                              'Featured',
                                              style: TextStyle(color: Colors.white),
                                            ),
                                          ],
                                        ),
                                      )
                                          : null
                                  ),
                                ),
                                Positioned(
                                    top: 0,
                                    right: 0,
                                    child: GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            imageFileList.removeAt(index);
                                          });
                                        },
                                        child: Container(
                                          decoration: BoxDecoration(
                                              color: Colors.grey.withOpacity(0.8),
                                              borderRadius: BorderRadius.circular(20)),
                                          child: Icon(
                                            Icons.close,
                                            color: Colors.white,
                                          ),
                                        )))
                              ],
                            )),
                      );
                    }),
                  ),
                ),
                  InputBlock(
                    enabled: societyFilesId.isNotEmpty,
                    title: 'Society Photos Selected',
                    child: SocietyFilesSelector(
                      key: UniqueKey(),
                      files: societyFiles,
                      fileIds: societyFilesId,
                      onlySelected: true,
                      onSelected: (value) {
                        setState(() {
                          societyFilesId = value;
                        });
                      },
                      featured: (index) {
                        setState(() {
                          featured_type = 'our_images';
                          featured_index = index;
                        });
                      },
                      featuredIndex: featured_type =='our_images' ? featured_index : -1,
                    ),
                  ),
                if(isEdit)
                InputBlock(
                  enabled: isEdit && property!.media.isNotEmpty,
                  title: 'Existing Media',
                  child: SocietyFilesSelector(
                    key: UniqueKey(),
                    files: property!.media,
                    fileIds: property!.media.map((e) => e.id).toList(),
                    onlySelected: true,
                    onSelected: (value) {
                      setState(() {
                        property!.media.retainWhere((e) => value.contains(e.id));
                      });
                    },
                    featured: (index) {
                      setState(() {
                        featured_type = 'existing_media';
                        featured_index = index;
                      });
                    },
                    featuredIndex: featured_type =='existing_media' ? featured_index : -1,
                  ),
                ),
                SizedBox(height: 20.0),
                GestureDetector(
                    onTap: () async {
                      if (_fbKey.currentState!.saveAndValidate()) {
                        Notify.progress(context, 'Please wait...');
                        Map<String, dynamic> values = {};
                        values.addAll(_fbKey.currentState!.value);
                        if (checkBoxValue) values['price'] = 0;
                        values['type'] = type;
                        values['app_version'] = '${uaData?.version}.' + '${uaData?.buildNumber}';
                        values['purpose'] = purpose;
                        // values['staff_id'] = controller.user!.staff!.id;
                        values['featured_image'] = '{"type" : "$featured_type", "index" : $featured_index}';
                        values['our_images[]'] = societyFilesId;
                        if(isEdit){
                          values['retain_media[]'] = property!.media.map((e) => e.id).toList();
                        }

                        print(values);

                        FormData formData;
                        var file;

                        if(!kIsWeb) {
                          if (imageFileList.isNotEmpty) {
                            List<MultipartFile> multipartImageList = [];
                            for (XFile photo in imageFileList) {
                              multipartImageList
                                  .add(await MultipartFile.fromFile(photo.path, filename: photo.path
                                  .split('/')
                                  .last));
                            }
                            file = multipartImageList;
                          } else if (imageFile != null) {
                            MultipartFile multipartImage;
                            multipartImage =
                            await MultipartFile.fromFile(imageFile!.path, filename: imageFile!
                                .path
                                .split('/')
                                .last);
                            file = multipartImage;
                          }
                          if(file != null) {
                            formData = FormData.fromMap(
                                {...values, 'images[]': file});
                          } else {
                            formData = FormData.fromMap(values);
                          }
                        } else {
                          formData = FormData.fromMap(values);
                          if (imageFileList.isNotEmpty) {
                            for (XFile photo in imageFileList) {
                              formData.files.add(MapEntry('images[]', MultipartFile.fromBytes(await photo.readAsBytes(), filename: photo.name ?? 'picture')));
                            }
                          } else if (imageFile != null) {
                            formData.files.add(MapEntry('images[]', MultipartFile.fromBytes(await imageFile!.readAsBytes(), filename: imageFile!.name ?? 'picture')));
                          }
                        }
                        APIResponse result;
                        if (widget.propertyId != null)
                          result = await API.updateClassifiedProperty(widget.propertyId, formData);
                        else
                          result = await API.addClassifiedProperty(formData);
                        Notify.hide();
                        if (result.status!.status == ApiStatus.OK) {
                          Notify.toast(
                              widget.propertyId != null ? 'Classified property Updated' : 'Classified property Added',
                              type: 1);
                          Nav.back(context);
                        } else {
                          Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
                        }
                      }
                      sendAnalyticsEvent(
                          eventName: widget.propertyId != null
                              ? 'update_classified_button_click'
                              : 'submit_classified_button_click');
                    },
                    child: CommonButton(
                        screenHeight: screenHeight,
                        screenWidth: screenWidth,
                        txt: widget.propertyId != null ? 'Update' : 'Submit')),
                SizedBox(height: 10.0),
              ],
            ),
          ))
    ]));
  }

  InkWell photoBottomSheet(BuildContext context, double screenHeight, double screenWidth) {
    return InkWell(
      onTap: () async {
        await showModalBottomSheet(
            isScrollControlled: true,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(topRight: Radius.circular(30), topLeft: Radius.circular(30))),
            context: context,
            builder: (context) {
              return Container(
                  height: screenHeight * (societyFiles.isEmpty ? 0.2 : 0.7),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListTile(
                          leading: Icon(Icons.camera_alt),
                          title: Text("Camera"),
                          onTap: () {
                            Nav.back(context);
                            pickImagesFromCamera();
                          },
                        ),
                        ListTile(
                          leading: Icon(Icons.photo),
                          title: Text("Gallery"),
                          onTap: () {
                            Nav.back(context);
                            pickImagesFromGallery();
                          },
                        ),
                        if (societyFiles.isNotEmpty)
                          ListTile(
                            title: Text("Society Photo Bank"),
                          ),
                        if (societyFiles.isNotEmpty)
                          Expanded(
                            child: SocietyFilesSelector(
                              files: societyFiles,
                              fileIds: societyFilesId,
                              onSelected: (value) {
                                setState(() {
                                  societyFilesId = value;
                                });
                              },
                            ),
                          ),
                      ],
                    ),
                  ));
            });
        setState(() {

        });
      },
      child: Container(
          height: 100,
          width: screenWidth,
          child: Icon(Icons.camera_alt, color: Theme.of(context).primaryColor),
          color: Colors.blueGrey.shade100),
    );
  }

  generateAiDescription() {
    _fbKey.currentState!.saveAndValidate();
    Map<String, dynamic> values = {};
    values.addAll(_fbKey.currentState!.value);
    if (checkBoxValue) values['price'] = 'On Call';
    values['type'] = type;
    values['app_version'] = '${uaData?.version}.' + '${uaData?.buildNumber}';
    values['purpose'] = purpose;

    print(values);
    // return;

    Notify.progress(context, 'Please wait...');

    API.aiDescriptionClassified(values).then((value) {
      print(value.data);
      Notify.hide();
      if (value.status!.status == ApiStatus.OK) {

        showModalBottomSheet(context: context, builder: (context) {
          return Container(
            height: 600,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichHeading.auto(text: 'Select One Description'),
                  SizedBox(height: 10),
                  Expanded(
                    child: ListView.builder(
                      itemCount: value.data!.length,
                      itemBuilder: (context, index) {
                        return ExpansionTile(
                          tilePadding: EdgeInsets.all(8),
                          title: Text('Description ${index + 1}'),
                          subtitle: Text(value.data![index], maxLines: 4, overflow: TextOverflow.ellipsis),
                          children: [
                            Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  PrimaryButton(text: 'Copy', onPressed: () {
                                    Clipboard.setData(ClipboardData(text: value.data![index]));
                                    Notify.toast('Description copied to clipboard', type: 1);
                                  }),
                                  SizedBox(width: 10),
                                  PrimaryButton(text: 'Select', onPressed: () {
                                    var currentValue = _fbKey.currentState!.fields['description']!.value;
                                    if(currentValue.isNotEmpty){
                                      currentValue += '\n\n';
                                    }
                                    currentValue += value.data![index] + '\n\n';
                                    _fbKey.currentState!.fields['description']!.didChange(currentValue);
                                    Navigator.pop(context, value.data![index]);
                                  }),
                                ],
                              ),
                              Container(
                                padding: EdgeInsets.all(8),
                                color: Colors.grey.shade200,
                                width: double.infinity,
                                child: Text(value.data![index]),
                              ),
                            ],
                          ),
                          ],
                        );
                      },
                    ),
                  )
                ],
              ),
            ),
          );
        });

      } else {
        Notify.customDialog(context, desc: value.status!.message, type: DialogType.error);
      }
    });

  }
}

class SocietyFilesSelector extends StatefulWidget {
  SocietyFilesSelector({
    super.key,
    required this.files,
    required this.fileIds,
    required this.onSelected,
    this.onlySelected = false,
    this.featuredIndex = -1,
    this.featured,
  });

  List<ClassifiedMedia> files;
  List<int> fileIds;
  Function(List<int>) onSelected;
  bool onlySelected;
  Function(int)? featured;
  int featuredIndex;
  // int featuredIndex;

  @override
  State<SocietyFilesSelector> createState() => _SocietyFilesSelectorState();
}

class _SocietyFilesSelectorState extends State<SocietyFilesSelector> {
  List<int> filesId = [];
  List<ClassifiedMedia> files = [];

  @override
  void initState() {
    filesId = widget.fileIds;
    freshFilesList();
    super.initState();
  }

  freshFilesList() {
    if (widget.onlySelected) {
      files = widget.files
          .where((element) => filesId.contains(element.id))
          .toList();
    } else {
      files = widget.files;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GridView.count(
      crossAxisCount: 3,
      shrinkWrap: true,
      physics: widget.onlySelected ? NeverScrollableScrollPhysics() : null,
      children: List.generate(files.length, (index) {
        return Padding(
          padding: const EdgeInsets.all(8.0),
          child: Container(
            width: 200,
            height: 100,
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                Positioned.fill(
                    child: NetworkImageWithPlaceholder(files[index].url),),
                if (!widget.onlySelected)
                Positioned.fill(
                  child: InkWell(
                      onTap: () {
                        setState(() {
                          filesId.contains(files[index].id)
                              ? filesId.remove(files[index].id)
                              : filesId.add(files[index].id);
                        });
                        widget.onSelected(filesId);
                      },
                      child: filesId.contains(files[index].id)
                          ? Container(
                              color: Colors.black.withOpacity(0.5),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.check,
                                    color: Colors.white,
                                  ),
                                  Text(
                                    'Selected',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ],
                              ),
                            )
                          : null),
                ),
                if(widget.featured != null)
                  Positioned.fill(
                    child: InkWell(
                        onTap: () {
                          setState(() {
                            // featuredIndex = index;
                          });
                          widget.featured!(index);
                        },
                        child: widget.featuredIndex == index
                            ? Container(
                          color: Colors.black.withOpacity(0.5),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.check,
                                color: Colors.white,
                              ),
                              Text(
                                'Featured',
                                style: TextStyle(color: Colors.white),
                              ),
                            ],
                          ),
                        )
                            : null),
                  ),
                if (widget.onlySelected)
                  Positioned(
                    top: 0,
                    right: 0,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          filesId.remove(files[index].id);
                          freshFilesList();
                          widget.onSelected(filesId);
                        });
                      },
                      child: Container(
                        decoration: BoxDecoration(
                            color: Colors.grey.withOpacity(0.8),
                            borderRadius: BorderRadius.circular(20)),
                        child: Icon(
                          Icons.close,
                          color: Colors.white,
                        ),
                      ),
                    ),
                  )
              ],
            ),
          ),
        );
      }),
    );
  }
}

class InputBlock extends StatelessWidget {
  const InputBlock({
    super.key,
    required this.title,
    required this.child,
    this.enabled = true,
  });

  final String title;
  final Widget child;
  final bool enabled;

  @override
  Widget build(BuildContext context) {
    if(!enabled) return Container();
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 8, top: 8, bottom: 4),
          child: Text(
            title,
            style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500),
          ),
        ),
        SizedBox(height: 5.0),
        child,
      ],
    );
  }
}
