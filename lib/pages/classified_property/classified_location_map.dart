import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:latlong2/latlong.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:property_dealers_directory/pages/maps_v2/cached_network_tile_provider.dart';
import 'package:property_dealers_directory/pages/maps_v2/maps_launcher_custom.dart';

import '../../models/classified_entity.dart';
import '../../utils/functions.dart';

class ClassifiedLocationMap extends StatelessWidget {
  final double lat;
  final double long;
  final String mapURL;
  final int minZoom;
  final int maxZoom;
  final int? id;
  final String plotName;



  ClassifiedLocationMap({
    this.id,
    this.plotName = '',
    required this.lat,
    required this.long,
    required this.mapURL,
    required this.minZoom,
    required this.maxZoom});

  @override
  Widget build(BuildContext context) {
    // final LatLng _center = LatLng(data.plot!.lat!, data.plot!.long!);
    final LatLng _center = LatLng(lat, long);

    return Stack(
      children: <Widget>[
        Container(
          child: FlutterMap(
            options: MapOptions(
              initialCenter: _center,
              initialZoom: plotName.isNotEmpty ? 17.0 : 15.0,
              minZoom: minZoom.toDouble(),
              maxZoom: maxZoom.toDouble(),
              // maxZoom: data.block.maxZoom.toDouble(),
            ),
            children: [
              TileLayer(
                urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                subdomains: ['a', 'b', 'c'],
                tileProvider: CachedNetworkTileProvider(),
              ),
              TileLayer(
                urlTemplate: "$mapURL/{z}/{x}/{y}.png",
                subdomains: ['a', 'b', 'c'],
                tileProvider: CachedNetworkTileProvider(),
                // backgroundColor: Colors.white.withOpacity(0),
                minZoom: minZoom.toDouble(),
                maxZoom: maxZoom.toDouble(),
                // maxZoom: data.block.maxZoom.toDouble(),
                // opacity: 1,
                tms: false,
              ),
            if(plotName.isNotEmpty)
              MarkerLayer(markers: [
                Marker(
                  height: 50,
                  width: 50,
                  point: _center,
                  child: Transform.translate(
                    offset: const Offset(0.0, -35 / 2),
                    child: Icon(
                      Icons.location_pin,
                      color: Theme.of(context).colorScheme.primary,
                      size: 50,
                    ),
                  ),
                ),
              ])
            ],
          ),
        ),
        Positioned(
          right: 0,
          bottom: 0,
          child: Container(
            padding: EdgeInsets.all(8.0),
            child: FloatingActionButton(
              mini: true,
              backgroundColor: Theme.of(context).primaryColor,
              child: Icon(FontAwesomeIcons.diamondTurnRight),
              onPressed: () {
                // MapsLauncher.launchCoordinates(lat, long);
                MapsLauncherCustom.launchCoordinates(lat, long);
                sendAnalyticsEvent(
                    eventName: 'detail_classified_google_map_click', parameters: {'classified_id': id});
              },
            ),
          ),
        )
      ],
    );
  }
}
