import 'package:flutter/material.dart';
import 'package:property_dealers_directory/models/plot_entity.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../utils/Notify.dart';

class FiltersProvider with ChangeNotifier {
  List<CityEntity?> societies = [];
  List<CityEntity> get getSocieties => societies.map((e) => e!).toList();
  List<BlockEntity> blocks = [];
  List<String> categories = ['All'];

  CityEntity city = CityEntity();
  CityEntity society = CityEntity();
  BlockEntity _defaultBlock = BlockEntity();
  BlockEntity block = BlockEntity();
  PlotEntity plot = PlotEntity();
  String? query = '';
  String purpose = 'All';
  String type = 'All';
  int mType = 0;
  String category = 'All';
  String mCategory = 'File';
  String size_min = '';
  String size_max = '';
  String size_unit = '';

  String widget = "classifieds";
  final bool isDeepLink;

  FiltersProvider({this.widget = "classifieds", this.isDeepLink = false}){
    block.id = 0;
    _defaultBlock.id = 0;
    getCategories();
    if(!isDeepLink) {
      setDefaults(obProvider.city, obProvider.onboarding.society);
    }
  }

  setDefaults(CityEntity _city, CityEntity? _society){
    city = _city;
    society = _society ?? CityEntity();
    societies = city.societies!;
    getBlocks(category: 'Residential');
  }

  setQuery(String? value){
    query = value;
    notifyListeners();
  }

  String _old = '';
  void saveOld(String type){
    if(type == 'inventory'){
      _old = searchInventory.toString();
    }else if(type == 'classified'){
      _old = searchClassified.toString();
    } else if(type == 'market'){
      _old = searchMarketInventory.toString();
    }
  }

  bool isChanged(String type){
    if(type == 'inventory'){
      return _old != searchInventory.toString();
    }else if(type == 'classified'){
      return _old != searchClassified.toString();
    } else if(type == 'market'){
      return _old != searchMarketInventory.toString();
    }
    return false;
  }

  int get filterCounter{
  int countFilter = 0;
    // if(query != '')
    //   countFilter ++;
    if(purpose != 'All')
      countFilter ++;
    if(type != 'All')
      countFilter ++;
    if(category != 'All')
      countFilter ++;
  if(size_min != '' || size_max != '' || size_unit != '')
    countFilter ++;

    return countFilter;
  }

  Map  get searchInventory {
    return {
      if(city.id != -1)
      'city_id': city.id,
      if(society.id != -1)
      'society_id':society.id,
      if(block.id != 0)
        'block_id' :block.id,
      if(type != 'All')
      'type_v2': type,
      if(purpose != 'All')
      'purpose': purpose,
      if(category != 'All')
      'category': category,
      if(query != '')
      'note': query,
      if(size_min != '')
        'size_min' : size_min,
      if(size_max != '')
        'size_max' : size_max,
      if(size_unit != '')
        'size_unit' : size_unit,

    };

  }
  Map  get searchClassified {
    return {
      if(city.id != -1)
        'city_id': city.id,
      if(society.id != -1)
        'society_id':society.id,
      if(block.id != 0)
        'block_id' :block.id,
      if(type != 'All')
        'type': type,
      if(purpose != 'All')
        'purpose': purpose,
      if(category != 'All')
        'category': category,
      if(query != '')
        'query': query,
      if(size_min != '')
        'size_min' : size_min,
      if(size_max != '')
        'size_max' : size_max,
      if(size_unit != '')
        'size_unit' : size_unit,

    };

  }

  String get searchString{
    String filter = '';
    if(city.id != null)
      filter += '&city_id=${city.id}';
    if(society.id != null)
      filter += '&society_id=${society.id}';
    if(block.id != 0)
      filter += '&block_id=${block.id}';

    if(purpose != 'All')
      filter += '&purpose=$purpose';
    if(type != 'All')
      filter +=  widget == "classifieds" ?  '&type=$type' : '&type_v2=$type'  ;
    if(category != 'All')
      filter += '&category=$category';
    if(query != '')
      filter += widget == "classifieds" ?  '&query=$query' : '&note=$query';
    if(size_min != '')
      filter += '&size_min=$size_min';
    if(size_max != '')
      filter += '&size_max=$size_max';
    if(size_unit != '')
      filter += '&size_unit=$size_unit';

    return filter;
  }

  Map  get searchMarketInventory {
    return {
      if(city.id != null)
        'city_id': city.id,
      if(society.id != null)
        'society_id':society.id,
      if(block.id != 0)
        'block_id' :block.id,
      if(plot.id != 0)
        'plot_id' : plot.id,
      if(mType != 0)
        'type': mType,
      if(mCategory != 'File')
        'category': mCategory,
    };

  }

  void reset(){
     purpose = 'All';
     type = 'All';
     category = 'All';
     size_min = '';
     size_max = '';
     size_unit = '';
    notifyListeners();
  }


  defaultBlock(BlockEntity blockEntity) {
    _defaultBlock = blockEntity;
    block = blockEntity;
    notifyListeners();
  }
  Future<void> citySelected(List<CityEntity> cities, String? _city) async {
    societies = [];
    society = CityEntity();
    blocks = [];
    block = _defaultBlock;
    city = cities.firstWhere((e) => e.name == _city);
    societies = cities.firstWhere((e) => e.name == _city).societies!;
    notifyListeners();
  }
  Future<void> societySelected(String? _society, String category) async {
    blocks = [];
    block = _defaultBlock;
    society = societies.firstWhere((e) => e?.name == _society, orElse: () => CityEntity())!;
    await getBlocks(category: category);
    notifyListeners();
  }

  Future<void> blockSelected(String? _block) async {
    block = blocks.firstWhere((e) => e?.name == _block, orElse: ()=> BlockEntity())!;
    notifyListeners();
  }

  Future<void> getBlocks({category = ""}) async {
    if(society.id == -1){
     // Notify.toast("Please select society first");
      blocks = [];
      notifyListeners();
      return;
    }
    var value = await API.blocksList(society.id, cityId: city.id, category: category);
    if (value.status!.status == ApiStatus.OK) {
      blocks = value.data!;
      notifyListeners();
    } else {
      Notify.toast("Blocks ${value.status!.message}");
    }
  }

  getCategories() async {
    APIResponse<dynamic> result = await API.getInventoryCategories();
    if (result.isOK) {
      categories = result.data;
      notifyListeners();
    }
  }

}