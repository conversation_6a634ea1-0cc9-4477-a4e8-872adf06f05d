import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';
import '../../common/common_button.dart';
import '../../common/main_textField.dart';
import '../../controllers/login_controller.dart';
import '../../data/api.dart';
import '../../models/property_entity.dart';
import '../../nav.dart';
import '../../utils/functions.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import 'filters_provider.dart';


class SelectClassifiedFilters extends StatefulWidget {
  const SelectClassifiedFilters();

  @override
  _SelectClassifiedFiltersState createState() => _SelectClassifiedFiltersState();
}

class _SelectClassifiedFiltersState extends State<SelectClassifiedFilters> {
  LoginController login = Get.find();
  final _fbKey = GlobalKey<FormBuilderState>();
  List purposeList = ['All', 'Sale', 'Rent', 'Require'];
  List typesList = ['All', 'Residential', 'Commercial'];
  List sizeUnits = ['Marla', 'Kanal', 'Sq. Ft.', 'Sq. Yards', 'Acre'];

  int? gValue;
  bool checkBoxValue = false;

  reset(){
    _fbKey.currentState?.reset();
  }


  @override
  Widget build(BuildContext context) {
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    FiltersProvider _provider = context.watch<FiltersProvider>();
    return Scaffold(
      // appBar: AppBar(title: Text(widget.propertyId == null ? 'Select Filters' : 'Update Filters')),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: sWidth * .05, vertical: sHeight * .02),
          child: FormBuilder(
            key: _fbKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                  Column(
                    children: [
                      // FormBuilderTextField(
                      //   name: "name",
                      //   initialValue: _provider.query,
                      //   // key: _fbKey,
                      //   onChanged: (v){
                      //     _provider.query = v?? '';
                      //   },
                      //   decoration: InputStyle(
                      //       hint: "Query"
                      //   ),
                      // ),

                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Purpose'),

                        ],
                      ),
                      SizedBox(height: 5.0),
                      FormBuilderRadioGroup(
                        activeColor: Theme.of(context).primaryColor,
                        initialValue: _provider.purpose,
                        name: 'purpose',
                        onChanged: (dynamic v) {
                          setState(() {
                            _provider.purpose = v;
                          });
                        },
                        decoration: InputStyle().copyWith(contentPadding: EdgeInsets.symmetric(vertical: 3)),
                        options:
                        purposeList.map((e) => FormBuilderFieldOption(value: e, child: Text('$e'))).toList(),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Type'),

                        ],
                      ),
                      SizedBox(height: 5.0),
                      FormBuilderRadioGroup(
                        activeColor: Theme.of(context).primaryColor,
                        name: 'type',
                        initialValue: _provider.type,
                        onChanged: (dynamic v) {
                          setState(() {
                            _provider.type = v;

                          });
                        },
                        decoration: InputStyle().copyWith(contentPadding: EdgeInsets.symmetric(vertical: 3)),
                        options:
                        typesList.map((e) => FormBuilderFieldOption(value: e, child: Text('$e'))).toList(),
                        validator: FormBuilderValidators.required(errorText: 'Type is required'),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Category'),

                        ],
                      ),
                      SizedBox(height: 5.0),
                      FormBuilderDropdown(
                        initialValue: _provider.category,
                        name: 'category',
                        decoration: InputStyle(hint: 'Select category'),
                        onChanged: (dynamic v) {
                          setState(() {
                            _provider.category = v;
                          });
                        },
                        items: _provider.categories.map((e) => DropdownMenuItem(value: e, child: Text('$e'))).toList(),
                        validator: FormBuilderValidators.required(errorText: 'Category is required'),
                      ),


                    ],
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Size'),
                  ],
                ),
                SizedBox(height: 5.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: FormBuilderTextField(
                        name: 'size_min',
                        initialValue: _provider.size_min,
                        keyboardType: TextInputType.number,
                        onChanged: (v) {
                          setState(() {
                            _provider.size_min = v!;
                          });
                        },
                      validator: (val){
                          var v = double.tryParse(val!);
                          var v2 = double.tryParse(_provider.size_max);
                          if(v == null || v2 == null)
                            return null;
                          if(v > v2){
                            return "From should be less than size_max";
                          }
                          return null;
                      },
                        decoration: InputStyle(hint: 'From'),
                      ),
                    ),
                    SizedBox(width: 5,),
                    Expanded(
                      child: FormBuilderTextField(
                        name: 'size_max',
                        initialValue: _provider.size_max,
                        keyboardType: TextInputType.number,
                        onChanged: (v) {
                          setState(() {
                            _provider.size_max = v!;

                          });
                        },
                        decoration: InputStyle(hint: 'To'),
                        validator: (val){
                          var v = double.tryParse(val!);
                          var v2 = double.tryParse(_provider.size_min);
                          if(v == null || v2 == null)
                            return null;
                          if(v < v2){
                            return "To should be less than size_max";
                          }
                          return null;
                        },
                      ),
                    ),
                    SizedBox(width: 5,),
                     Expanded(
                       child: Container(
                         height: 50,
                         child: FormBuilderDropdown(
                            name: 'size_unit',
                            enabled:  _provider.size_min.isNotEmpty && _provider.size_max.isNotEmpty,
                          initialValue: _provider.size_unit.isNotEmpty ? _provider.size_unit : null,
                            decoration: InputStyle(hint: 'e.g sq.ft.'),
                            onChanged: (dynamic v) {
                              _provider.size_unit = v;
                            },
                            // icon: Text('e.g sq.ft.', style: TextStyle(fontSize: 12)),
                            items: sizeUnits.map((e) => DropdownMenuItem(value: e, child: Text('$e'))).toList(),

                    ),
                       ),
                     ),
                  ],
                ),

                SizedBox(height: 20.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    GestureDetector(
                      onTap: () {
                        if (_fbKey.currentState!.saveAndValidate()) {
                          Nav.back(context);
                        }

                      },
                      child: CommonButton(
                          screenHeight: sHeight,
                          screenWidth: sWidth * 0.42,
                          txt: 'Apply Filters'),
                    ),

                    GestureDetector(
                      onTap: () {
                  _provider.reset();
                  Nav.back(context);
                      },
                      child: CommonButton(
                          screenHeight: sHeight,
                          screenWidth: sWidth * 0.42,
                          txt: 'Reset'),
                    ),
                  ],
                ),
                SizedBox(height: 10.0),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

