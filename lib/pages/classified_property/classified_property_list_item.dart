import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';

import '../../controllers/login_controller.dart';
import '../../data/api.dart';
import '../../models/classified_short_entity.dart';
import '../../nav.dart';
import '../../services/remote_config.dart';
import '../../theme.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../../utils/widgets.dart';
import '../../widgets/counter_widgets.dart';

class ClassifiedPropertyListItem extends StatelessWidget {
  final ClassifiedShortEntity property;
  final bool isMyClassifiedProperties;
  final VoidCallback? deleted;
  final Function(String? text)? showSnack;
  final Function(String text)? showSnackR;

  ClassifiedPropertyListItem(this.property, this.isMyClassifiedProperties,
      {this.deleted, this.showSnack, this.showSnackR});

  final LoginController login = Get.find();

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Map<String, dynamic> body = {};
        body['id'] = property.id;
        body['action'] = "Click";
        body['model'] = "classified_property";
        API.postAnalytics(body);
        isMyClassifiedProperties && login.staff?.id != null && property.status == 'Active'
            ? _openDialog(property, context)
            : Nav.classifiedPropertyDetailPage(context, property.id, property.photo);
        // sendAnalyticsEvent(eventName: 'list_classified_item_click', parameters: {'classified_id': property.id});
      },
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 5),
        child: TileCard(
          horizontal: 0,
          vertical: 0,
          elevation: 5,
          child: Stack(
            children: [
              Container(
                height: 140,
                decoration: property.isFeatured!
                    ? BoxDecoration(
                        // border: Border(bottom: BorderSide(color: MyColors.primary, width: 3)),
                        border: Border.all(color: Colors.red, width: 3),
                  borderRadius: BorderRadius.circular(10),
                      )
                    : null,
                child: Row(
                  children: <Widget>[
                    Hero(
                      tag: property.id,
                      child: NetworkImageWithPlaceholder(
                        property.photo!,
                        height: 140,
                        width: 100,
                        radius: 8,
                      ),
                    ),
                    SizedBox(width: 5),
                    Flexible(
                      child: Container(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 1.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: <Widget>[
                              SizedBox(height: 5.0),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.circular(5),
                                      border: Border.all(color: MyColors.secondary, width: 2),
                                    ),
                                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
                                    child: Text(
                                      property.price != '0' ? property.price : 'Call for price',
                                      maxLines: 2,
                                      textAlign: TextAlign.left,
                                      style: TextStyle(
                                          fontSize: 16,
                                          fontFamily: "Comfortaa",
                                          fontWeight: FontWeight.bold,
                                          color: MyColors.primary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 2.0),
                              Row(
                                children: <Widget>[
                                  Icon(Icons.location_on, size: 16.0, color: MyColors.secondary),
                                  SizedBox(width: 5),
                                  Expanded(
                                    child: AutoSizeText(
                                      '${property.block}, ${property.society} ${property.city}',
                                      maxLines: 1,
                                      style: TextStyle(color: MyColors.primary, fontSize: 12),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5),
                              Row(
                                children: [
                                  Icon(FontAwesomeIcons.building, size: 16.0, color: MyColors.secondary),
                                  SizedBox(width: 5),
                                  Expanded(
                                    child: AutoSizeText(
                                      '${property.titleLocal}',
                                      maxLines: 1,
                                      style: TextStyle(color: Colors.black, fontSize: 12),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 5),
                              Row(
                                children: [
                                  if (property.bedrooms != 0)
                                    Row(
                                      children: [
                                        Icon(Icons.king_bed_outlined, color: MyColors.secondary),
                                        SizedBox(width: 2),
                                        Text('${property.bedrooms} bed', style: TextStyle(fontSize: 12)),
                                        SizedBox(width: 5),
                                      ],
                                    ),
                                  if (property.bathrooms != 0)
                                    Row(
                                      children: [
                                        Icon(Icons.bathtub_outlined, color: MyColors.secondary),
                                        SizedBox(width: 2),
                                        Text('${property.bathrooms} bath', style: TextStyle(fontSize: 12)),
                                        SizedBox(width: 5),
                                      ],
                                    ),
                                  Row(
                                    children: [
                                      Icon(Icons.area_chart, color: MyColors.secondary),
                                      SizedBox(width: 2),
                                      Text('${property.size} ${property.sizeUnit}', style: TextStyle(fontSize: 12)),
                                    ],
                                  ),
                                  if (property.bedrooms != 0 && property.bathrooms != 0) Spacer(),
                                  if (isMyClassifiedProperties)
                                    LimitedBox(
                                      maxWidth: MediaQuery.of(context).size.width * 0.36,
                                      child: Row(
                                        children: [
                                          if (property.bedrooms != 0 && property.bathrooms != 0) Spacer(),
                                          if (RemoteConfigService.showClassifiedImpressions)
                                            ImpressionWidget(property: property),
                                          if (RemoteConfigService.showClassifiedViews) ViewWidget(property: property),
                                          if (RemoteConfigService.showClassifiedClicks) ClickWidget(property: property),
                                        ],
                                      ),
                                    ),
                                ],
                              ),
                              SizedBox(height: 5),
                              Row(
                                children: [
                                  Icon(Icons.access_time_filled, size: 16.0, color: MyColors.secondary),
                                  SizedBox(width: 5),
                                  Text(
                                    property.createdAt!,
                                    maxLines: 1,
                                    style: TextStyle(color: Colors.black, fontSize: 12),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if(property.isFeatured!)
              Align(
                alignment: Alignment.topRight,
                child: Container(
                  color: Colors.redAccent,
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10, top: 3, right: 10, bottom: 2),
                    child: Text(
                      'Hot',
                      style: TextStyle(color: Colors.white, fontSize: 12, fontWeight: FontWeight.bold),
                    ),
                  ),
                ),
              ),

              if (property.agencyFeatured)
                Positioned(
                  bottom: 10,
                  right: 10,
                  child: Column(
                    children: [
                      NetworkImageWithPlaceholder(
                        property.agencyLogo,
                        height: 35,
                        width: 35,
                      ),
                      SizedBox(height: 2),
                      Container(
                        decoration: BoxDecoration(
                            border: Border.all(
                                color: MyColors.primary),
                            borderRadius: BorderRadius.circular(5)),
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: 10, top: 3, right: 10, bottom: 2),
                          child: Text(
                            'Titanium',
                            style: TextStyle(
                                color: MyColors.primary,
                                fontSize: 10,
                                fontWeight: FontWeight.bold),
                          ),
                        ),
                      )
                    ],
                  ),
                )
            ],
          ),
        ),
      ),
    );
  }

  void _deleteItem(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text("Confirm"),
          content: const Text("Are you sure you wish to delete this item?"),
          actions: <Widget>[
            TextButton(
                onPressed: () {
                  Navigator.of(context).pop(true);
                  Map<String, dynamic> body = {};
                  body['status'] = 'Sold';
                  API.changeClassifiedStatus(property.id, body);
                  deleted!();
                  showSnack!('Classified Ad Deleted');
                },
                child: const Text("DELETE")),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text("CANCEL"),
            ),
          ],
        );
      },
    );
  }

  void _makeItHot(property, context) {
    showDialog(
      context: context,
      builder: (BuildContext _context) {
        return AlertDialog(
          title: const Text("Confirm"),
          content: const Text("Are you sure you want to make this ad hot?"),
          actions: <Widget>[
            TextButton(
                onPressed: () {
                  Navigator.of(context).pop(false);
                  Notify.progress(context, 'Please wait...');
                  API.makeClassifiedHot(property.id, {}).then((value) {
                    Notify.hide();
                    showSnack!(value.data['message']);
                  });
                },
                child: const Text("YES")),
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text("NO"),
            ),
          ],
        );
      },
    );
  }

  void _openDialog(property, context) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        builder: (_context) {
          return Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (login.isLoggedIn && !property.isFeatured)
                  ListTile(
                    title: Text('Make it hot'),
                    leading: Icon(Icons.whatshot),
                    onTap: () {
                      Get.back();
                      _makeItHot(property, context);
                      sendAnalyticsEvent(
                          eventName: 'list_my_classified_makeHotAd_click', parameters: {'classified_id': property.id});
                    },
                  ),
                ListTile(
                  title: Text('Refresh this ad'),
                  leading: Icon(Icons.refresh),
                  onTap: () async {
                    Get.back();
                    var result = await API.makeClassifiedRefresh(property.id, {});
                    result.status!.status == ApiStatus.OK
                        ? showSnackR!('Classified Ad Refreshed')
                        : showSnackR!(result.status!.message);
                    sendAnalyticsEvent(
                        eventName: 'list_my_classified_refreshAd_click', parameters: {'classified_id': property.id});
                  },
                ),
                ListTile(
                  title: Text('Open this ad'),
                  leading: Icon(Icons.open_in_new),
                  onTap: () {
                    Get.back();
                    Nav.classifiedPropertyDetailPage(context, property.id, property.photo);
                    sendAnalyticsEvent(
                        eventName: 'list_my_classified_openAd_click', parameters: {'classified_id': property.id});
                  },
                ),
                ListTile(
                  title: Text('Edit this ad'),
                  leading: Icon(Icons.edit),
                  onTap: () {
                    Get.back();
                    Nav.addClassifiedProperty(context, id: property.id, logo: property.photo);
                    sendAnalyticsEvent(
                        eventName: 'list_my_classified_editAd_click', parameters: {'classified_id': property.id});
                  },
                ),
                ListTile(
                  title: Text('Delete this ad'),
                  leading: Icon(Icons.delete_forever),
                  onTap: () {
                    Get.back();
                    _deleteItem(_context);
                    sendAnalyticsEvent(
                        eventName: 'list_my_classified_deleteAd_click', parameters: {'classified_id': property.id});
                  },
                ),
              ],
            ),
          );
        });
  }
}
