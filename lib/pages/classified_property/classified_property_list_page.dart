import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/utils/height_fraction_page.dart';
import 'package:provider/provider.dart';
import '../../../data/api.dart';
import '../../../utils/sliver_list_base_state.dart';
import '../../../widgets/not_available.dart';
import '../../controllers/login_controller.dart';
import '../../models/ad_banner_entity.dart';
import '../../models/classified_short_entity.dart';
import '../../services/addmob_services.dart';
import '../../utils/Notify.dart';
import '../../utils/functions.dart';
import '../../utils/text.dart';
import '../../widgets/admob_widgets.dart';
import '../../widgets/custom_ad_banner.dart';
import '../classified_property/classified_property_list_item.dart';
import '../properties_list/app_bar_with_filters.dart';

class ClassifiedPropertyListPage extends StatefulWidget {
  final int? id;
  final int? staffId;
  final int? agencyId;
  String currentAd;


  ClassifiedPropertyListPage({this.staffId, this.agencyId = 0, this.currentAd = "0", this.id = 0,});

  @override
  _ClassifiedPropertyListPageState createState() => _ClassifiedPropertyListPageState();
}

class _ClassifiedPropertyListPageState
    extends SliverListBaseState<ClassifiedPropertyListPage, List<ClassifiedShortEntity>> {
  final LoginController login = Get.find();


  bool get isMyClassifiedProperties => login.isLoggedIn && widget.staffId == login.staff?.id;


  @override
  String get title => isMyClassifiedProperties && login.staff?.id != null ? 'My Classified' : 'Classifieds';

  @override
  bool get paginate => true;

  @override
  double get expandedHeight =>  getHeight(context);

  @override
  bool get withAppBar => false;

  void applyFilters(){
    if(pullToRefresh)
      refreshController.requestRefresh();
  }

  @override
  Widget get customAppBar => AppBarWithFilters(
    type: 'classified',
    title: title,
    applyFilters: applyFilters,
    filtersProvider: context.read<FiltersProvider>(),
  );

  @override
  void getData() {
    Map filters = context.read<FiltersProvider>().searchClassified;
    if (widget.id == 0 && widget.agencyId == 0 && widget.currentAd == "0"){
      filters['city_id'] = filters['city_id'] ?? obProvider.city.id;
    }
    if (widget.currentAd != "0") {
      filters['ad_id'] = widget.currentAd;
    }

    API
        .getClassifiedPropertyList(page: page, staffId: widget.staffId, agencyId: widget.agencyId, query: filters)
        .then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'ClassifiedPropertyListPage');
  }

  @override
  Widget listItem(index) {
    return Column(
      key: Key('classified_property_list_item$index'),
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if(index == 0)
          CustomAdBanner(position: AdBannerPosition.cf_top),
        ClassifiedPropertyListItem(data![index], isMyClassifiedProperties,
            deleted: () => setState(() {}),
            showSnack: (text) => Notify.showSnack(context, text),
            showSnackR: (text) => Notify.showSnack(context, text)),
        if( (index + 1) % 5 == 0 && AdMobService.canDisplay(AdMobService.LOCATION_CLASSIFIED_LIST))
          // Center(child: AdmobBanner(size: AdSize.largeBanner, location: AdMobService.LOCATION_CLASSIFIED_LIST)),
          CustomAdBanner(key: Key('cf_list_banner$index'),position: AdBannerPosition.cf_list),
      ],
    );
  }
  @override
  Widget get notFound => SingleChildScrollView(
        child: Container(
          child: NotAvailable('Currently there is no data available for this search.', title: 'Not Found'),
        ),
      );
  Widget classifiedFilterDialog(){
    return AlertDialog(
      title: Heading.big('Select Filters', font: 20),
    );
  }

}
