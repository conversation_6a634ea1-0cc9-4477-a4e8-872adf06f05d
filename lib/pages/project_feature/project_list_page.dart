import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:property_dealers_directory/pages/project_feature/project_filter.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:provider/provider.dart';

import '../../controllers/city_selected.dart';
import '../../data/api.dart';
import '../../models/project_Short_entity.dart';
import '../../utils/functions.dart';
import '../../utils/height_fraction_page.dart';
import '../../utils/sliver_list_base_state.dart';
import '../../widgets/not_available.dart';
import '../classified_property/filters_provider.dart';
import 'project_list_item.dart';

class ProjectListPage extends StatefulWidget {
  final int? id;
  ProjectListPage({this.id = 0,});
  @override
  _ProjectListPageState createState() => _ProjectListPageState();
}

class _ProjectListPageState extends SliverListBaseState<ProjectListPage, List<ProjectShortEntity>> {
  String query = '';

  @override
  Widget listItem(index) {
    return ProjectListItem(data![index]);
  }

  @override
  String get title {
    if (query.isEmpty)
      return 'All Projects';
    else
      return 'Search results for "$query"';
  }

  @override
  void getData() {
    Map filters = context.read<FiltersProvider>().searchClassified;
    if (widget.id == 0){
      filters['city_id'] = filters['city_id'] ?? obProvider.city.id;
    }

    API.getProjectList(page: page, query: query, searchQuery: filters).then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'ProjectListPage');
  }

  @override
  bool get paginate => true;

  @override
  bool get showAd => true;

  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();

  @override
  double get expandedHeight => kIsWeb ? 120 :Platform.isIOS ? getAgencyHeight(context) : 110;

  @override
  bool get withAppBar => false;

  void applyFilters(){
    if(pullToRefresh)
      refreshController.requestRefresh();
  }

  @override
  Widget get customAppBar => PreferredSize(
        preferredSize: Size.fromHeight(120),
        child: AppBar(
            elevation: 2,
            shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.only(bottomLeft: Radius.circular(10), bottomRight: Radius.circular(10))),
            title: Text(title),
            flexibleSpace: Padding(
              padding: kIsWeb ? EdgeInsets.only(top: 35, left: 20, right: 20, bottom: 10) :
              (Platform.isIOS ? EdgeInsets.only(top: getProjectsAppBarHeight(context), left: 20, right: 20, bottom: 10) :
              EdgeInsets.only(top: getProjectsHeight(context), left: 20, right: 20, bottom: 0)),
              child: ProjectFilter(callback: applyFilters),
              // child: FormBuilder(
              //   key: _fbKey,
              //   child: Column(
              //     crossAxisAlignment: CrossAxisAlignment.start,
              //     children: [
              //       SizedBox(height: 40),
              //       Row(
              //         children: <Widget>[
              //           Container(
              //             height: 42,
              //             width: MediaQuery.of(context).size.width * .888,
              //             padding: EdgeInsets.symmetric(horizontal: 5.0),
              //             decoration: BoxDecoration(
              //                 color: Colors.white,
              //                 border: Border.all(
              //                   color: Colors.grey[400]!,
              //                   width: 0.8,
              //                 ),
              //                 borderRadius: BorderRadius.circular(5.0)),
              //             child: Row(
              //               children: [
              //                 Expanded(
              //                   child: FormBuilderTextField(
              //                       textInputAction: TextInputAction.search,
              //                       initialValue: '',
              //                       name: 'query',
              //                       decoration: InputDecoration(hintText: 'Search project', border: InputBorder.none),
              //                       onSubmitted: (value) {
              //                         searchPressed();
              //                       }),
              //                 ),
              //                 InkWell(
              //                   splashColor: Colors.transparent,
              //                   highlightColor: Colors.transparent,
              //                   onTap: searchPressed,
              //                   child: Container(
              //                     padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
              //                     decoration: BoxDecoration(
              //                         color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5.0)),
              //                     child: Icon(
              //                       Icons.search,
              //                       size: 18.0,
              //                       color: Colors.white,
              //                     ),
              //                   ),
              //                 )
              //               ],
              //             ),
              //           ),
              //         ],
              //       ),
              //     ],
              //   ),
              // ),
            )),
      );

  void searchPressed() {
    _fbKey.currentState!.save();
    query = _fbKey.currentState!.value['query'];
    data = null;
    setState(() {
      this.loading = true;
    });
    getData();
    sendAnalyticsEvent(eventName: 'list_project_search_button_click');
  }

  @override
  Widget get notFound => SingleChildScrollView(
        child: Container(
          child: NotAvailable(
            'Currently there is no data available for this search.',
            title: 'Not Found',
          ),
        ),
      );
}
