import 'package:flutter/material.dart';
import 'package:find_dropdown/find_dropdown.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/customized_lib/form_builder/find_dropdown_field.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:provider/provider.dart';

import '../../common/main_textField.dart';
import '../../controllers/city_selected.dart';
import '../../data/api.dart';
import '../../models/city_entity.dart';
import '../../utils/base_state.dart';
import '../../utils/widgets.dart';
import '../maps_v2/drop_down_container.dart';

class ProjectFilter extends StatefulWidget {
  VoidCallback callback;
  ProjectFilter({Key? key, required this.callback}) : super(key: key);

  @override
  State<ProjectFilter> createState() => _ProjectFilterState();
}

class _ProjectFilterState extends BaseState<ProjectFilter, List<CityEntity>> {
  List categoryList = ['Residential', 'Commercial'];
  String category = 'Residential';
  @override
  bool get pullToRefresh => false;

  @override
  bool get withScaffold => false;

  @override
  bool get showAd => false;

  @override
  Widget get pageLoader => SizedBox.shrink();

  @override
  void getData() {
    setState(() {
      context.read<FiltersProvider>().city = obProvider.city;
      context.read<FiltersProvider>().societies = obProvider.city.societies;
    });
    API.getCitiesList().then(this.handleResponse);
  }

  @override
  Widget body() {
    final classifiedProvider = context.watch<FiltersProvider>();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 0),
      child: Padding(
        padding: const EdgeInsets.only(bottom: 4),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: FindDropDownField(
                    name: "city_id",
                    hint: "Search Here",
                    label: "Select City",
                    selectedItem: classifiedProvider.city,
                    items: data!,
                    context: context,
                    onChanged: (v) async {
                      context.read<FiltersProvider>().citySelected(data!, v?.selectedLabel);
                      widget.callback();
                      setState(() {});
                    },
                  ),
                ),

                // Expanded(
                //   // flex: 2,
                //   child: FindDropdown<CityEntity?>(
                //     selectedItem: cityController.city,
                //     constraints: BoxConstraints.tight(MediaQuery.of(context).size * .5),
                //     label: "Select City",
                //     labelStyle: TextStyle(fontSize: 12, color: Colors.white),
                //     // labelVisible: false,
                //     searchBoxDecoration: InputStyle(hint: 'Search Here'),
                //     items: data!,
                //     onFind: (value) async {
                //       if (value.isEmpty) return data!;
                //       value = value.toLowerCase();
                //       return data!.where((city) {
                //         String name = city.name.toLowerCase();
                //         return name.contains(value);
                //       }).toList();
                //     },
                //     emptyBuilder: (context) {
                //       return FullLoader();
                //     },
                //     dropdownItemBuilder: (BuildContext context, CityEntity? city, bool isSelected) {
                //       return ListTile(selected: isSelected, title: Text(city!.name));
                //     },
                //     dropdownBuilder: (BuildContext context, item) {
                //       return DropDownContainer(item: item?.name, hint: "Search City");
                //     },
                //     onChanged: (v) async {
                //       context.read<FiltersProvider>().citySelected(data!, v?.name);
                //       widget.callback();
                //       // setState(() {
                //       //   if(classifiedProvider.societies.length == 1){
                //       //     classifiedProvider.societySelected(classifiedProvider.societies.first!.name, category);
                //       //     _dpKeySociety.currentState?.setSelectedItem(classifiedProvider.societies.first);
                //       //   } else{
                //       //     _dpKeySociety.currentState?.setSelectedItem(null);
                //       //   }
                //       //   _dpKeyBlock.currentState?.setSelectedItem(null);
                //       // });
                //     },
                //   ),
                // ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
