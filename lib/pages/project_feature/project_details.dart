import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';

import '../../data/api.dart';
import '../../models/project_entity.dart';
import '../../nav.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../../utils/helpers.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';
import 'project_Items.dart';

class ProjectDetailPage extends StatefulWidget {
  final int? projectId;
  final String? logo;

  const ProjectDetailPage(this.projectId, this.logo);

  @override
  _ProjectDetailPageState createState() => _ProjectDetailPageState();
}

class _ProjectDetailPageState extends BaseState<ProjectDetailPage, ProjectEntity> {
  @override
  Widget get fab => data != null
      ? PrimaryButton(
          text: 'Call for more detail',
          onPressed: () {
            Map<String, dynamic> body = {};
            body['id'] = data!.id;
            body['action'] = "Phone Call";
            body['model'] = "project";
            API.postAnalytics(body);
            API.clickCounterOnProject(widget.projectId, {});
            openLink('tel: +92${data!.contactNumber.substring(1)}');
            sendAnalyticsEvent(eventName: 'detail_project_call_button_click', parameters: {'project_id': data!.id});
          },
        )
      : Container();

  @override
  bool get withAppBar => false;

  @override
  void getData() {
    API.getProjectDetail(widget.projectId).then(this.handleResponse);
    API.viewCounterOnProject(widget.projectId, {});
    sendAnalyticsScreenEvent(screenName: 'ProjectDetailPage');
  }

  @override
  Widget body() {
    return SafeArea(
      child: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              children: [
                CarouselSlider(
                  options: CarouselOptions(
                    height: MediaQuery.of(context).size.height * .35,
                    viewportFraction: 1,
                    autoPlay: true,
                    autoPlayInterval: Duration(seconds: 3),
                    autoPlayAnimationDuration: Duration(milliseconds: 800),
                    autoPlayCurve: Curves.fastOutSlowIn,
                  ),
                  items: data!.media.map((i) {
                    return Builder(
                      builder: (BuildContext context) {
                        return GestureDetector(
                          onTap: () => Nav.galleryView(context, data!.media),
                          child: NetworkImageWithPlaceholder(i.url, height: 160, width: MediaQuery.of(context).size.width),
                        );
                      },
                    );
                  }).toList(),
                ),
                SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  child: TileCard(
                    elevation: 2,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.house_outlined),
                        SizedBox(width: 5),
                        Container(
                            width: 250,
                            alignment: Alignment.center,
                            child: Heading('${data!.location}', color: Colors.black)),
                      ],
                    ),
                  ),
                ),
                SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Column(
                      children: [
                        Text('Developer'),
                        SizedBox(height: 5),
                        Container(
                          width: 150,
                          child: Column(
                            children: [
                              NetworkImageWithPlaceholder(data!.developerLogo!),
                              SizedBox(height: 5),
                              FittedBox(child: Text('${data!.developerName}'))
                            ],
                          ),
                        ),
                      ],
                    ),
                    Column(
                      children: [
                        Text('Marketed By'),
                        SizedBox(height: 5),
                        Container(
                          width: 150,
                          child: Column(
                            children: [
                              NetworkImageWithPlaceholder(data!.marketerLogo!),
                              SizedBox(height: 5),
                              FittedBox(child: Text(data!.marketerName))
                            ],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
                  child: TileCard(
                    vertical: 0,
                    horizontal: 0,
                    elevation: 4,
                    child: ListTile(
                      title: Text(data!.address),
                      leading: Icon(Icons.location_on),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                  child: TileCard(
                    vertical: 10,
                    child: ReadMoreText(data!.about, trimLength: 200, style: TextStyle(color: Colors.black)),
                  ),
                ),
                SizedBox(height: 10),
                FeaturedProjectItems(data!.items, data!.media.map((e) => e.url).toList()),
                SizedBox(height: 80),
              ],
            ),
            Positioned(
              left: 10,
              top: 10,
              child: Row(
                children: [
                  Container(
                    height: 40,
                    width: 40,
                    decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Colors.white),
                    child: IconButton(
                        onPressed: () => Nav.back(context),
                        icon: Icon(
                          Icons.arrow_back,
                        )),
                  ),
                  SizedBox(
                    width: 15.0,
                  ),
                  Heading(
                    data!.title,
                    color: Colors.white,
                    weight: FontWeight.bold,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget get pageLoader => Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Hero(tag: widget.logo!, child: NetworkImageWithPlaceholder(widget.logo!, circular: true)),
          FullLoader(),
        ],
      );
}
