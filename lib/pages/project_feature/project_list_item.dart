import 'package:flutter/material.dart';
import 'package:property_dealers_directory/utils/functions.dart';

import '../../data/api.dart';
import '../../models/project_Short_entity.dart';
import '../../nav.dart';
import '../../utils/widgets.dart';

class ProjectListItem extends StatelessWidget {
  final ProjectShortEntity project;

  ProjectListItem(this.project);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Map<String, dynamic> body = {};
        body['id'] = project.id;
        body['action'] = "Click";
        body['model'] = "project";
        API.postAnalytics(body);
        Nav.projectDetailPage(context, project.id, project.logo);
        sendAnalyticsEvent(eventName: 'list_project_item_click', parameters: {'project_id': project.id});
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: TileCard(
          horizontal: 0,
          vertical: 0,
          elevation: 5,
          child: Container(
            height: 100,
            decoration: project.isFeatured
                ? BoxDecoration(
                    border: Border(bottom: BorderSide(color: Theme.of(context).primaryColor, width: 3)),
                  )
                : null,
            child: Row(
              children: <Widget>[
                Hero(
                  tag: project.logo!,
                  child: NetworkImageWithPlaceholder(
                    project.logo!,
                    width: 100,
                    text: project.title,
                  ),
                ),
                SizedBox(width: 5),
                Flexible(
                  child: Container(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 1.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 1.0),
                            child: Text(
                              project.title,
                              maxLines: 2,
                              textAlign: TextAlign.left,
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                          ),
                          SizedBox(height: 2.0),
                          Row(
                            children: <Widget>[
                              Icon(Icons.layers, size: 18.0),
                              SizedBox(width: 5),
                              Text(project.type),
                            ],
                          ),
                          SizedBox(height: 2.0),
                          Row(
                            children: <Widget>[
                              Icon(Icons.location_on, size: 18.0),
                              SizedBox(width: 5),
                              Text(project.location),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
