import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../../models/project_entity.dart';
import '../../nav.dart';
import '../../utils/text.dart';
import '../../utils/widgets.dart';

class ItemDetailPage extends StatelessWidget {
  final ProjectItems item;
  final List<String>? photos;

  ItemDetailPage(this.item, this.photos);

  @override
  Widget build(BuildContext context) {
    return Material(
      child: SafeArea(
        child: SingleChildScrollView(
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CarouselSlider(
                    options: CarouselOptions(
                      height: MediaQuery.of(context).size.height * .35,
                      viewportFraction: 1,
                      autoPlay: false,
                      scrollPhysics: NeverScrollableScrollPhysics(),
                      autoPlayInterval: Duration(seconds: 3),
                      autoPlayAnimationDuration: Duration(milliseconds: 800),
                      autoPlayCurve: Curves.fastOutSlowIn,
                    ),
                    items: item.photos != null && item.photos!.isNotEmpty
                        ? item.photos!.map<Widget>((i) {
                            return Builder(
                              builder: (BuildContext context) {
                                return NetworkImageWithPlaceholder(i,
                                    height: 160, width: MediaQuery.of(context).size.width);
                              },
                            );
                          }).toList()
                        : photos!.map<Widget>((i) {
                            return Builder(
                              builder: (BuildContext context) {
                                return NetworkImageWithPlaceholder(i,
                                    height: 160, width: MediaQuery.of(context).size.width);
                              },
                            );
                          }).toList(),
                  ),
                  SizedBox(height: 20),
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        TileCard(elevation: 2, child: Heading(item.type, color: Colors.black)),
                        TileCard(
                            elevation: 2,
                            child: Heading('${item.size.toString().replaceAll('.0', '')} - ${item.sizeUnit}',
                                color: Colors.black)),
                      ],
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(10),
                    child: TileCard(
                      child: Column(
                        children: [
                          Column(
                            children: [
                              Text("Payment Plan", style: TextStyle(fontSize: 22.0, fontWeight: FontWeight.bold)),
                              Divider(thickness: 1, color: Colors.black),
                            ],
                          ),
                          Table(
                            children: [
                              TableRow(children: [
                                TableCell(
                                    child: Column(
                                  children: [
                                    Text("Title", style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold)),
                                    Divider(thickness: 1, color: Colors.black),
                                    SizedBox(height: 10.0),
                                    for (int i = 0; i < item.plans!.length; i++)
                                      FittedBox(child: Text("${item.plans![i]!.title}\n")),
                                  ],
                                )),
                                TableCell(
                                    child: Column(
                                  children: [
                                    Text("Amount", style: TextStyle(fontSize: 18.0, fontWeight: FontWeight.bold)),
                                    Divider(thickness: 1, color: Colors.black),
                                    SizedBox(height: 10.0),
                                    for (int i = 0; i < item.plans!.length; i++) Text("${item.plans![i]!.amount}  \n"),
                                  ],
                                )),
                              ])
                            ],
                          ),
                        ],
                      ),
                    ),
                  )
                ],
              ),
              Positioned(
                left: 10,
                top: 10,
                child: Row(
                  children: [
                    Container(
                      height: 40,
                      width: 40,
                      decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: Colors.white),
                      child: IconButton(
                          onPressed: () => Nav.back(context),
                          icon: Icon(
                            Icons.arrow_back,
                          )),
                    ),
                    SizedBox(width: 15.0),
                    Heading(item.title, color: Colors.white, weight: FontWeight.bold)
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
