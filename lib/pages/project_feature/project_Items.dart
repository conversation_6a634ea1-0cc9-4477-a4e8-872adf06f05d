import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:property_dealers_directory/nav.dart';
import 'package:property_dealers_directory/utils/functions.dart';

import '../../models/project_entity.dart';
import '../../utils/widgets.dart';

class FeaturedProjectItems extends StatelessWidget {
  final List<ProjectItems?>? items;
  final List<String>? photos;

  FeaturedProjectItems(this.items, this.photos);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: items!.length,
      itemBuilder: (BuildContext context, int index) {
        ProjectItems item = items![index]!;
        return GestureDetector(
          onTap: () {
            Nav.itemDetailPage(context, item, photos);
            sendAnalyticsEvent(eventName: 'detail_project_item_click', parameters: {'projectItem_id': item.id});
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
            child: TileCard(
              horizontal: 0,
              vertical: 0,
              elevation: 5,
              child: Container(
                height: 100,
                child: Row(
                  children: <Widget>[
                    NetworkImageWithPlaceholder(
                      item.photos != null && item.photos!.isNotEmpty
                          ? item.photos!.asMap().containsKey(0)
                              ? item.photos![0]
                              : ''
                          : photos![0],
                      width: 100,
                      text: item.title,
                    ),
                    SizedBox(width: 5),
                    Flexible(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 1.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Text(
                              item.title,
                              maxLines: 2,
                              textAlign: TextAlign.left,
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                            Divider(thickness: 0.5, color: Colors.black),
                            SizedBox(height: 5.0),
                            Row(
                              children: [
                                Icon(FontAwesomeIcons.arrowsAlt, size: 14),
                                SizedBox(width: 10),
                                Text('${item.size.toString().replaceAll('.0', '')} - ${item.sizeUnit}'),
                              ],
                            ),
                            SizedBox(height: 4.0),
                            Row(
                              children: [
                                Icon(FontAwesomeIcons.tag, size: 14),
                                SizedBox(width: 10),
                                Text("${item.totalPayment}"),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}
