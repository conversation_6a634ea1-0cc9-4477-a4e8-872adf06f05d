import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

import '../models/video_entity.dart';
import '../utils/functions.dart';

class VideoPlayPage extends StatefulWidget {
  final VideoEntity data;

  VideoPlayPage(this.data);

  @override
  _VideoPlayPageState createState() => _VideoPlayPageState();
}

class _VideoPlayPageState extends State<VideoPlayPage> {
  late YoutubePlayerController _controller;
  bool _fullScreen = false;

  @override
  void initState() {
    super.initState();
    _controller =
        YoutubePlayerController(initialVideoId: widget.data.videoId, flags: YoutubePlayerFlags(autoPlay: false))
          ..addListener(listener);
    sendAnalyticsScreenEvent(screenName: 'HowToPlayPage');
  }

  void listener() {
    setState(() {
      _fullScreen = _controller.value.isFullScreen;
    });
  }

  @override
  void deactivate() {
    _controller.pause();
    super.deactivate();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _fullScreen ? null : AppBar(title: Text(widget.data.title)),
      body: YoutubePlayerBuilder(
        player: YoutubePlayer(
          controller: _controller,
          showVideoProgressIndicator: true,
          actionsPadding: EdgeInsets.all(10),
        ),
        builder: (context, player) {
          return SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                player,
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                  child: ReadMoreText(widget.data.description, trimLength: 200, style: TextStyle(color: Colors.black)),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}
