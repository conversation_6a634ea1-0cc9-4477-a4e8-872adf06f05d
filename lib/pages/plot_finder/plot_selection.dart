import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_progress_hud/flutter_progress_hud.dart';
import 'package:form_builder_validators/form_builder_validators.dart';

import '../../common/common_button.dart';
import '../../common/main_textField.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/plot_entity.dart';
import '../../models/society_entity.dart';
import '../../nav.dart';
import '../../utils/Notify.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../../utils/text.dart';

class PlotSelectionPage extends StatefulWidget {
  @override
  _PlotSelectionPageState createState() => _PlotSelectionPageState();
}

class _PlotSelectionPageState extends BaseState<PlotSelectionPage, List<SocietyEntity?>> {
  final _fbKey = GlobalKey<FormBuilderState>();
  final _dropdownKey = GlobalKey<DropdownSearchState>();
  final _dropdownKeyP = GlobalKey<DropdownSearchState>();

  List categoryList = ['Residential', 'Commercial'];

  @override
  String get title => 'Find Plot';

  List<BlockEntity?> blocks = [];
  List<PlotEntity?> plots = [];
  String? block;
  int? plotId;
  String category = 'Residential';
  String? catSociety;

  @override
  Widget body() {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return WillPopScope(
      onWillPop: () {
        return Nav.homePage(context);
      },
      child: FormBuilder(
        key: _fbKey,
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InputText(screenWidth: screenWidth, screenHeight: screenHeight, txt: 'Type'),
              Padding(
                padding:
                    EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.015),
                child: FormBuilderRadioGroup(
                    activeColor: Theme.of(context).primaryColor,
                    initialValue: categoryList[0],
                    name: 'category',
                    decoration: InputStyle().copyWith(contentPadding: EdgeInsets.all(0)),
                    onChanged: (dynamic v) {
                      setState(() {
                        category = v;
                        refreshBlocks(catSociety);
                      });
                    },
                    options: categoryList.map((e) => FormBuilderFieldOption(value: e, child: Text('$e'))).toList(),
                    validator: FormBuilderValidators.required(errorText: 'Category is required')),
              ),
              InputText(screenWidth: screenWidth, screenHeight: screenHeight * 0.5, txt: "Society"),
              Padding(
                padding:
                    EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.015),
                child: DropdownSearch(
                  popupProps: PopupProps.dialog(
                    searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                    showSearchBox: true,
                  ),
                  dropdownDecoratorProps: DropDownDecoratorProps(
                    dropdownSearchDecoration: InputStyle()
                        .copyWith(contentPadding: EdgeInsets.symmetric(horizontal: 15), hintText: 'Select society'),
                  ),
                  onChanged: (dynamic society) {
                    refreshBlocks(society);
                  },
                  items: data!.map((society) => '${society!.title}').toList(),
                  validator: FormBuilderValidators.required(errorText: 'Society is required'),
                ),
              ),
              InputText(screenWidth: screenWidth, screenHeight: screenHeight * 0.5, txt: "Block"),
              Padding(
                padding:
                    EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.015),
                child: DropdownSearch(
                  key: _dropdownKey,
                  popupProps: PopupProps.dialog(
                    searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                    showSearchBox: true,
                  ),
                  dropdownDecoratorProps: DropDownDecoratorProps(
                    dropdownSearchDecoration: InputStyle()
                        .copyWith(contentPadding: EdgeInsets.symmetric(horizontal: 15), hintText: 'Select block'),
                  ),
                  enabled: blocks.length > 0,
                  onChanged: (dynamic val) {
                    block = val;
                    setState(() {
                      plotId = null;
                      _dropdownKeyP.currentState!.clear();
                      plots = [];
                    });
                    if (block != null) getPlot(getBlock()!.id);
                  },
                  items: blocks.map((block) => block!.name).toList(),
                  validator: FormBuilderValidators.required(errorText: 'Block is required'),
                ),
              ),
              InputText(screenWidth: screenWidth, screenHeight: screenHeight * 0.5, txt: "Plot No"),
              Padding(
                padding:
                    EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.015),
                child: DropdownSearch<PlotEntity?>(
                  key: _dropdownKeyP,
                  popupProps: PopupProps.dialog(
                    searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                    showSearchBox: true,
                  ),
                  dropdownDecoratorProps: DropDownDecoratorProps(
                    dropdownSearchDecoration: InputStyle()
                        .copyWith(contentPadding: EdgeInsets.symmetric(horizontal: 15), hintText: 'Select plot No'),
                  ),
                  enabled: plots.length > 0,
                  itemAsString: (plot) => plot!.isVilla == false ? 'Plot - ' + plot.title : 'Villa - ' + plot.title,
                  onChanged: (plot) {
                    if (plot != null)
                      setState(() {
                        plotId = plot.id;
                      });
                  },
                  items: plots,
                  validator: FormBuilderValidators.required(errorText: 'Plot is required'),
                ),
              ),
              Padding(
                padding: EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.07),
                child: GestureDetector(
                  onTap: () {
                    _submitClick();
                    sendAnalyticsEvent(eventName: 'plot_search_button_click', parameters: {'plot_id': plotId});
                  },
                  child: CommonButton(screenWidth: screenWidth, screenHeight: screenHeight, txt: "Search"),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void getData() {
    API.getSocietiesListWithCity().then(this.handleResponse);
    sendAnalyticsScreenEvent(screenName: 'PlotFinderPage');
  }

  void refreshBlocks(String? society) {
    setState(() {
      catSociety = society;
      block = null;
      _dropdownKey.currentState!.clear();
      blocks = [];
      plotId = null;
      _dropdownKeyP.currentState!.clear();
      plots = [];
    });
    int? societyId = data!.firstWhere((element) => element!.title == society)!.id;
    ProgressHUD.of(context)!.show();
    API.blocksList(societyId, cityId: 0, category: category).then((value) {
      ProgressHUD.of(context)!.dismiss();
      if (value.data!.isEmpty) return Notify.showSnack(context, "Blocks ${value.status!.message}");
      print('blocks found.');
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          blocks = value.data!;
        });
    });
  }

  void getPlot(blockId) {
    ProgressHUD.of(context)!.show();
    API.getPlotList(blockId).then((value) {
      ProgressHUD.of(context)!.dismiss();
      if (value.data!.isEmpty) return Notify.showSnack(context, "Plots ${value.status!.message}");
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          plots = value.data!;
        });
    });
  }

  Future<void> _submitClick() async {
    if (_fbKey.currentState!.saveAndValidate()) {
      Notify.progress(context, 'Please wait...');
      APIResponse<PlotEntity> result = await API.getPlotDetail(getBlock()!.id, plotId);
      Notify.hide();
      if (result.status!.status == ApiStatus.OK) {
        var plot = result.data!;
        Map<String, dynamic> body = {};
        body['id'] = plot.id;
        body['action'] = "Plot Search";
        body['model'] = "plot";
        API.postAnalytics(body);
        Nav.singlePlotMap(context, getBlock(), plot);
      } else if (result.status!.status == ApiStatus.NOT_FOUND) {
        Notify.showSnack(context, 'Plot not found, Please try again with different search.');
      } else {
        Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
      }
    }
  }

  BlockEntity? getBlock() {
    return blocks.firstWhere((element) => element!.name == block);
  }
}
