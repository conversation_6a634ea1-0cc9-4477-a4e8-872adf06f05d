import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:image/image.dart' as wmImg;
import 'package:image_gallery_saver_plus/image_gallery_saver_plus.dart';
import 'package:latlong2/latlong.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:share/share.dart';

import '../../models/block_entity.dart';
import '../../models/plot_entity.dart';
import '../../nav.dart';
import '../../utils/Notify.dart';
import '../../utils/base_state.dart';
import '../../utils/functions.dart';
import '../plot_finder/feedback_dialog.dart';
import '../plot_finder/map_buttons.dart';
import 'cached_network_tile_provider.dart';
import 'plot_details_container.dart';

class PlotLocatorPage extends StatefulWidget {
  final BlockEntity block;
  final PlotEntity plot;

  PlotLocatorPage(this.block, this.plot);

  @override
  _PlotLocatorPageState createState() => _PlotLocatorPageState();
}

class _PlotLocatorPageState extends BaseState<PlotLocatorPage, String> with SingleTickerProviderStateMixin {
  final GlobalKey<ScaffoldState> scaffold1 = GlobalKey();
  var scr = new GlobalKey();

  MapController controller = MapController();

  late AnimationController _controller;
  late CurvedAnimation _animation;
  late PermissionStatus info;

  bool changeMarker = false;
  double zoom = 3.0;

  @override
  bool get showAd => true;

  @override
  bool get withAppBar => false;

  @override
  bool get pullToRefresh => false;

  @override
  void getData() {
    zoom = widget.block.maxZoom - (widget.plot.category == 'Commercial' ? 0.0 : 2.0);
    loading = false;
    if (widget.plot.note != null) Future.delayed(Duration(milliseconds: 1)).then((value) => _showNote(context));
    _controller = AnimationController(vsync: this, duration: const Duration(milliseconds: 750));
    _animation = CurvedAnimation(parent: _controller, curve: Curves.elasticOut);
    _controller.repeat(min: 0.15, max: 1.0, reverse: true);
    sendAnalyticsScreenEvent(screenName: 'PlotLocatorPage');
  }

  @override
  void dispose() {
    _controller.stop();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget body() {
    double lat = widget.plot.lat;
    double lng = widget.plot.long;
    return RepaintBoundary(
      key: scr,
      child: Scaffold(
        key: scaffold1,
        appBar: AppBar(title: FittedBox(child: Text('Plot ' + widget.plot.title)), actions: [
          GestureDetector(
            child: Image.asset('images/screenshot.png', color: Colors.white, height: 20, width: 35),
            onTap: changeMarker ? null : plotScreenShot,
          ),
          IconButton(
            icon: Icon(Icons.feedback),
            onPressed: () {
              showDialog<void>(
                context: context,
                barrierDismissible: true,
                builder: (BuildContext dialogContext) {
                  return FeedbackDialog(id: widget.plot.id!);
                },
              );
              sendAnalyticsEvent(
                  eventName: 'plot_locator_feedback_button_click', parameters: {'plot_id': widget.plot.id});
            },
          )
        ]),
        body: Column(
          children: [
            Expanded(
              child: Stack(
                children: [
                  FlutterMap(
                    mapController: controller,
                    options: MapOptions(
                      interactionOptions: const InteractionOptions(
                          flags: InteractiveFlag.all & ~InteractiveFlag.rotate),
                      // interactiveFlags: InteractiveFlag.all & ~InteractiveFlag.rotate,
                      minZoom: widget.block.minZoom.toDouble(),
                      maxZoom: widget.block.maxZoom.toDouble(),
                      // zoom: zoom,
                      initialZoom: zoom,
                      // center: LatLng(lat, lng),
                      initialCenter: LatLng(lat, lng),
                    ),
                    children: [
                      TileLayer(
                        urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                        subdomains: ['a', 'b', 'c'],
                        tileProvider: CachedNetworkTileProvider(),
                      ),
                      TileLayer(
                        // opacity: 1,
                        urlTemplate: "${widget.block.mapUrl}/{z}/{x}/{y}.png",
                        minZoom: widget.block.minZoom.toDouble(),
                        maxZoom: widget.block.maxZoom.toDouble(),
                        tms: false,
                        // backgroundColor: Colors.white.withOpacity(0),
                        tileProvider: CachedNetworkTileProvider(),
                      ),
                      MarkerLayer(markers: [
                        !changeMarker
                            ? Marker(
                                point: LatLng(lat, lng),
                                // anchorPos: AnchorPos.align(AnchorAlign.top),
                                child: Transform.translate(
                                  offset: Offset(0.0, -70 / 2),
                                  child: ScaleTransition(
                                    alignment: Alignment.bottomCenter,
                                    scale: _animation,
                                    child: Icon(Icons.location_pin, color: Theme.of(context).primaryColor, size: 70),
                                  ),
                                ),
                                width: 100,
                                height: 30,
                              )
                            : Marker(
                                point: LatLng(lat, lng),
                                // anchorPos: AnchorPos.align(AnchorAlign.top),
                                child:
                                    Icon(Icons.location_on, color: Theme.of(context).primaryColor, size: 35),
                              )
                      ])
                    ],
                  ),
                  MapButtons(
                    plot: widget.plot,
                    block: widget.block,
                    onPressedPinBtn: () => controller.move(LatLng(widget.plot.lat, widget.plot.long), zoom),
                  ),
                ],
              ),
            ),
            PlotDetailsContainer(plot: widget.plot, blockName: widget.block.name!),
          ],
        ),
      ),
    );
  }

  void _showNote(context) {
    showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text('Note'),
          content: Text('${widget.plot.note}', style: TextStyle(color: Colors.red)),
          actions: [
            TextButton(
              onPressed: () {
                Nav.back(context);
              },
              child: Text("Ok"),
            )
          ],
        );
      },
    );
  }

  Future<void> _requestPermission() async {
    Map<Permission, PermissionStatus> statuses = await [Permission.storage].request();
    info = statuses[Permission.storage]!;
    print('permission:........$info');
    print(info.runtimeType);
  }

  void plotScreenShot() async {
    await _requestPermission();
    if (info == PermissionStatus.permanentlyDenied || info == PermissionStatus.denied) {
      setState(() {
        changeMarker = false;
      });
      Notify.showSnack(context, 'Give the permission of storage to ilaaqa.com app in Settings to save Screenshot.');
      return;
    }
    setState(() {
      changeMarker = true;
    });
    Future.delayed(Duration(milliseconds: 100)).then((_) => saveScreenShot());
    sendAnalyticsEvent(eventName: 'plot_locator_screenshot_button_click', parameters: {'plot_id': widget.plot.id});
  }

  saveScreenShot() async {
    if (info == PermissionStatus.granted) {
      RenderRepaintBoundary boundary = scr.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      final directory = await getTemporaryDirectory();
      File imgFile = File('${directory.path}/screenshot.png');
      imgFile.writeAsBytesSync(byteData!.buffer.asUint8List());
      //================================WaterMark Code=================================//
      File watermark = await getImageFileFromAssets('bahriaplus_watermark.png');
      wmImg.Image? originalImage = wmImg.decodeImage(byteData.buffer.asUint8List());
      wmImg.Image? watermarkImage = wmImg.decodeImage((watermark.readAsBytesSync()));
      wmImg.Image imageW = wmImg.Image( width: originalImage!.width, height: originalImage.height);
      wmImg.compositeImage(imageW, watermarkImage!);
      final int positionX = (originalImage.width - watermarkImage.width) ~/ 2;
      final int positionY = (originalImage.height - watermarkImage.height) ~/ 2;
      wmImg.compositeImage(originalImage, imageW, dstX: positionX, dstY: positionY);
      final File watermarkedFile = File('${(await getTemporaryDirectory()).path}/screenshot.png');
      await watermarkedFile.writeAsBytes(wmImg.encodeJpg(originalImage));
      //===================================End========================================//
      Uint8List bytes = watermarkedFile.readAsBytesSync();
      await ImageGallerySaverPlus.saveImage(bytes, quality: 100);
      setState(() {
        changeMarker = false;
      });
      Notify.customDialog(
        context,
        title: "ScreenShot Saved",
        desc: "Screenshot saved to gallery Pictures Folder.",
        type: DialogType.success,
        btnCancel: TextButton(
          child: new Text("OK"),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
        btnOk: ElevatedButton(
          child: new Text("Share"),
          onPressed: () async {
            Navigator.of(context).pop();
            Share.shareFiles([watermarkedFile.path]);
          },
        ),
      );
      return;
    }
  }

  Future<File> getImageFileFromAssets(String path) async {
    final byteData = await rootBundle.load('images/$path');

    final file = File('${(await getTemporaryDirectory()).path}/$path');
    await file.writeAsBytes(byteData.buffer.asUint8List(byteData.offsetInBytes, byteData.lengthInBytes));

    return file;
  }
}
