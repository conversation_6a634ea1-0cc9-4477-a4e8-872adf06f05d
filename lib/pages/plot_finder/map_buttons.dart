import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:property_dealers_directory/pages/maps_v2/maps_launcher_custom.dart';

import '../../models/block_entity.dart';
import '../../models/plot_entity.dart';
import '../../utils/functions.dart';

class MapButtons extends StatelessWidget {
  final PlotEntity plot;
  final BlockEntity block;
  final void Function()? onPressedPinBtn;

  MapButtons({Key? key, required this.plot, required this.block, required this.onPressedPinBtn}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String? mapLabel;
    if (!kIsWeb) {
      if (Platform.isIOS) {
        mapLabel = "${plot.lat}, ${plot.long}";
      }
      if (Platform.isAndroid) {
        mapLabel = "Plot ${plot.plotNo}, ${block.name}, ${plot.societyName.replaceAll(" -", ",")}";
      }
    }
    return Align(
      alignment: Alignment.bottomRight,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 50,
              width: 50,
              color: Theme.of(context).primaryColor,
              child: IconButton(
                icon: Icon(Icons.pin_drop, size: 35, color: Colors.white),
                onPressed: onPressedPinBtn,
                color: Theme.of(context).primaryColor,
              ),
            ),
            SizedBox(width: 10),
            Container(
              height: 50,
              width: 50,
              color: Theme.of(context).primaryColor,
              child: IconButton(
                icon: Icon(FontAwesomeIcons.diamondTurnRight, size: 35, color: Colors.white),
                onPressed: () {
                  // MapsLauncher.launchCoordinates(plot.lat, plot.long, mapLabel);
                  MapsLauncherCustom.launchCoordinates(plot.lat, plot.long, mapLabel);
                  sendAnalyticsEvent(
                      eventName: 'plot_locator_google_map_button_click', parameters: {'plot_id': plot.id});
                },
                color: Theme.of(context).primaryColor,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
