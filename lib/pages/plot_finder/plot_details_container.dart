import 'package:flutter/material.dart';

import '../../common/main_textField.dart';
import '../../models/plot_entity.dart';

class PlotDetailsContainer extends StatelessWidget {
  const PlotDetailsContainer({Key? key, required this.plot, required this.blockName}) : super(key: key);

  final PlotEntity plot;
  final String blockName;

  @override
  Widget build(BuildContext context) {
    final List<Color> featureClr = [
      Color(0xFFff6666),
      Color(0xFF007f5c),
      Color(0xFF5f65d3),
      Color(0xFF19ca21),
      Color(0xFF60230b)
    ];
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          SizedBox(height: 20),
          Text('${plot.societyName}', style: TextStyle(fontSize: 18, color: Colors.black)),
          Padding(
            padding: EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                LimitedBox(
                  maxWidth: MediaQuery.of(context).size.width * .4,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      LabelWithText(txt1: 'Plot No. ', txt2: '${plot.plotNo}'),
                      SizedBox(height: 10),
                      FittedBox(child: LabelWithText(txt1: 'Block: ', txt2: '$blockName')),
                      SizedBox(height: 10),
                      LabelWithText(txt1: 'Size: ', txt2: '${plot.plotSize} - ${plot.sizeType}')
                    ],
                  ),
                ),
                LimitedBox(
                  maxWidth: MediaQuery.of(context).size.width * .4,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (plot.streetType != 'None')
                        LabelWithText(txt1: '${plot.streetType}: ', txt2: '${plot.streetNo}'),
                      SizedBox(height: 10),
                      LabelWithText(
                        txt1: 'Category: ',
                        txt2: '${plot.category}',
                        color: plot.category == 'Commercial' ? Colors.red : Colors.black,
                      ),
                      // I
                    ],
                  ),
                )
              ],
            ),
          ),
          SizedBox(height: 10),
          if (plot.features != null && plot.features!.isNotEmpty)
            Column(
              children: [
                Text('Features', style: TextStyle(fontSize: 18, color: Colors.black)),
                SizedBox(height: 10),
                Wrap(spacing: 8.0, children: [
                  for (int i = 0; i < plot.features!.length; i++)
                    Chip(
                      label: Text(
                        plot.features![i],
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 12, color: Colors.white),
                      ),
                      backgroundColor: featureClr[i],
                      elevation: 3.0,
                      shadowColor: Colors.grey[60],
                    ),
                ]),
              ],
            ),
          SizedBox(height: 10),
        ],
      ),
    );
  }
}
