import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:dropdown_search/dropdown_search.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_progress_hud/flutter_progress_hud.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart' show toBeginningOfSentenceCase;
import 'package:package_info_plus/package_info_plus.dart';

import '../common/common_button.dart';
import '../common/main_textField.dart';
import '../controllers/login_controller.dart';
import '../data/api.dart';
import '../models/block_entity.dart';
import '../models/inventory_design_entity.dart';
import '../models/property_entity.dart';
import '../models/society_entity.dart';
import '../nav.dart';
import '../utils/Notify.dart';
import '../utils/functions.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class AddPropertyPageV2 extends StatefulWidget {
  final int? propertyId;

  const AddPropertyPageV2({this.propertyId});

  @override
  _AddPropertyPageV2State createState() => _AddPropertyPageV2State();
}

class _AddPropertyPageV2State extends State<AddPropertyPageV2> {
  LoginController login = Get.find();
  final priceController = TextEditingController();
  final _fbKey = GlobalKey<FormBuilderState>();
  final _dropdownBlock = GlobalKey<DropdownSearchState>();
  List purposeList = ['Sale', 'Rent', 'Require'];
  List typesList = ['Residential', 'Commercial'];
  List sizeUnits = ['Marla', 'Kanal', 'Sq. Ft.', 'Sq. Yards', 'Acre'];

  List<SocietyEntity?> societies = [];
  List<InventoryDesignEntity?> designs = [];
  List<dynamic>? categories = [];

  List<BlockEntity?> blocks = [];
  PropertyEntity? property;
  String type = 'Residential';
  String purpose = 'Sale';
  String? category;
  String? cityName;
  int? cityId;
  int? societyId;
  int? gValue;
  int? blockId;
  PackageInfo? uaData;
  bool checkBoxValue = false;
  bool sizeEnter = false;
  int price = 0;

  @override
  void initState() {
    getAppData();
    if (widget.propertyId == null) {
      getSocietiesListWithCity();
      getCategories();
      getDesignList();
    } else {
      getPropertyDetail(widget.propertyId);
    }
    sendAnalyticsScreenEvent(screenName: widget.propertyId == null ? 'AddInventoryPageV2' : 'UpdateInventoryPageV2');
    super.initState();
  }

  getPropertyDetail(id) async {
    await API.getPropertyDetail(id).then((value) {
      if (value.status!.status == ApiStatus.OK) {
        setState(() {
          property = value.data!;
          property?.price == 0 ? checkBoxValue = true : checkBoxValue = false;
          priceController.text = property!.price.toString() == "0" ? "" : property!.price.toString();
          if (property!.size.toString() != "0" && property!.size.toString() != "null") sizeEnter = true;
        });
        getDesignList();
      }
    });
  }

  getSocietiesListWithCity() async {
    await API.getSocietiesListWithCityCached().then((value) {
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          societies.addAll(value.data!);
        });
    });
  }

  getCategories() async {
    APIResponse<dynamic> result = await API.getInventoryCategories();
    if (result.status!.status == ApiStatus.OK)
      setState(() {
        categories = result.data;
        categories!.removeAt(0);
      });
  }

  getBlockList(id) {
    ProgressHUD.of(context)!.show();
    final ScaffoldMessengerState scaffoldMessenger = ScaffoldMessenger.of(context);
    API.blocksList(id, cityId: 0, category: type, inActive: "&inactive=1").then((value) {
      ProgressHUD.of(context)!.dismiss();
      if (value.data!.isEmpty && societyId != null)
        return scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text("$type Blocks ${value.status!.message}"),
            action: SnackBarAction(label: 'OK', onPressed: () {}),
          ),
        );
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          blocks = value.data!;
        });
    });
  }

  getDesignList() {
    API.getInventoryDesign().then((value) {
      if (value.status!.status == ApiStatus.OK)
        setState(() {
          var list = value.data!;
          if (login.user!.agency!.isFeatured)
            list.sort((a, b) => b.forTitanium.toString().compareTo(a.forTitanium.toString()));
          designs = list;
          if (widget.propertyId == null)
            gValue = designs[0]!.id;
          else
            gValue = property!.designId;
        });
    });
  }

  Future<PackageInfo?> getAppData() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      uaData = packageInfo;
    });
    return uaData;
  }

  convertToRupee() {
    var length = price.toString().length;
    if (length >= 6 && length <= 7) {
      return (price / 100000).toPrecision(2).toString() + ' Lakh';
    } else if (length >= 8) {
      return (price / 100000).toStringAsFixed(0) + ' Lakh';
    } else if (length >= 4 && length <= 5) {
      return (price / 1000).toPrecision(2).toString() + ' Thousand';
    } else {
      return price;
    }
  }

  @override
  Widget build(BuildContext context) {
    double sWidth = MediaQuery.of(context).size.width;
    double sHeight = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(title: Text(widget.propertyId == null ? 'Add Inventory' : 'Update Inventory')),
      body: widget.propertyId != null && property == null
          ? Center(child: Loader())
          : SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.symmetric(horizontal: sWidth * .05, vertical: sHeight * .02),
                child: FormBuilder(
                  key: _fbKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.propertyId == null)
                        Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Purpose'),
                                InputText(
                                    screenWidth: sWidth * 0.1,
                                    screenHeight: sHeight * 0.5,
                                    txt: 'ایڈ کے مقصد کا انتخاب کریں'),
                              ],
                            ),
                            SizedBox(height: 5.0),
                            FormBuilderRadioGroup(
                              activeColor: Theme.of(context).primaryColor,
                              enabled: widget.propertyId == null ? true : false,
                              initialValue: widget.propertyId != null
                                  ? toBeginningOfSentenceCase(property?.purpose) ?? ''
                                  : purposeList[0],
                              name: 'purpose',
                              onChanged: (dynamic v) {
                                setState(() {
                                  purpose = v;
                                });
                              },
                              decoration: InputStyle().copyWith(contentPadding: EdgeInsets.symmetric(vertical: 3)),
                              options:
                                  purposeList.map((e) => FormBuilderFieldOption(value: e, child: Text('$e'))).toList(),
                              validator: FormBuilderValidators.required(errorText: 'Purpose is required'),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Type'),
                                InputText(
                                    screenWidth: sWidth * 0.1,
                                    screenHeight: sHeight * 0.5,
                                    txt: 'ایڈ ٹائپ کا انتخاب کریں'),
                              ],
                            ),
                            SizedBox(height: 5.0),
                            FormBuilderRadioGroup(
                              activeColor: Theme.of(context).primaryColor,
                              enabled: widget.propertyId == null ? true : false,
                              initialValue: widget.propertyId != null
                                  ? toBeginningOfSentenceCase(property?.typeV2) ?? ''
                                  : typesList[0],
                              name: 'type',
                              onChanged: (dynamic v) {
                                setState(() {
                                  type = v;
                                  _dropdownBlock.currentState?.clear();
                                  blocks = [];
                                  blockId = null;
                                  if (societyId != null) getBlockList(societyId);
                                });
                              },
                              decoration: InputStyle().copyWith(contentPadding: EdgeInsets.symmetric(vertical: 3)),
                              options:
                                  typesList.map((e) => FormBuilderFieldOption(value: e, child: Text('$e'))).toList(),
                              validator: FormBuilderValidators.required(errorText: 'Type is required'),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Category'),
                                InputText(
                                    screenWidth: sWidth * 0.1,
                                    screenHeight: sHeight * 0.5,
                                    txt: 'جس چیز کا ایڈلگا رہے ہیں'),
                              ],
                            ),
                            SizedBox(height: 5.0),
                            FormBuilderDropdown(
                              initialValue: widget.propertyId != null ? property?.category ?? '' : null,
                              name: 'category',
                              decoration: InputStyle(hint: 'Select category'),
                              onChanged: (dynamic v) {
                                setState(() {
                                  category = v;
                                });
                              },
                              items: categories == null || categories!.isEmpty
                                  ? []
                                  : categories!.map((e) => DropdownMenuItem(value: e, child: Text('$e'))).toList(),
                              validator: FormBuilderValidators.required(errorText: 'Category is required'),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Society'),
                                InputText(
                                    screenWidth: sWidth * 0.1,
                                    screenHeight: sHeight * 0.5,
                                    txt: 'سوسائٹی کا انتخاب کریں'),
                              ],
                            ),
                            SizedBox(height: 5.0),
                            DropdownSearch(
                              selectedItem: widget.propertyId != null
                                  ? "${property?.society?.name} - ${property?.city?.name}"
                                  : null,
                              enabled: societies.isEmpty ? false : societies.length > 0,
                              popupProps: PopupProps.dialog(
                                searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                                showSearchBox: true,
                              ),
                              dropdownDecoratorProps: DropDownDecoratorProps(
                                dropdownSearchDecoration: InputStyle().copyWith(
                                    contentPadding: EdgeInsets.only(left: 18, top: 3, bottom: 3),
                                    hintText: 'Select society'),
                              ),
                              onChanged: (dynamic society) {
                                print(society);
                                societyId = societies.isEmpty
                                    ? null
                                    : societies.firstWhere((element) => element?.title == society)?.id;
                                cityId = societies.isEmpty
                                    ? null
                                    : societies.firstWhere((element) => element?.title == society)?.cityId;
                                setState(() {
                                  cityName = societies.isEmpty
                                      ? null
                                      : societies.firstWhere((element) => element?.title == society)?.cityName;
                                });
                                _dropdownBlock.currentState?.clear();
                                blocks = [];
                                blockId = null;
                                getBlockList(societyId);
                              },
                              items: societies.isEmpty ? [] : societies.map((e) => '${e!.title}').toList(),
                              validator: FormBuilderValidators.required(errorText: 'Society is required'),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                InputText(
                                    screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Block (Optional)'),
                                InputText(
                                    screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'بلاک کا انتخاب کریں'),
                              ],
                            ),
                            SizedBox(height: 5.0),
                            DropdownSearch(
                              key: _dropdownBlock,
                              selectedItem: widget.propertyId != null ? property?.block?.name : null,
                              enabled: blocks.isEmpty ? false : blocks.length > 0,
                              popupProps: PopupProps.dialog(
                                searchFieldProps: TextFieldProps(decoration: InputStyle(hint: 'Search here.')),
                                showSearchBox: true,
                              ),
                              dropdownDecoratorProps: DropDownDecoratorProps(
                                dropdownSearchDecoration: InputStyle().copyWith(
                                    contentPadding: EdgeInsets.only(left: 18, top: 3, bottom: 3),
                                    hintText: 'Select block'),
                              ),
                              clearButtonProps: ClearButtonProps(isVisible: blockId != null ? true : false),
                              onChanged: (dynamic block) {
                                if (block != null)
                                  setState(() {
                                    blockId = blocks.isEmpty
                                        ? null
                                        : blocks.firstWhere((element) => element!.name == block)!.id;
                                  });
                              },
                              items: blocks.isEmpty ? [] : blocks.map((e) => '${e!.name}').toList(),
                            ),
                          ],
                        ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Size'),
                          InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'سائز'),
                        ],
                      ),
                      SizedBox(height: 5.0),
                      Row(
                        children: [
                          Container(
                            width: sWidth * .46,
                            child: FormBuilderTextField(
                              name: 'size',
                              initialValue: widget.propertyId != null
                                  ? ((property!.size.toString() == "0" || property!.size.toString() == "null")
                                      ? ""
                                      : property!.size.toString())
                                  : null,
                              keyboardType: TextInputType.number,
                              onChanged: (v) {
                                setState(() {
                                  if (v!.isEmpty) {
                                    _fbKey.currentState?.fields['size_unit']?.reset();
                                    _fbKey.currentState?.fields['size_unit']?.didChange(null);
                                    sizeEnter = false;
                                  } else {
                                    sizeEnter = true;
                                  }
                                });
                              },
                              decoration: InputStyle(hint: 'Enter size'),
                              validator: FormBuilderValidators.compose(
                                  [FormBuilderValidators.min(1, errorText: 'Size should be greater \nthen 0')]),
                            ),
                          ),
                          SizedBox(width: 10.0),
                          Container(
                            width: sWidth * .412,
                            child: FormBuilderDropdown(
                                name: 'size_unit',
                                enabled: sizeEnter,
                                initialValue: widget.propertyId != null ? property?.sizeUnit ?? null : null,
                                decoration: InputStyle(hint: 'e.g Sq.Yard'),
                                // icon: Text('e.g Sq.Yard', style: TextStyle(fontSize: 12)),
                                items: sizeUnits.map((e) => DropdownMenuItem(value: e, child: Text('$e'))).toList(),
                                validator: sizeEnter
                                    ? FormBuilderValidators.required(errorText: 'Size Unit is required')
                                    : null),
                          ),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Price (PKR)'),
                          InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'ڈیمانڈ'),
                        ],
                      ),
                      SizedBox(height: 5.0),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: sWidth * .5,
                            child: FormBuilderTextField(
                              controller: priceController,
                              enabled: !checkBoxValue,
                              name: 'price',
                              onChanged: (value) {
                                if (value != '')
                                  setState(() {
                                    price = int.parse(value!);
                                  });
                              },
                              keyboardType: TextInputType.number,
                              enableSuggestions: true,
                              decoration: InputStyle(hint: checkBoxValue ? 'On Call' : 'Full price e.g 27000.'),
                              validator: !checkBoxValue
                                  ? FormBuilderValidators.compose([
                                      FormBuilderValidators.required(errorText: 'Price is required'),
                                      FormBuilderValidators.min(1000, errorText: 'Please enter min price 1000')
                                    ])
                                  : null,
                            ),
                          ),
                          Container(
                            width: sWidth * .37,
                            child: CheckboxListTile(
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: EdgeInsets.all(0),
                              title: Text(
                                'On Call',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              activeColor: Theme.of(context).primaryColor,
                              value: checkBoxValue,
                              onChanged: (v) {
                                setState(() {
                                  _fbKey.currentState!.fields['price']!.reset();
                                  if (widget.propertyId != null)
                                    priceController.text = (v! ? '' : property?.price.toString())!;
                                  else
                                    priceController.text = '';
                                  price = 0;
                                  checkBoxValue = !checkBoxValue;
                                });
                              },
                            ),
                          )
                        ],
                      ),
                      if (price.toString().length > 1 && price != 0)
                        Text('${convertToRupee()}', style: TextStyle(color: Colors.black54, fontSize: 12)),
                      SizedBox(height: 5.0),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(bottom: 10),
                            child:
                                InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Description'),
                          ),
                          Text(
                            'اس سیکشن میں اپنانام، موبائل نمبر \nاور آفس کا نام لکھنے کی ضرورت نہیں ہے!',
                            textDirection: TextDirection.rtl,
                            style: TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ],
                      ),
                      SizedBox(height: 5.0),
                      FormBuilderTextField(
                        initialValue: widget.propertyId != null ? property?.note ?? '' : null,
                        maxLength: 200,
                        name: 'description',
                        maxLines: 5,
                        // inputFormatters: [MaxLinesTextInputFormatter(5)],
                        textCapitalization: TextCapitalization.sentences,
                        enableSuggestions: true,
                        decoration: InputStyle(hint: 'Enter description'),
                        validator: FormBuilderValidators.required(errorText: 'Description is required'),
                      ),
                      InputText(screenWidth: sWidth * 0.1, screenHeight: sHeight * 0.5, txt: 'Design'),
                      SizedBox(height: 10.0),
                      Container(
                        height: 170,
                        child: ListView.builder(
                          shrinkWrap: true,
                          scrollDirection: Axis.horizontal,
                          itemExtent: 140,
                          itemCount: designs.length,
                          itemBuilder: (context, i) {
                            return Column(
                              children: [
                                GestureDetector(
                                    onTap: () {
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => ViewDesign(photo: designs[i]!.image)));
                                    },
                                    child: Hero(
                                        tag: designs[i]!.link,
                                        child: ClipRect(
                                          clipBehavior: Clip.antiAliasWithSaveLayer,
                                          child: Stack(
                                            children: [
                                              NetworkImageWithPlaceholder(designs[i]!.link,
                                                  height: 120, width: 120, radius: 8.0),
                                              if (designs[i]!.forTitanium)
                                                Positioned(
                                                  right: 0,
                                                  child: Banner(
                                                    location: BannerLocation.topEnd,
                                                    color: Colors.red,
                                                    message: 'Titanium',
                                                  ),
                                                )
                                            ],
                                          ),
                                        ))),
                                (designs[i]!.forTitanium && login.user!.agency!.isFeatured) || !designs[i]!.forTitanium
                                    ? Radio(
                                        activeColor: Theme.of(context).primaryColor,
                                        value: designs[i]!.id,
                                        groupValue: widget.propertyId == null
                                            ? (gValue == null ? designs[0]!.id : gValue)
                                            : (gValue == null ? property?.designId : gValue),
                                        onChanged: (dynamic v) => setState(() {
                                          gValue = v;
                                          print('...........................................');
                                          print(gValue);
                                        }),
                                      )
                                    : Padding(padding: const EdgeInsets.all(10.0), child: Text('Only for Titanium')),
                              ],
                            );
                          },
                        ),
                      ),
                      SizedBox(height: 10.0),
                      GestureDetector(
                        onTap: () {
                          if (_fbKey.currentState!.saveAndValidate()) {
                            addAction();
                          }
                        },
                        child: CommonButton(
                            screenHeight: sHeight,
                            screenWidth: sWidth,
                            txt: widget.propertyId != null ? 'Update' : 'Submit'),
                      ),
                      SizedBox(height: 10.0),
                    ],
                  ),
                ),
              ),
            ),
    );
  }

  Future<void> addAction() async {
    Notify.progress(context, 'Please Wait...');
    var data = {
      'app_version': '${uaData?.version}.' + '${uaData?.buildNumber}',
      'city_id': cityId,
      'society_id': societyId,
      'block_id': blockId,
      'type': type.toLowerCase(),
      'purpose': purpose.toLowerCase(),
      'category': category?.toLowerCase(),
      'design': gValue,
      'price':
          (checkBoxValue || _fbKey.currentState?.value['price'] == null || _fbKey.currentState!.value['price'].isEmpty)
              ? 0
              : _fbKey.currentState!.value['price'],
      'note': _fbKey.currentState!.value['description'].isEmpty ? null : _fbKey.currentState!.value['description'],
      'size': (_fbKey.currentState!.value['size'] == null || _fbKey.currentState!.value['size'].isEmpty)
          ? null
          : _fbKey.currentState!.value['size'],
      'size_unit': (_fbKey.currentState!.value['size_unit'] == null || _fbKey.currentState!.value['size_unit'].isEmpty)
          ? null
          : _fbKey.currentState!.value['size_unit'],
    };
    APIResponse<dynamic> result;
    if (widget.propertyId != null)
      result = await API.updateProperty(widget.propertyId, data);
    else
      result = await API.addProperty(data);

    if (result.status!.status == ApiStatus.OK) {
      Notify.toast(widget.propertyId != null ? "Inventory Updated" : "Inventory Added", type: 1);
      Notify.hide();
      Nav.back(context);
    } else {
      Notify.hide();
      print(result.status!.message);
      Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
    }
    sendAnalyticsEvent(
        eventName: widget.propertyId != null ? 'update_inventory_button_click' : 'add_inventory_button_click');
  }
}

class ViewDesign extends StatelessWidget {
  final String? photo;

  const ViewDesign({Key? key, this.photo}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("View Design")),
      body: InteractiveViewer(
        child: Hero(
          tag: photo!,
          child: NetworkImageWithPlaceholder(
            photo!,
            height: MediaQuery.of(context).size.height,
            width: MediaQuery.of(context).size.width,
            cover: false,
          ),
        ),
      ),
    );
  }
}
