import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:cropperx/cropperx.dart';
import 'package:dio/dio.dart' show FormData, MultipartFile;
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:percent_indicator/percent_indicator.dart';
import '../services/remote_config.dart';

import '../common/common_button.dart';
import '../controllers/login_controller.dart';
import '../data/api.dart';
import '../models/user_plan_entity.dart';
import '../nav.dart';
import '../utils/Notify.dart';
import '../utils/base_state.dart';
import '../utils/functions.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class UpdateProfilePage extends StatefulWidget {
  @override
  _UpdateProfilePageState createState() => _UpdateProfilePageState();
}

class _UpdateProfilePageState extends BaseState<UpdateProfilePage, UserPlanEntity> {
  final GlobalKey<FormBuilderState> _fKey = GlobalKey<FormBuilderState>();
  final GlobalKey _cropperKey = GlobalKey(debugLabel: 'cropperKey');
  final ImagePicker picker = ImagePicker();
  final LoginController controller = Get.find();

  bool isPlan = false;
  Uint8List? _imageToCrop;
  Uint8List? _croppedImage;
  File? imageFile;

  @override
  String get title => 'Update Profile';

  @override
  bool get pullToRefresh => false;

  @override
  void getData() {
    API.getUserPlan().then(this.handleResponse);
    isPlan = true;
    sendAnalyticsScreenEvent(screenName: 'UpdateProfilePage');
  }

  @override
  Widget body() {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    List<UserPlanFeatures> features = data!.features!;
    if(!RemoteConfigService.enableMarketInventory){
      features.removeWhere((element) => element.name.contains('Market Inventory'));
    }
    return SingleChildScrollView(
      child: Column(
        children: [
          SizedBox(height: 20),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                imageFile != null && _croppedImage != null
                    ? Stack(
                        children: [
                          Image.memory(_croppedImage!, fit: BoxFit.cover, width: 150, height: 150),
                          Positioned(
                            top: 5,
                            right: 5,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  imageFile = null;
                                  _imageToCrop = null;
                                  _croppedImage = null;
                                });
                              },
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(0.8), borderRadius: BorderRadius.circular(20)),
                                child: Icon(Icons.close, color: Colors.white),
                              ),
                            ),
                          ),
                        ],
                      )
                    : SizedBox(
                        width: 150, height: 150, child: NetworkImageWithPlaceholder(controller.user!.staff!.imageUrl)),
                Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    GestureDetector(
                        onTap: () {
                          imageFile != null && _croppedImage != null
                              ? _onPressed()
                              : showModalBottomSheet(
                                  isScrollControlled: true,
                                  shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(30), topLeft: Radius.circular(30))),
                                  context: context,
                                  builder: (context) {
                                    return Container(
                                      height: screenHeight * 0.2,
                                      child: Padding(
                                        padding: const EdgeInsets.all(20),
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            ListTile(
                                              leading: Icon(Icons.camera_alt),
                                              title: Text("Camera"),
                                              onTap: () {
                                                Nav.back(context);
                                                pickImagesFromCamera();
                                              },
                                            ),
                                            ListTile(
                                              leading: Icon(Icons.photo),
                                              title: Text("Gallery"),
                                              onTap: () {
                                                Nav.back(context);
                                                pickImagesFromGallery();
                                              },
                                            )
                                          ],
                                        ),
                                      ),
                                    );
                                  });
                        },
                        child: CommonButton(
                            screenHeight: screenHeight,
                            screenWidth: screenWidth / 2.5,
                            txt: imageFile != null && _croppedImage != null ? "Save" : "Change Photo")),
                    SizedBox(height: 10),
                    GestureDetector(
                        onTap: () {
                          showDialog(
                            context: context,
                            barrierDismissible: true,
                            builder: (context) {
                              return AlertDialog(
                                title: Heading.big('Change Password'),
                                content: FormBuilder(
                                  key: _fKey,
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      FormBuilderTextField(
                                        name: 'password',
                                        decoration: InputDecoration(hintText: 'Enter new password'),
                                        validator: FormBuilderValidators.required(errorText: 'Password is required'),
                                      ),
                                      FormBuilderTextField(
                                        name: 'password_confirmation',
                                        validator:
                                            FormBuilderValidators.required(errorText: 'Confirm password is required'),
                                        decoration: InputDecoration(hintText: 'Confirm password'),
                                      ),
                                    ],
                                  ),
                                ),
                                actions: [
                                  TextButton(
                                      onPressed: () {
                                        Nav.back(context);
                                      },
                                      child: Text('Cancel')),
                                  TextButton(
                                      onPressed: () async {
                                        if (_fKey.currentState!.saveAndValidate()) {
                                          if (_fKey.currentState!.value['password'] ==
                                              _fKey.currentState!.value['password_confirmation']) {
                                            Notify.progress(context, 'Please wait...');
                                            APIResponse result = await API.changePassword(_fKey.currentState!.value);
                                            Notify.hide();
                                            if (result.status!.status == ApiStatus.OK) {
                                              Notify.toast('Password Change Successfully', type: 1);
                                              Nav.back(context);
                                            } else {
                                              Notify.customDialog(context,
                                                  desc: result.status!.message, type: DialogType.error);
                                            }
                                          } else {
                                            Notify.toast('Password Not Match');
                                          }
                                        }
                                      },
                                      child: Text('Confirm')),
                                ],
                              );
                            },
                          );
                          sendAnalyticsEvent(eventName: 'changed_password_button_click');
                        },
                        child: CommonButton(
                            screenHeight: screenHeight, screenWidth: screenWidth / 2.5, txt: 'Change Password')),
                  ],
                )
              ],
            ),
          ),
          SizedBox(height: 10),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Heading('Package:  '),
                    isPlan
                        ? (data!.expired
                            ? Container(
                                height: 20,
                                width: 60,
                                decoration: BoxDecoration(color: Colors.red, borderRadius: BorderRadius.circular(10)),
                                child: Center(
                                  child: Text(
                                    'Expired',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ))
                            : Container(
                                height: 20,
                                width: 50,
                                decoration: BoxDecoration(color: Colors.green, borderRadius: BorderRadius.circular(10)),
                                child: Center(
                                  child: Text(
                                    'Active',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                )))
                        : Container(
                            height: 5,
                            width: 50,
                            child: LinearProgressIndicator(
                              color: Theme.of(context).primaryColor,
                              backgroundColor: Colors.grey[200],
                            )),
                  ],
                ),
              ],
            ),
          ),
          for (int i = 0; i < features.length; i++)
            Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                child: Row(
                  children: [
                    Container(
                      width: screenWidth * .38,
                      child: Heading('${features[i].name}', font: 14),
                    ),
                    LinearPercentIndicator(
                      trailing: Text('${features[i].consumed} / ${features[i].limit}'),
                      backgroundColor: Colors.grey[200]!,
                      progressColor: Theme.of(context).primaryColor,
                      width: screenWidth * .39,
                      lineHeight: 14.0,
                      percent: features[i].consumed.toDouble() / double.parse('${features[i].limit}'),
                    ),
                  ],
                ))
        ],
      ),
    );
  }

  Future<void> pickImagesFromCamera() async {
    XFile? result;
    try {
      result = await picker.pickImage(source: ImageSource.camera);
    } on Exception catch (e) {
      print(e);
    }
    _imageToCrop = await result?.readAsBytes();
    setState(() {
      imageFile = File(result!.path);
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return _cropperScreen();
          });
    });
  }

  Future<void> pickImagesFromGallery() async {
    XFile? result;
    try {
      result = await (picker.pickImage(source: ImageSource.gallery));
    } on Exception catch (e) {
      print(e);
    }
    _imageToCrop = await result?.readAsBytes();
    setState(() {
      imageFile = File(result!.path);
      showDialog(
          context: context,
          builder: (BuildContext context) {
            return _cropperScreen();
          });
    });
  }

  _cropperScreen() {
    return Scaffold(
      appBar: AppBar(title: Text("Crop Image")),
      body: Padding(
        padding: const EdgeInsets.all(10),
        child: Column(
          children: [
            _imageToCrop != null
                ? Cropper(
                    cropperKey: _cropperKey,
                    overlayType: OverlayType.rectangle,
                    image: Image.memory(_imageToCrop!),
                  )
                : const ColoredBox(color: Colors.grey),
            const SizedBox(height: 10),
            ElevatedButton(
              child: const Text('Crop Image'),
              onPressed: () async {
                final imageBytes = await Cropper.crop(cropperKey: _cropperKey, pixelRatio: 1.5);
                if (imageBytes != null) {
                  setState(() {
                    _croppedImage = imageBytes;
                    imageFile = File.fromRawPath(imageBytes);
                  });
                  Nav.back(context);
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  _onPressed() async {
    FormData formData;
    formData = FormData.fromMap({'photo': MultipartFile.fromBytes(_croppedImage!, filename: "abc")});
    Notify.progress(context, 'Please wait...');
    APIResponse result = await API.changeStaffProfilePic(formData);
    Notify.hide();
    if (result.status!.status == ApiStatus.OK) {
      setState(() {
        controller.user!.staff!.image = result.data['path'];
        controller.storage.write('USER_INFO', jsonEncode(controller.user));
      });
      Notify.toast('Profile photo Change Successfully', type: 1);
      Nav.back(context);
    } else {
      Notify.customDialog(context, desc: result.status!.errorMessage, type: DialogType.error);
    }
    sendAnalyticsEvent(eventName: 'change_profile_picture_button_click');
  }
}
