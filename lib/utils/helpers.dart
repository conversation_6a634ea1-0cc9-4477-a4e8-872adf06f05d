import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';

Future openLink(String s) async {
  try {
    s = Uri.encodeFull(s);
    if (await canLaunch(s)) {
      await launch(s);
    }else{
        print_("Cannot Launch openLink");
    }
  } catch (e) {
      print_("Exception in openLink");
      print_(e);
  }
}

Future openWhatsApp(String phone, {String message = ''}) async{

  var url = 'https://wa.me/+92${phone.substring(1)}';
  url += message.isNotEmpty ? '?text=$message' : '';
  // url = Uri.encodeFull(url);
  await openLink(url);

}

String upperFirst(String input) => '${input[0].toUpperCase()}${input.substring(1)}';

print_(dynamic content){
    if(kDebugMode) {
      print(content);
    }
  }
