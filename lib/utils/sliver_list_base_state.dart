import 'package:flutter/material.dart';

import 'sliver_base_state.dart';

abstract class SliverListBaseState<T extends StatefulWidget, U extends List> extends SliverBaseState<T, U> {
  Widget body() => Container();

  @override
  Widget sliverBody() {
    return SliverList(
      delegate: SliverChildBuilderDelegate(
            (_context, index) {
          if (index == data!.length) return paginateLoader();
          return listItem(index);
        },
        childCount: paginate ? data!.length + 1 : data!.length,
      ),
    );
  }

  Widget listItem(int index);
}