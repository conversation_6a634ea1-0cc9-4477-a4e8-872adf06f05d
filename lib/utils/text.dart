import 'package:flutter/material.dart';
import 'package:property_dealers_directory/theme.dart';

class Heading extends StatelessWidget {
  final String text;
  final double font;
  final FontWeight weight;
  final Color? color;

  Heading(this.text, {this.color, this.font = 20.0, this.weight = FontWeight.w400});

  Heading.big(this.text, {this.font = 25.0, this.weight = FontWeight.bold, this.color});
  const Heading.input(this.text,
      {Key? key, this.color = Colors.black, this.font = 16.0, this.weight = FontWeight.w500, })
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(color: color ?? Theme.of(context).primaryColor, fontWeight: weight, fontSize: font),
    );
  }
}

class HeadingText extends StatelessWidget {
  const HeadingText({Key? key, required this.screenWidth, required this.screenHeight, required this.txt}) : super(key: key);

  final double screenWidth;
  final double screenHeight;
  final String txt;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: screenWidth * 0.1, top: screenHeight * 0.06),
      child: Text(
        txt,
        style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold, fontSize: 30),
      ),
    );
  }
}

class SubHeadingText extends StatelessWidget {
  const SubHeadingText({
    Key? key,
    required this.screenWidth,
    required this.screenHeight,
    required this.txt,
  }) : super(key: key);

  final double screenWidth;
  final double screenHeight;
  final String txt;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: screenWidth * 0.1, top: screenHeight * 0.02),
      child: Text(
        txt,
        style: TextStyle(color: Color(0xff8d8f8e), fontWeight: FontWeight.w500),
      ),
    );
  }
}

class InputText extends StatelessWidget {
  const InputText({
    Key? key,
    required this.screenWidth,
    required this.screenHeight,
    required this.txt,
  }) : super(key: key);

  final double screenWidth;
  final double screenHeight;
  final String txt;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: screenWidth * 0.08, top: screenHeight * 0.05),
      child: Text(
        txt,
        style: TextStyle(color: Colors.black, fontWeight: FontWeight.w500),
      ),
    );
  }
}

class RichHeading extends StatelessWidget {
  RichHeading({
    super.key,
    required this.start,
    required this.middle,
    required this.end,
    this.fontSize = 20,
    this.padding = 8.0,
  });

  late String start;
  late String middle;
  late String end;
  final double fontSize;
  final double padding;

  RichHeading.auto({
    required String text,
    this.fontSize = 20,
    this.padding = 8.0,
  }) {
    var split = text.split(' ');
    if (split.length == 1) {
      this.start = split[0];
      middle = '';
      end = '';
    } else if (split.length == 2) {
      start = split[0];
      middle = ' ' + split[1];
      end = '';
    } else {
      start = split[0];
      middle = ' ' + split[1];
      end = ' ' + split.sublist(2).join(' ');
    }
  }

  @override
  Widget build(BuildContext context) {
    final TextStyle style = TextStyle(
      fontSize: fontSize,
      fontWeight: FontWeight.w900,
      fontFamily: 'Comfortaa',
      color: MyColors.primary,
    );
    return Padding(
      padding: EdgeInsets.all(padding),
      child: RichText(
        text: TextSpan(
          children: [
            TextSpan(
              text: start,
              style: style,
            ),
            TextSpan(
              text: middle,
              style: style.copyWith(color: MyColors.secondary),
            ),
            TextSpan(
              text: end,
              style: style,
            ),
          ],
        ),
      ),
    );
  }
}