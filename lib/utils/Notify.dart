import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

import '../utils/widgets.dart';

class Notify {
  static AwesomeDialog? dialog;

  static AwesomeDialog progress(context, String message) {
    AwesomeDialog pr = AwesomeDialog(
      context: context,
      width: kIsWeb ? 300 : MediaQuery.of(context).size.width,
      dialogType: DialogType.noHeader,
      body: Padding(
        padding: const EdgeInsets.only(bottom: 30),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Loader(),
            Text(message, style: TextStyle(fontSize: 20, color: Colors.black, fontWeight: FontWeight.w600))
          ],
        ),
      ),
    );
    pr.show();
    dialog = pr;
    return pr;
  }

  static AwesomeDialog customDialog(context,
      {String? title,
      String? desc,
      Widget? body,
      DialogType type = DialogType.success,
      Widget? btnCancel,
      Widget? btnOk}) {
    AwesomeDialog pr = AwesomeDialog(
      context: context,
      title: type == DialogType.error ? 'Error' : title,
      desc: desc,
      body: body,
      dialogType: type,
      btnCancel: btnCancel,
      btnOk: type == DialogType.error
          ? TextButton(
              child: new Text("OK"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            )
          : btnOk,
      headerAnimationLoop: false,
    );
    pr.show();
    return pr;
  }

  static void hide() {
    if (dialog != null) dialog?.dismiss();
    dialog = null;
  }

  static void showSnack(key, text, {String? label, Function? pressed}) {

    ScaffoldMessenger.of(key).showSnackBar(
      SnackBar(
        content: Text(text),
        action: SnackBarAction(
            label: label ?? 'OK',
            onPressed: () {
              if (pressed != null) pressed();
            }),
      ),
    );
  }

  static void showError(context, message, {title = 'Error', action = 'OK', Function? pressed}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: <Widget>[
            TextButton(
              child: new Text("OK"),
              onPressed: () {
                if (pressed != null) {
                  pressed();
                } else {
                  Navigator.of(context).pop();
                }
              },
            ),
          ],
        );
      },
    );
  }

  static void toast(message, {type = 0, color = Colors.red}) {
    Fluttertoast.showToast(
        msg: message,
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: 2,
        backgroundColor: type == 0 ? Colors.red : Colors.green,
        textColor: Colors.white,
        fontSize: 16.0);
  }
}
