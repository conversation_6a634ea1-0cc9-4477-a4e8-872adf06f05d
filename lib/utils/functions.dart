import 'dart:io' show Platform;

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/my_app.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';


sendAnalyticsScreenEvent({required String screenName}) {
  if (kIsWeb) return;
  if (Platform.isIOS) return;
  final firebase = FirebaseAnalytics.instance;
  firebase.setCurrentScreen(screenName: screenName);
}

sendAnalyticsEvent({required String eventName, Map<String, dynamic>? parameters}) {
  if (kIsWeb) return;
  if (Platform.isIOS) return; //TODO test this on iOS
  final firebase = FirebaseAnalytics.instance;

  Map<String, Object> map = {'selected_city': obProvider.city.name};

  if (parameters != null) {
    parameters.forEach((key, value)  {
      map[key] = value??'';

    });
    // map.addAll(parameters);
  }

  firebase.logEvent(name: eventName, parameters: map);
  // try {
  //   final fb = FacebookAppEvents();
  //   fb.logEvent(name: 'User_Properties', parameters: {
  //     'Selected_City': city.city?.name,
  //     'User_Type': user.userType,
  //   });
  //   fb.logEvent(name: eventName, parameters: map);
  // }catch(e){
  //
  // }
}
