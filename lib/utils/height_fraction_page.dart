import 'package:flutter/material.dart';

double getAgencyFraction(_context) {
  var width = MediaQuery.of(_context).size.width;
  if (width < 500) {
    return 0.25;
  }
  if (width < 768) {
    return 0.18;
  }
  if (width < 900) {
    return 0.18;
  }
  if (width < 1500) {
    return 0.1;
  }
  return 0.1;
}

double getAgencyHeight(_context) {
  var height = MediaQuery.of(_context).size.height;
  var width = MediaQuery.of(_context).size.width;

  if (height <= 500 && width <= 500) {
    return height * .23;
  }
  if (height <= 500 && width > 500) {
    return height * .23;
  }
  if (height <= 680 && width <= height) {
    return height * .17;
  }
  if (height < 768 && width < height) {
    return height * .155;
  }
  if (height < 768 && width > height) {
    return height * .16;
  }
  if (height < 900 && width < height) {
    return height * .15;
  }
  if (height < 900 && width > height) {
    return height * .13;
  }
  if (height < 1500 && width < height) {
    return height * .135;
  }
  if (height < 1500 && width > height) {
    return height * .12;
  }
  return height * .2;
}

//===================================================================//

double getTopPropertiesFraction(_context) {
  var width = MediaQuery.of(_context).size.width;
  if (width <= 360) {
    return 0.8;
  }
  if (width <= 500) {
    return 0.7;
  }
  if (width < 768) {
    return 0.5;
  }
  if (width < 900) {
    return 0.33;
  }
  if (width < 1366) {
    return 0.25;
  }
  if (width < 1500) {
    return 0.2;
  }
  if (width > 1500) {
    return 0.15;
  }
  return 0.1;
}

double getTopPropertiesHeight(_context) {
  var height = MediaQuery.of(_context).size.height;
  var width = MediaQuery.of(_context).size.width;

  if (height <= 500 && width <= 500) {
    return height * .3;
  }
  if (height <= 500 && width > 500) {
    return height * .3;
  }
  if (height < 768 && width < height) {
    return height * .262;
  }
  if (height < 768 && width > height) {
    return height * .2;
  }
  if (height < 900 && width < height) {
    return height * .22;
  }
  if (height < 900 && width > height) {
    return height * .2;
  }
  if (height < 1500 && width < height) {
    return height * .19;
  }
  if (height < 1500 && width > height) {
    return height * .19;
  }
  return height * .2;
}

//===================================================================//

double getClassifiedFraction(_context) {
  var width = MediaQuery.of(_context).size.width;
  if (width <= 500) {
    return 0.5;
  }
  if (width < 768) {
    return 0.3;
  }
  if (width < 900) {
    return 0.18;
  }
  if (width < 1500) {
    return 0.15;
  }
  return 0.1;
}

double getClassifiedHeight(_context) {
  var height = MediaQuery.of(_context).size.height;
  var width = MediaQuery.of(_context).size.width;

  if (height <= 500 && width <= 500) {
    return height * .46;
  }
  if (height <= 500 && width > 500) {
    return height * .47;
  }
  if (height < 768 && width < height) {
    return height * .32;
  }
  if (height < 768 && width > height) {
    return height * .36;
  }
  if (height < 900 && width < height) {
    return height * .3;
  }
  if (height < 900 && width > height) {
    return height * .24;
  }
  if (height < 1500 && width < height) {
    return height * .24;
  }
  if (height < 1500 && width > height) {
    return height * .22;
  }
  return height * .2;
}

//===================================================================//

double getNewsFraction(_context) {
  var width = MediaQuery.of(_context).size.width;
  if (width <= 500) {
    return 0.45;
  }
  if (width < 768) {
    return 0.2;
  }
  if (width < 900) {
    return 0.2;
  }
  if (width < 1500) {
    return 0.1;
  }
  return 0.08;
}

double getNewsHeight(_context) {
  var height = MediaQuery.of(_context).size.height;
  var width = MediaQuery.of(_context).size.width;

  if (height <= 500 && width <= 500) {
    return height * .41;
  }
  if (height <= 500 && width > 500) {
    return height * .42;
  }
  if (height < 768 && width < height) {
    return height * .25;
  }
  if (height < 768 && width > height) {
    return height * .25;
  }
  if (height < 900 && width < height) {
    return height * .2;
  }
  if (height < 900 && width > height) {
    return height * .25;
  }
  if (height < 1500 && width < height) {
    return height * .19;
  }
  if (height < 1500 && width > height) {
    return height * .17;
  }
  return height * .17;
}

//===============================================================//

double getProjectFraction(_context) {
  var width = MediaQuery.of(_context).size.width;
  if (width <= 500) {
    return 0.5;
  }
  if (width < 768) {
    return 0.3;
  }
  if (width < 900) {
    return 0.18;
  }
  if (width < 1500) {
    return 0.15;
  }
  return 0.1;
}

double getProjectHeight(_context) {
  var height = MediaQuery.of(_context).size.height;
  var width = MediaQuery.of(_context).size.width;

  if (height <= 500 && width <= 500) {
    return height * .44;
  }
  if (height <= 500 && width > 500) {
    return height * .45;
  }
  if (height < 768 && width < height) {
    return height * .23;
  }
  if (height < 768 && width > height) {
    return height * .28;
  }
  if (height < 900 && width < height) {
    return height * .18;
  }
  if (height < 900 && width > height) {
    return height * .2;
  }
  if (height < 1500 && width < height) {
    return height * .17;
  }
  if (height < 1500 && width > height) {
    return height * .17;
  }
  return height * .2;
}

//===================================================================//

double getInventoryListPageHeight(_context) {
  var height = MediaQuery.of(_context).size.height;
  var width = MediaQuery.of(_context).size.width;

  if (height <= 500 && width <= 500) {
    return height * .67;
  }
  if (height <= 500 && width > 500) {
    return height * .65;
  }
  if (height < 768 && width < height) {
    return height * .455;
  }
  if (height < 768 && width > height) {
    return height * .53;
  }
  if (height < 900 && width < height) {
    return height * .477;
  }
  if (height < 900 && width > height) {
    return height * .477;
  }
  if (height < 1500 && width < height) {
    return height * .34;
  }
  if (height < 1500 && width > height) {
    return height * .46;
  }
  return height * .2;
}

double getHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 120;
  }
  if (height < 1500 && width < height) {
    return height = 110;
  }
  return height = 140;
}
double getMarketInventoryHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 135;
  }
  return height = 140;
}

double getAgenciesHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 70;
  }
  if(height <= 900 && width <height){
    return height = 80;
  }
  return height = 40;
}
double getProjectsHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 60;
  }
  if(height <= 900 && width <height){
    return height = 75;
  }
  return height = 60;
}

double getProjectsAppBarHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  // if(height <= 720 && width <height){
  //   return height = 60;
  // }
  if (height <= 680 && width <= height) {
    return height = 70;
  }
  if(height <= 900 && width <height){
    return height = 95;
  }
  if(height <= 1500 && width <height){
    return height = 95;
  }
  return height = 60;
}

double getHotContainerHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 210;
  }
  return height = 210;
}

double getWillPopHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 325;
  }
  if (height < 1500 && width < height) {
    return height = 325;
  }
  return height = 342;
}
double getMyInventoryWillPopHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 110;
  }
  if (height < 1500 && width < height) {
    return height = 110;
  }
  return height = 110;
}


//ios willpop height

double getIOSWillPopHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if (height <= 680 && width <= height) {
    return height = 350;
  }
  if(height <= 720 && width <height){
    return height = 325;
  }
  if(height <= 1200 && width <height){
    return height = 340;
  }
  if (height < 1500 && width < height) {
    return height = 350;
  }
  return height = 340;
}

double getIosSizedBoxHeight (context){
  var height = MediaQuery.of(context).size.height;
  var width = MediaQuery.of(context).size.width;
  if(height <= 720 && width <height){
    return height = 15;
  }
  if(height <= 1200 && width <height){
    return height = 45;
  }
  if (height < 1500 && width < height) {
    return height = 15;
  }
  return height = 15;
}