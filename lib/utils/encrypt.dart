import 'package:encrypt/encrypt.dart';

class Encryption {
  static const String ENC_KEY = 'GucaqBD7MwnQSxYCKfb4EmUPjNWgR5Hs';
  static const String ENV_KEY_ENCODED = 'R3VjYXFCRDdNd25RU3hZQ0tmYjRFbVVQak5XZ1I1SHM=';
  static const String ENV_IV_ENCODED = 'HDCLMnMuhQY+7R2WJZ15Lw==';

  static String encrypt(String data) {
    var (encrypter, iv) = algo();
    final encrypted = encrypter.encrypt(data, iv: iv);
    return encrypted.base64;
  }

  static String decrypt(String data) {
    var (encrypter, iv) = algo();
    return encrypter.decrypt(Encrypted.fromBase64(data), iv: iv);
  }

  static (Encrypter, IV) algo() {
    final key = Key.fromBase64(ENV_KEY_ENCODED);
    final iv = IV.fromBase64(ENV_IV_ENCODED);

    final encrypter = Encrypter(AES(key, mode: AESMode.cbc));

    return (encrypter, iv);
  }
}
