import 'package:flutter/material.dart';

import '../data/api_base.dart';
import 'base_state.dart';

abstract class SliverBaseState<T extends StatefulWidget, U> extends BaseState<T, U> {
  @override
  Widget showHeader() {
    return CustomScrollView(
      shrinkWrap: header != null ? true : false,
      controller: scrollController,
      slivers: [
        SliverAppBar(
          floating: isFloating,
          automaticallyImplyLeading: false,
          expandedHeight: expandedHeight,
          titleSpacing: 0.0,
          backgroundColor: Colors.transparent,
          flexibleSpace: FlexibleSpaceBar(
            collapseMode: CollapseMode.parallax,
            background: Column(
              children: [
                if (withScrollAppBar) customAppBar,
                if (header != null) header!,
              ],
            ),
            titlePadding: EdgeInsets.zero,
            centerTitle: true,
          ),
        ),
        builder(),
      ],
    );
  }

  @override
  Widget builder() {
    if (loading) {
      return SliverToBoxAdapter(child: pageLoader);
    } else {
      if (response != null && response!.status!.status == ApiStatus.NOT_FOUND) {
        return SliverToBoxAdapter(child: notFound);
      } else if (response != null && response!.status!.status == ApiStatus.NO_INTERNET) {
        return SliverToBoxAdapter(child: noInternet);
      } else if (response != null && response!.status!.status != ApiStatus.OK) {
        return SliverToBoxAdapter(child: unknownError);
      } else {
        return sliverBody();
      }
    }
  }

  Widget sliverBody() {
    return SliverToBoxAdapter(
      child: body(),
    );
  }

  double get expandedHeight => 60;

  bool get withAppBar => false;

  bool get withScrollAppBar => true;

  bool get isFloating => true;
}
