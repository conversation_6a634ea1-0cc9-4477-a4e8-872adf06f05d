import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:property_dealers_directory/widgets/custom_ad_banner.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../controllers/admob_controller.dart';
import '../data/api_base.dart';
import '../nav.dart';
import '../widgets/not_available.dart';
import 'helpers.dart';
import 'widgets.dart';

abstract class BaseState<T extends StatefulWidget, U> extends State<T> with AutomaticKeepAliveClientMixin {
  bool loading = true;
  U? data;
  APIResponse<U>? response;
  final GlobalKey<ScaffoldState> scaffold = GlobalKey();
  RefreshController refreshController = RefreshController(initialRefresh: false);
  ScrollController scrollController = new ScrollController();

  int page = 1;
  bool? hasMore = false;

  AdmobController adsController = Get.find();

  bool showAd = false;

  InterstitialAd? ad;
  bool _isAdLoaded = false;

  void _createInterstitialAd() {
    if (kIsWeb) return;
    InterstitialAd.load(
        adUnitId:
            Platform.isAndroid ? 'ca-app-pub-8557684672668225/4730283009' : 'ca-app-pub-8557684672668225/3603732074',
        request: AdRequest(),
        adLoadCallback: InterstitialAdLoadCallback(
          onAdLoaded: (InterstitialAd interstitialAd) {
            ad = interstitialAd;
            _isAdLoaded = true;
            print('---------------------------- InterstitialAd loaded');
          },
          onAdFailedToLoad: (LoadAdError error) {
            ad = null;
            _isAdLoaded = false;
            print('------------------- InterstitialAd failed to load: $error.');
          },
        ));
  }

  @override
  void initState() {
    super.initState();
    getData();

    if (paginate) {
      scrollController.addListener(() {
        if (scrollController.position.pixels == scrollController.position.maxScrollExtent && hasMore!) {
          page++;
          getData();
        }
      });
    }
    if ( !kIsWeb && withScaffold && showAd && adsController.canLoad) {
     // _createInterstitialAd();
     //  ad?.show();
     //  AdBannerPopup.show(context);
    }
  }

  @override
  void dispose() {
    refreshController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  void refresh() {
    if (paginate)
      setState(() {
        page = 1;
      });
    getData();
  }

  void handleResponse(APIResponse<U> _response) {
    if (!mounted) return;
    if (_response.status!.status == ApiStatus.OK) {
      setState(() {
        if (paginate) {
          hasMore = _response.hasMore;
          if (page == 1)
            data = _response.data;
          else
            (data as List).addAll(_response.data as List);
        } else
          data = _response.data;

        response = _response;
      });
    } else {
      setState(() {
        response = _response;
      });
    }
    setState(() {
      loading = false;
    });
    refreshController.refreshCompleted();
    dataLoadedCallback();
  }

  Future<bool> onWillPop() async {
    print_("----------------------scopePOP------------");
    if (kIsWeb) return true;
    bool isLoaded = false;
    try {
      isLoaded = _isAdLoaded;
    } on PlatformException {
      isLoaded = false;
    }
    if (showAd && isLoaded) {
      ad!.show();
    }
    if(showAd && adsController.canLoad){
      AdBannerPopup.show(context);
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    if (withScaffold)
      return Scaffold(
        key: scaffold,
        appBar: withAppBar ? customAppBar as PreferredSizeWidget? : null,
        drawer: drawer,
        body: WillPopScope(
          onWillPop: onWillPop,
          child: content(),
        ),
        floatingActionButton: fab,
      );
    else
      return content();
  }

  @protected
  Widget content() {
    if (pullToRefresh)
      return Container(
        child: SmartRefresher(
          controller: refreshController,
          header: WaterDropMaterialHeader(),
          onRefresh: refresh,
          child: showHeader(),
        ),
      );
    else
      return showHeader();
  }

  Widget showHeader() {
    return header == null
        ? builder()
        : Column(
            children: <Widget>[
              header!,
              Expanded(
                child: builder(),
              )
            ],
          );
  }

  Widget builder() {
    if (loading)
      return pageLoader;
    else if (response != null && response!.status!.status == ApiStatus.NOT_FOUND)
      return notFound;
    else if (response != null && response!.status!.status == ApiStatus.NO_INTERNET)
      return noInternet;
    else if (response != null && response!.status!.status != ApiStatus.OK)
      return unknownError;
    else
      return body();
  }

  Widget body();

  String get title => '';

  String get errorMessage => 'Not Found';

  bool get withScaffold => true;

  bool get withAppBar => true;

  Widget get customAppBar => AppBar(
        title: Text(title),
        actions: actions,
      );

  bool get pullToRefresh => true;

  bool get retry => true;

  Widget? get drawer => null;

  Widget? get fab => null;

  Widget? get header => null;

  dynamic get actions => null;

  @override
  bool get wantKeepAlive => true;

  void getData();

  void dataLoadedCallback() {}

  Widget get pageLoader {
    return FullLoader();
  }

  bool get paginate => false;

  Widget paginateLoader() {
    return (!hasMore!)
        ? Divider(height: 10)
        : Padding(
            padding: const EdgeInsets.all(4.0),
            child: Center(child: Loader(size: 50, ball: 10)),
          );
  }

  Widget get notFound => SingleChildScrollView(
        child: Container(
          child: NotAvailable(
            errorMessage,
            title: 'Coming Soon',
            buttonText: 'Submit Your Agency',
            callback: (BuildContext _context) {
              Nav.submitAgency(_context);
            },
          ),
        ),
      );

  Widget get noInternet => Container(
        child: NotAvailable(
          response!.status!.message,
          buttonText: retry ? 'Retry' : null,
          callback: (_) {
            setState(() {
              data = null;
              loading = true;
            });
            getData();
          },
        ),
      );

  Widget get unknownError => Container(
        child: NotAvailable(response!.status!.message),
      );
}
