import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

import '../nav.dart';
import 'loader/fading_circle.dart';

// ignore: must_be_immutable
class TileCard extends StatelessWidget {
  final Widget child;
  final double elevation;
  final double radius;
  final Color? color;
  final Color borderColor;
  double vertical;
  double horizontal;
  Function? onTap;
  bool halfRadius;

  TileCard({
    required this.child,
    this.elevation = 6.0,
    this.radius = 10.0,
    this.color,
    this.vertical = 5.0,
    this.horizontal = 15.0,
    this.borderColor = Colors.white,
    this.onTap,
    this.halfRadius = false,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: elevation,
      color: color,
      shadowColor: Color(0x582196F3),
      shape: RoundedRectangleBorder(
          borderRadius: halfRadius
              ? BorderRadius.only(topLeft: Radius.circular(radius), bottomRight: Radius.circular(radius))
              : BorderRadius.circular(radius),
          side: BorderSide(color: borderColor, width: 1.5)),
      child: ClipRRect(
        borderRadius: halfRadius
            ? BorderRadius.only(topLeft: Radius.circular(radius), bottomRight: Radius.circular(radius))
            : BorderRadius.circular(radius),
        child: InkWell(
          onTap: onTap as void Function()?,
          child: Container(
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal),
              child: child,
            ),
          ),
        ),
      ),
    );
  }
}

InputDecoration inputStyle(label, {hint = '', radius = 25.0}) {
  return InputDecoration(
    border: OutlineInputBorder(borderRadius: BorderRadius.circular(radius)),
    labelText: label,
    hintText: hint,
  );
}

class PlaceHolderImage extends StatelessWidget {
  final double height;
  final double width;

  PlaceHolderImage([this.height = 60.0, this.width = 60.0]);

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      'images/placeholder.png',
      height: height,
      width: width,
      fit: BoxFit.cover,
    );
  }
}

class NetworkImageWithPlaceholder extends StatelessWidget {
  final String url;
  final double height;
  final double width;
  final double radius;
  final bool circular;
  final bool cover;
  final String? text;

  NetworkImageWithPlaceholder(this.url,
      {this.height = 100.0,
      this.width = 100.0,
      this.circular = false,
      this.radius = 0.0,
      this.cover = true,
      this.text = ''});

  @override
  Widget build(BuildContext context) {
    double _radius = radius != 0.0 ? radius : (circular ? height / 2 : 0);
    return ClipRRect(
      borderRadius: BorderRadius.circular(_radius),
      child: url.isEmpty
          ? (placeholder)
          : CachedNetworkImage(
              height: height,
              width: width,
              fit: this.cover ? BoxFit.cover : BoxFit.contain,
              placeholder: (context, url) => Container(width: width, height: height, child: Loader()),
              imageUrl: url,
              errorWidget: (context, url, error) => placeholder,
            ),
    );
  }

  Widget get placeholder {
    return text!.isEmpty ? PlaceHolderImage(height, width) : TextPlaceHolder(height: height, width: width, text: text!);
  }
}

class TextPlaceHolder extends StatelessWidget {
  final double height;
  final double width;
  final String text;

  const TextPlaceHolder({required this.height, required this.width, required this.text});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height,
      width: width,
      color: Theme.of(context).primaryColor,
      child: Center(
        child: Text(
          text.substring(0, 1).toUpperCase(),
          style: TextStyle(fontSize: 26.0, color: Colors.white, fontWeight: FontWeight.w600),
        ),
      ),
    );
  }
}

class Loader extends StatelessWidget {
  final double size;
  final double ball;

  Loader({this.size = 100.0, this.ball = 15.0});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: size,
      width: size,
      child: SpinKitFadingCircle(
        color: Theme.of(context).primaryColor,
        size: size / 2,
      ),
    );
  }
}

class FullLoader extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      child: Center(
        child: Loader(),
      ),
    );
  }
}

class TabTitle extends StatelessWidget {
  final String title;

  TabTitle(this.title);

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      constraints: BoxConstraints.expand(width: 100),
      child: Text(title),
    );
  }
}

class PrimaryButton extends StatelessWidget {
  final String text;
  final Function onPressed;

  const PrimaryButton({Key? key, required this.text, required this.onPressed}) : super(key: key);

  Widget build(BuildContext context) {
    return ElevatedButton(
      style: ButtonStyle(
        shape: MaterialStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
            side: BorderSide(color: Theme.of(context).primaryColor),
          ),
        ),
        backgroundColor: MaterialStateProperty.all(Theme.of(context).primaryColor),
        textStyle: MaterialStateProperty.all(TextStyle(color: Colors.white)),
      ),
      onPressed: onPressed as void Function()?,
      child: Text(text),
    );
  }
}

class CancelButton extends StatelessWidget {
  const CancelButton({Key? key}) : super(key: key);

  Widget build(BuildContext context) {
    return ElevatedButton(
      child: Text("Cancel", style: TextStyle(color: Colors.red)),
      style: ButtonStyle(
          elevation: MaterialStateProperty.all(0),
          backgroundColor: MaterialStateProperty.all(Colors.white),
          side: MaterialStateProperty.all(BorderSide(color: Colors.red, width: 1))),
      onPressed: () => Nav.back(context),
    );
  }
}
