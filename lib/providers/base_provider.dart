import 'package:flutter/material.dart';
import 'package:flutter_progress_hud/flutter_progress_hud.dart';

class BaseProvider extends ChangeNotifier {
  Function(String)? onError;
  Function(bool)? onLoading;

  BuildContext? context;

  void setContext(BuildContext context) {
    this.context = context;
  }

  void setErrorHandler(Function(String) onError) {
    this.onError = onError;
  }

  void setLoadingHandler(Function(bool) onLoading) {
    this.onLoading = onLoading;
  }

  void showError(String error) {
    if (onError != null) {
      onError!(error);
      return;
    }
    if (context != null) {
      ScaffoldMessenger.of(context!)
          .showSnackBar(SnackBar(content: Text(error)));
    }
  }

  void toggleLoading(bool loading) {
    if (onLoading != null) {
      onLoading!(loading);
      return;
    }
    if (context != null) {
      if(loading) {
        ProgressHUD.of(context!)!.show();
      } else {
        ProgressHUD.of(context!)!.dismiss();
      }
    }
  }

  void showLoading() {
    toggleLoading(true);
  }

  void hideLoading() {
    toggleLoading(false);
  }
}
