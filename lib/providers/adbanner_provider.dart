import 'package:flutter/material.dart';
import 'package:property_dealers_directory/data/api.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/utils/helpers.dart';

late AdBannerProvider adProvider;

class AdBannerProvider extends ChangeNotifier {
  List<AdBannerEntity> allBanners = [];
  Map<String, int> bannerIndexes = {};

  bool hasBanner(AdBannerPosition position) {
    return allBanners.any((element) => element.positionEnum == position);
  }

  AdBannerEntity getNextBanner(AdBannerPosition position) {
    List<AdBannerEntity> banners = allBanners
        .where((element) => element.positionEnum == position)
        .toList();
    if (banners.isEmpty) return AdBannerEntity();
    int index = bannerIndexes[position.name] ?? 0;
    bannerIndexes[position.name] = (index + 1) % banners.length;
    return banners[index];
  }

  click(AdBannerEntity ad) {
    Map<String, dynamic> body = {};
    body['id'] = ad.id;
    body['action'] = "Click";
    body['model'] = "ad_banner";
    API.postAnalytics(body);
    if(ad.url.isNotEmpty) {
      openLink(ad.url);
    }
  }
  view(AdBannerEntity ad) {
    Map<String, dynamic> body = {};
    body['id'] = ad.id;
    body['action'] = "View";
    body['model'] = "ad_banner";
    API.postAnalytics(body);
  }

  AdBannerProvider() {
    API.getAdBanners().then((value) {
      if (!value.isOK) return;
      allBanners = value.data!;
      allBanners.forEach((element) {
        bannerIndexes[element.position] = 0;
      });
      notifyListeners();
    });
  }
}
