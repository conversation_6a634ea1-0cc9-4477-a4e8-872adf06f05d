import 'package:property_dealers_directory/data/api.dart';
import 'package:property_dealers_directory/models/block_entity.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import 'package:property_dealers_directory/models/plot_entity.dart';

import 'base_provider.dart';

class LocationSelectionProvider extends BaseProvider {
  List<CityEntity> cities = [];

  List<CityEntity> get societies => selectedCity.societies;
  CityEntity selectedCity = CityEntity();
  CityEntity selectedSociety = CityEntity();
  BlockEntity selectedBlock = BlockEntity();
  PlotEntity selectedPlot = PlotEntity();
  List<BlockEntity> blocks = [];
  List<PlotEntity> plots = [];

  bool plotsNeeded = false;
  int plotId = 0;

  LocationSelectionProvider({this.plotsNeeded = false}) : super() {
    getCities();
  }

  void setCity(CityEntity? city) {
    if (city == null) city = CityEntity();
    selectedCity = city;
    notifyListeners();
  }

  void setSociety(CityEntity? society) {
    if (society == null) society = CityEntity();
    selectedSociety = society;
    refreshBlocks();
    notifyListeners();
  }

  void setBlock(BlockEntity? block) {
    if (block == null) block = BlockEntity();
    selectedBlock = block;
    refreshPlots();
    notifyListeners();
  }

  void setPlotId(int id) {
    plotId = id;
    notifyListeners();
  }

  void getCities() {
    API.getCitiesList().then((value) {
      if (value.isOK) {
        cities = value.data!;
        notifyListeners();
      }
    });
  }

  void refreshBlocks() {
    blocks = [];
    notifyListeners();
    if(selectedSociety.isEmpty) return;
    showLoading();
    API
        .blocksList(
      selectedSociety.id,
      cityId: 0,
      category: 'Residential',
      inActive: "&inactive=1",
    )
        .then(
      (value) {
        hideLoading();
        if (value.isOK) {
          blocks = value.data!;
          notifyListeners();
        } else {
          showError('No active blocks for this society');
        }
      },
    );
  }

  void refreshPlots() {
    if (!plotsNeeded) return;
    plots = [];
    notifyListeners();
    if(selectedBlock.isEmpty) return;
    showLoading();
    API
        .getPlotList(
      selectedBlock.id,
    )
        .then(
      (value) {
        hideLoading();
        if (value.isOK) {
          plots = value.data!;
          if(plotId != 0) {
            selectedPlot = plots.firstWhere((element) => element.id == plotId, orElse: () => PlotEntity());
          }
          notifyListeners();
        } else {
          showError('No active plots for this block');
        }
      },
    );
  }
}
