import 'dart:async';
import 'dart:convert';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:property_dealers_directory/data/api.dart';
import 'package:property_dealers_directory/models/city_entity.dart';
import 'package:property_dealers_directory/models/onboarding_entity.dart';

import '../my_app.dart';
late OnboardingProvider obProvider;
class OnboardingProvider extends ChangeNotifier {
  GetStorage storage = GetStorage();
  Timer? _debounce;
  OnboardingEntity onboarding = OnboardingEntity();
  List<CityEntity> cities = [];

  List<int> get notifySocieties => onboarding.notificationSocieties.isNotEmpty
      ? onboarding.notificationSocieties.split(',').map(int.parse).toList()
      : [];

  bool get isTutorialDone => onboarding.tutorialDone;

  bool get isSocietySelected => !onboarding.society.isEmpty;
  bool get isIndividual => userType == UserTypes.individual;
  bool get isAgent => userType == UserTypes.agent;

  CityEntity get city => onboarding.city;
  CityEntity get society => onboarding.society;

  UserTypes get userType => UserTypes.values.firstWhere(
      (element) => element.name == onboarding.userType,
      orElse: () => UserTypes.agent);

  OnboardingProvider() {
    read();
  }

  read() {
    String data = storage.read('ONBOARDING') ?? '';
    if (data.isEmpty) return;
    this.onboarding = OnboardingEntity.fromJson(jsonDecode(data));
    String citiesData = storage.read('CITIES_ALL') ?? '';
    if (citiesData.isNotEmpty) {
      this.cities = (jsonDecode(citiesData) as List)
          .map((e) => CityEntity.fromJson(e))
          .toList();
    }
  }

  void selectCity(CityEntity city) {
    onboarding.city = city;
    write();
    FirebaseAnalytics.instance
        .setUserProperty(name: 'Selected_City', value: city.name);
  }

  void selectSociety(CityEntity society) {
    onboarding.society = society;
    write();
    FirebaseAnalytics.instance
        .setUserProperty(name: 'Selected_Society', value: society.name);
  }

  void selectUserType(UserTypes userType) {
    onboarding.userType = userType.name;
    write();
    FirebaseAnalytics.instance
        .setUserProperty(name: 'User_Type', value: userType.name);
  }

  void setSocietyNotify(bool notify, int societyId) {
    var list = notifySocieties;
    if (notify == true && list.contains(societyId)) return;
    if (notify) {
      list.add(societyId);
      toggleTopic('society_${societyId}', true);
    } else {
      list.remove(societyId);
      toggleTopic('society_${societyId}', false);
    }
    onboarding.notificationSocieties = list.isEmpty ? '' : list.join(',');
    write();
  }

  void setHot(bool hot) {
    if(kIsWeb)
      return;
    if (hot == onboarding.notificationHot) return;
    if (onboarding.notificationHot) {
      toggleTopic('property_urgent', false);
    }
    if (hot) {
      toggleTopic('property_urgent', true);
    }
    onboarding.notificationHot = hot;
    write();
  }

  void toggleTopic(String name, bool subscribe) {
    if(kIsWeb)
      return;
    if (subscribe) {
      MyApp.fcm.subscribeToTopic(name);
    } else {
      MyApp.fcm.unsubscribeFromTopic(name);
    }
  }

  refreshCity() {
    API.getCitiesList().then((value) {
      if (value.isOK) {
        onboarding.city = value.data!.firstWhere(
            (element) => element.id == onboarding.city.id,
            orElse: () => value.data!.first);
        cities = value.data!;
        write();
      }
    });
  }

  write() {
    if (_debounce?.isActive ?? false) _debounce?.cancel();
    _debounce = Timer(const Duration(milliseconds: 200), () {
      storage.write('ONBOARDING', jsonEncode(onboarding));
      storage.write('CITIES_ALL', jsonEncode(cities));
      notifyListeners();
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }
}

enum UserTypes {
  agent,
  individual,
  resident,
  delivery,
  none;

  String get title {
    switch (this) {
      case UserTypes.agent:
        return 'Dealer';
      case UserTypes.individual:
        return 'Buyer/Seller';
      case UserTypes.resident:
        return 'Resident';
      case UserTypes.delivery:
        return 'Delivery';
      default:
        return '';
    }
  }
}
