import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:unique_identifier/unique_identifier.dart';

import '../utils/encrypt.dart';

class ReadDeviceInfo {
  static PackageInfo? packageInfo;
  static final DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();

  static Map<String, dynamic> deviceMap = {};
  static String? id;
  static String? name;
  static String? model;

  static Map<String, dynamic> readAppInfo() {
    if (packageInfo == null) {
      PackageInfo.fromPlatform().then((value) => packageInfo = value);
      return {};
    }
    var pkg = packageInfo!;
    return {
      'App-Name': pkg.appName,
      'Package-Name': pkg.packageName,
      'Version': pkg.version,
      'Build-Number': pkg.buildNumber,
    };
  }

  static Map<String, dynamic> readDeviceInfo() {
    if (isEmpty()) {
      if (kIsWeb) {
        deviceInfoPlugin.webBrowserInfo.then((value) {
          name = 'Web';
          id = value.userAgent;
          model = value.browserName.name;
        });
      } else {
        switch (defaultTargetPlatform) {
          case TargetPlatform.android:
            UniqueIdentifier.serial.then((value) => id = value ?? '');
            deviceInfoPlugin.androidInfo.then((value) {
              model = value.model;
              name = value.brand;
            });
            break;
          case TargetPlatform.iOS:
            deviceInfoPlugin.iosInfo.then((value) {
              id = value.identifierForVendor ?? '';
              model = value.model;
              name = value.name;
            });
            break;
          default:
            id = 'Unknown';
            model = 'Unknown';
            name = 'Unknown';
        }
      }
    }
    if (name != null) deviceMap['Device-Name'] = name;
    if (model != null) deviceMap['Device-Model'] = model;
    if (id != null)
      deviceMap['device'] = Encryption.encrypt("$name^$model^$id");

    return deviceMap;
  }

  static bool isEmpty() {
    return id == null;
  }

  static String getDeviceId(){
    return deviceMap['device'] ?? '';
  }
}
