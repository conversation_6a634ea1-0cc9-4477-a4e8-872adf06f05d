import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:property_dealers_directory/controllers/login_controller.dart';

class APIClient extends http.BaseClient {
  final http.Client client = http.Client();
  final LoginController controller = Get.find();

  @override
  Future<http.StreamedResponse> send(http.BaseRequest request) {
    if (controller.token != '') {
      request.headers['Authorization'] = 'Bearer ${controller.token}';
    }
    request.headers['Accept'] = 'application/json';
    return client.send(request);
  }
}
