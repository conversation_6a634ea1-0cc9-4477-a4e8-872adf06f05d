import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/models/society_file_entity.dart';
import 'package:property_dealers_directory/models/society_file_entity.dart';
import 'package:property_dealers_directory/models/society_file_entity.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';

import '../controllers/login_controller.dart';
import '../models/agency_entity.dart';
import '../models/agency_short_entity.dart';
import '../models/block_entity.dart';
import '../models/blog_entity.dart';
import '../models/city_entity.dart';
import '../models/classified_entity.dart';
import '../models/classified_short_entity.dart';
import '../models/explore_data_entity.dart';
import '../models/explore_entity.dart';
import '../models/explore_short_entity.dart';
import '../models/home_entity.dart';
import '../models/inventory_design_entity.dart';
import '../models/login_entity.dart';
import '../models/market_inventory_long_entity.dart';
import '../models/market_inventory_short_entity.dart';
import '../models/plot_entity.dart';
import '../models/project_Short_entity.dart';
import '../models/project_entity.dart';
import '../models/property_entity.dart';
import '../models/rates_entity.dart';
import '../models/society_entity.dart';
import '../models/user_plan_entity.dart';
import '../models/video_entity.dart';
import 'api_base.dart';

export 'api_base.dart';

class API extends BaseApi {
  ///added in postman
  static Future<APIResponse<List<CityEntity>>> getCitiesList({page = 1}) async {
    APIResponse<dynamic> _response = await BaseApi.get('cities/all', cache: 7);
    APIResponse<List<CityEntity>> response = copy<List<CityEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(CityEntity.fromJson(element));
      });
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<List<CityEntity>>> getCitiesListWithBlocks({page = 1}) async {
    APIResponse<dynamic> _response = await BaseApi.get('v3/cities/all', cache: 0);
    APIResponse<List<CityEntity>> response = copy<List<CityEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(CityEntity.fromJson(element));
      });
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<List<SocietyEntity>>> getSocietiesListWithCity() async {
    APIResponse<dynamic> _response = await BaseApi.get('societies/all');
    APIResponse<List<SocietyEntity>> response = copy<List<SocietyEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        if (element['city_id'] is String) element['city_id'] = int.tryParse(element['city_id']);
        response.data!.add(SocietyEntity.fromJson(element));
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<SocietyEntity>>> getSocietiesListWithCityCached() async {
    APIResponse<dynamic> _response = await BaseApi.get('societies/all', cache: 7);
    APIResponse<List<SocietyEntity>> response = copy<List<SocietyEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        if (element['city_id'] is String) element['city_id'] = int.tryParse(element['city_id']);
        response.data!.add(SocietyEntity.fromJson(element));
      });
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<List<CityEntity>>> getSocietiesList({page = 1, cityId, all = 0}) async {
    String extra = '?page=$page&all=$all';
    extra += cityId != null ? '&city_id=$cityId' : '';
    APIResponse<dynamic> _response = await BaseApi.get('societies$extra', cache: 7);
    APIResponse<List<CityEntity>> response = copy<List<CityEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(CityEntity.fromJson(element));
      });
    }
    return response;
  }
 /// added in postman
  static Future<APIResponse<List<AgencyShortEntity>>> getAgencyList({page = 1, societyId, isFeatured, query}) async {
    String extra = '?page=$page';
    extra += societyId != null ? '&society_id=$societyId' : '';
    extra += isFeatured != null ? '&is_featured=1' : '';
    extra += query != null ? '&query=$query' : '';
    var _response = await BaseApi.get('agencies$extra');
    APIResponse<List<AgencyShortEntity>> response = copy<List<AgencyShortEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(AgencyShortEntity.fromJson(element));
      });
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<AgencyEntity>> getAgency(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('agency/$id');
    APIResponse<AgencyEntity> response = copy<AgencyEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = AgencyEntity.fromJson(_response.data);
    }
    return response;
  }

  static Future<APIResponse<dynamic>> submitAgency(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('v3/agency/create-request', body);
    return _response;
  }
 /// added in postman
  static Future<APIResponse<HomeEntity>> getHome(int? cityId) async {
    var _response = await BaseApi.get<Map<String, dynamic>>('home/v2?city_id=' + (cityId ?? '').toString());
    APIResponse<HomeEntity> response = copy<HomeEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = HomeEntity.fromJson(_response.data!);
    }
    return response;
  }
 /// added in postman
  static Future<APIResponse<List<PropertyEntity>>> getProperties(
      {int? id = 0, int page = 1, int? agencyId, Map extra = const {}}) async {
    String extras = '';

    extra.forEach((key, value) {
      extras += value != null ? '&$key=$value' : '';
    });

    extras += id != 0 ? '&id=$id' : '';
    extras += agencyId != 0 ? '&agency_id=$agencyId' : '';

    APIResponse<dynamic> _response = await BaseApi.get('properties?page=$page' + extras);
    APIResponse<List<PropertyEntity>> response = copy<List<PropertyEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(PropertyEntity.fromJson(element));
      });
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<PropertyEntity>> getPropertyDetail(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('properties/$id');
    APIResponse<PropertyEntity> response = copy<PropertyEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = PropertyEntity.fromJson(_response.data);
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<List<PropertyEntity>>> getHotProperties() async {
    APIResponse<dynamic> _response = await BaseApi.get('properties/hot');
    APIResponse<List<PropertyEntity>> response = copy<List<PropertyEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(PropertyEntity.fromJson(element));
      });
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<LoginEntity>> login(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('auth/login', body);
    APIResponse<LoginEntity> response = copy<LoginEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = LoginEntity.fromJson(_response.data);
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<UserPlanEntity>> getUserPlan() async {
    APIResponse<dynamic> _response = await BaseApi.get('user');
    APIResponse<UserPlanEntity> response = copy<UserPlanEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = UserPlanEntity.fromJson(_response.data['plan']);
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<List<InventoryDesignEntity>>> getInventoryDesign() async {
    APIResponse<dynamic> _response = await BaseApi.get('designs');
    APIResponse<List<InventoryDesignEntity>> response = copy<List<InventoryDesignEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(InventoryDesignEntity.fromJson(element));
      });
    }
    return response;
  }
  ///added in postman
  static Future<APIResponse<dynamic>> resetPassword(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('password/reset', body);
    return _response;
  }
  // don't forget to discus
  /// added in postman
  static Future<APIResponse<dynamic>> addProperty(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('v2/properties', body);
    return _response;
  }
  /// test
  static Future<APIResponse<dynamic>> updateProperty(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('properties/$id/update', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> deleteProperty(id, int status) async {
    APIResponse<dynamic> _response =
        await BaseApi.post('v2/properties/delete', {'property_id': id.toString(), 'sold_from_app': status});
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> activateProperty(id) async {
    APIResponse<dynamic> _response = await BaseApi.post('v2/properties/un-sold', {'property_id': id.toString()});
    return _response;
  }
 /// test
  static Future<APIResponse<String>> reportProperty(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('properties/$id/report', body);
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
 /// test
  static Future<APIResponse<String>> clickCounterOnProperty(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('properties/$id/click', body);
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
  /// added in postman
  static Future<APIResponse<dynamic>> addMarketInventory(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('market-inventory', body);
    return _response;
  }
 /// test
  static Future<APIResponse<String>> postAnalytics(body) async {

    if(kIsWeb){
      body["platform"] = "Web App";
    } else {
      if (Platform.isAndroid) body["platform"] = "Android";
      if (Platform.isIOS) body["platform"] = "Ios";
    }

    LoginController login = Get.find();

    if (login.isLoggedIn) {
      body["user_id"] = login.user?.staff!.id;
      body["agency_name"] = login.user?.agency!.name;
    }
    body["selected_city"] = obProvider.city.name;
    body["selected_type"] = obProvider.userType.name;
    body["firebase_token"] = login.fcm;

    APIResponse<dynamic> _response = await BaseApi.post('analytics', body);
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
 /// added in postman
  static Future<APIResponse<List<PropertyEntity>>> getFavrouitePropertyList(idList, page) async {
    APIResponse<dynamic> _response = await BaseApi.get('properties/list?ids=$idList&page=$page');
    APIResponse<List<PropertyEntity>> response = copy<List<PropertyEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(PropertyEntity.fromJson(element));
      });
    }
    return response;
  }
 /// test
  static Future<APIResponse<String>> viewCounterOnProject(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('projects/$id/view', body);
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
 /// test
  static Future<APIResponse<String>> clickCounterOnProject(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('projects/$id/click', body);
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
 /// test
  static Future<APIResponse<String>> clickCounterOnClassified(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/$id/click', body);
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
 /// test
  static Future<APIResponse<dynamic>> makeItHotApi(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('properties/hot-request?property_id=$id', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> makeInventoryRefresh(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('v2/properties/$id/refresh', body);
    return _response;
  }
 /// test
  static Future<APIResponse<String>> updateToken(token) async {
    APIResponse<dynamic> _response = await BaseApi.post('link-token', {'token': token});
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<BlockEntity>>> blocksList(int? id,
      {int? cityId, String? category, String inActive = ""}) async {
    category ??= '';
    cityId ??= 0;
    APIResponse<dynamic> _response =
        await BaseApi.get('blocks?city_id=$cityId&society_id=$id&category=$category$inActive');
    APIResponse<List<BlockEntity>> response = copy<List<BlockEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(BlockEntity.fromJson(element));
      });
    }
    return response;
  }
 /// added in postman
  static Future<APIResponse<BlockEntity>> defaultBlock(int id) async {
    APIResponse<dynamic> _response = await BaseApi.get('v3/cities/default-block?city_id=$id');
    APIResponse<BlockEntity> response = copy<BlockEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = BlockEntity.fromJson(_response.data);
    }
    return response;
  }

   static Future<APIResponse<List<ClassifiedMarker>>> blockClassifieds(int id) async {
    APIResponse<dynamic> _response = await BaseApi.get('plots/block-classifieds/$id');
    APIResponse<List<ClassifiedMarker>> response = copy<List<ClassifiedMarker>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(ClassifiedMarker.fromJson(element));
      });
    }
    return response;
  }
   static Future<APIResponse<List<ClassifiedMarker>>> societyClassifieds(int id) async {
    APIResponse<dynamic> _response = await BaseApi.get('plots/society-classifieds/$id');
    APIResponse<List<ClassifiedMarker>> response = copy<List<ClassifiedMarker>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(ClassifiedMarker.fromJson(element));
      });
    }
    return response;
  }


  /// added in postman
  static Future<APIResponse<List<String>>> getInventoryCategories() async {
    var _response = await BaseApi.get('categories');
    APIResponse<List<String>> response = copy<List<String>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(element);
      });
    }
    return response;
  }
  static Future<APIResponse<List<String>>> getFeaturesList() async {
    var _response = await BaseApi.get('classified-features');
    APIResponse<List<String>> response = copy<List<String>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(element);
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<PlotEntity>> getPlotDetail(blockId, plotId) async {
    APIResponse<dynamic> _response = await BaseApi.get('v4/plots?block=$blockId&plot=$plotId&classifieds=0');
    APIResponse<PlotEntity> response = copy<PlotEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = PlotEntity.fromJson(_response.data);
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<ProjectShortEntity>>> getProjectList({page = 1, isFeatured, query, Map searchQuery = const {}}) async {
    String extra = '?page=$page';
    extra += isFeatured != null ? '&featured=1' : '';
    extra += query != null ? '&search=$query' : '';
    searchQuery.forEach((key, value) {
      extra += value != null ? '&$key=$value' : '';
    });

    var _response = await BaseApi.get('projects$extra');
    APIResponse<List<ProjectShortEntity>> response = copy<List<ProjectShortEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(ProjectShortEntity.fromJson(element));
      });
    }
    return response;
  }
 /// added in postman
  static Future<APIResponse<ProjectEntity>> getProjectDetail(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('projects/$id');
    APIResponse<ProjectEntity> response = copy<ProjectEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = ProjectEntity.fromJson(_response.data);
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<RateEntity>>> getRateList(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('rates?society_id=$id');
    APIResponse<List<RateEntity>> response = copy<List<RateEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(RateEntity.fromJson(element));
      });
    }
    return response;
  }
  static Future<APIResponse<List<ClassifiedMedia>>> getSocietyFiles(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('society-files/$id');
    APIResponse<List<ClassifiedMedia>> response = copy<List<ClassifiedMedia>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(ClassifiedMedia.fromJson(element));
      });
    }
    return response;
  }
 /// test
  static Future<APIResponse<dynamic>> addClassifiedProperty(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/create', body);
    return _response;
  }
  static Future<APIResponse<List<String>>> aiDescriptionClassified(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/gen-ai-description', body);
    APIResponse<List<String>> response = copy<List<String>>(_response);

    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(element);
      });
    }
    return response;
  }
 /// test
  static Future<APIResponse<dynamic>> updateClassifiedProperty(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/$id', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> makeClassifiedHot(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/$id/hot', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> makeClassifiedRefresh(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/$id/refresh', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> changeClassifiedStatus(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/$id/status', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> deleteClassified(id, body) async {
    APIResponse<dynamic> _response = await BaseApi.post('classified-properties/$id/delete', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> changeStaffProfilePic(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('user/profile-photo', body);
    return _response;
  }
 /// test
  static Future<APIResponse<dynamic>> changePassword(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('change-password', body);
    return _response;
  }
  /// added in postman
  static Future<APIResponse<List<ClassifiedShortEntity>>> getClassifiedPropertyList(
      {page = 1, staffId, agencyId, isFeatured, Map query = const {}}) async {
    String extra = '?page=$page';
    extra += staffId != null ? '&staff_id=$staffId' : '';
    extra += agencyId != null ? '&agency_id=$agencyId' : '';
    extra += isFeatured != null ? '&featured=$isFeatured' : '';
    // extra += query != null ? '$query' : '';
    query.forEach((key, value) {
      extra += value != null ? '&$key=$value' : '';
    });
    var _response = await BaseApi.get('classified-properties$extra');
    APIResponse<List<ClassifiedShortEntity>> response = copy<List<ClassifiedShortEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(ClassifiedShortEntity.fromJson(element));
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<ClassifiedEntity>> getClassifiedPropertyDetail(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('classified-properties/$id');
    APIResponse<ClassifiedEntity> response = copy<ClassifiedEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = ClassifiedEntity.fromJson(_response.data);
    }
    return response;
  }
 /// added in postman
  static Future<APIResponse<List<PlotEntity>>> getPlotList(blockId) async {
    APIResponse<dynamic> _response = await BaseApi.get('v2/plots?block=$blockId');
    APIResponse<List<PlotEntity>> response = copy<List<PlotEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(PlotEntity.fromJson(element));
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<VideoEntity>>> getVideos(query) async {
    String extra = query != null ? '?query=$query' : '';
    APIResponse<dynamic> _response = await BaseApi.get('videos$extra');
    APIResponse<List<VideoEntity>> response = copy<List<VideoEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(VideoEntity.fromJson(element));
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<BlogEntity>>> getNewsList({page = 1}) async {
    APIResponse<dynamic> _response = await BaseApi.get('blogs?page=$page');
    APIResponse<List<BlogEntity>> response = copy<List<BlogEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(BlogEntity.fromJson(element));
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<BlogEntity>> getNewsDetail(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('blogs/$id');
    APIResponse<BlogEntity> response = copy<BlogEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = BlogEntity.fromJson(_response.data);
    }
    return response;
  }
 /// test
  static feedBack(int plotId, body) async {
    APIResponse<dynamic> response = await BaseApi.post('v2/plots/$plotId/feedback', body);
    return response;
  }
 /// added in postman
  static Future<APIResponse<List<ExploreShortEntity>>> getExploreList() async {
    APIResponse<dynamic> _response = await BaseApi.get('explore');
    APIResponse<List<ExploreShortEntity>> response = copy<List<ExploreShortEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(ExploreShortEntity.fromJson(element));
      });
    }
    return response;
  }
 /// added in postman
  static Future<APIResponse<ExploreEntity>> getExploreDetail(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('explore/$id');
    APIResponse<ExploreEntity> response = copy<ExploreEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = ExploreEntity.fromJson(_response.data);
    }
    return response;
  }
 /// added in postman
  static Future<APIResponse<ExploreDataEntity>> getAllExploreData() async {
    APIResponse<dynamic> _response = await BaseApi.get('explore-data/all');
    APIResponse<ExploreDataEntity> response = copy<ExploreDataEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = ExploreDataEntity.fromJson(_response.data);
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<ExploreShortCategory>>> getExploreData(type) async {
    APIResponse<dynamic> _response = await BaseApi.get('explore-data/$type');
    APIResponse<List<ExploreShortCategory>> response = copy<List<ExploreShortCategory>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(ExploreShortCategory.fromJson(element));
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<List<MarketInventoryShortEntity>>> getMarketInventoryList({
    page = 1, staffId,agencyId, Map extra = const {}}) async {
    String extras = '?page=$page';
    extras += staffId != null ? '&staff_id=$staffId' : '';
    extras += agencyId != null ? '&agency_id=$agencyId' : '';

    extra.forEach((key, value) {
      extras += value != null ? '&$key=$value' : '';
    });

    APIResponse<dynamic> _response = await BaseApi.get('market-inventory' + extras);
    APIResponse<List<MarketInventoryShortEntity>> response = copy<List<MarketInventoryShortEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(MarketInventoryShortEntity.fromJson(element));
      });
    }
    return response;
  }
  /// added in postman
  static Future<APIResponse<MarketInventoryLongEntity>> getMarketInventoryDetail(id) async {
    APIResponse<dynamic> _response = await BaseApi.get('market-inventory/$id');
    APIResponse<MarketInventoryLongEntity> response = copy<MarketInventoryLongEntity>(_response);
    if (_response.status!.status == ApiStatus.OK) {
      response.data = MarketInventoryLongEntity.fromJson(_response.data);
    }
    return response;
  }

  static Future<APIResponse<List<AdBannerEntity>>> getAdBanners() async {
    APIResponse<dynamic> _response = await BaseApi.get('adbanners');
    APIResponse<List<AdBannerEntity>> response = copy<List<AdBannerEntity>>(_response);
    response.data = [];
    if (_response.status!.status == ApiStatus.OK) {
      (_response.data as List).forEach((element) {
        response.data!.add(AdBannerEntity.fromJson(element));
      });
    };
    return response;
  }
  static Future<APIResponse<dynamic>> sendUserInvite(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('invite-user', body);
    return _response;
  }
  static Future<APIResponse<dynamic>> sendMapAccessRequest(body) async {
    APIResponse<dynamic> _response = await BaseApi.post('v4/map-access', body);
    return _response;
  }


 /// test
  static Future<APIResponse<dynamic>> feedbackFormSubmit(body) async {
    LoginController login = Get.find();
    //
    Map<String, dynamic> _body = {};
    _body.addAll(body);
    _body["selected_city"] = obProvider.city.name;
    _body["fcm_token"] = login.fcm;
    _body["selected_type"] = obProvider.userType.name;

    APIResponse<dynamic> _response = await BaseApi.post('app-feedback', _body);
    APIResponse<String> response = copy<String>(_response);
    return response;
  }
  static copy<T>(APIResponse<dynamic> input) {
    APIResponse<T> response = APIResponse();
    response.status = input.status;
    response.isList = input.isList;
    response.hasMore = input.hasMore;
    response.rawResponse = input.rawResponse;
    return response;
  }
}
