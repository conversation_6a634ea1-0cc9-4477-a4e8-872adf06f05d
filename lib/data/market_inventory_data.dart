class TableData{
 final String staff_name;
 final String plot_name;
 final String society_short;
 final String block_short;
 final String price;
 final String view;
 final String call;

 TableData({required this.staff_name, required this.plot_name, required this.society_short, required this.block_short, required this.price, required this.call, required this.view});

}

List<TableData> tableData = [
  TableData(
    staff_name: "Hello",
    plot_name:  "abc",
    society_short: "BTK",
    block_short: "BTK",
    price:  "1 lakh",
    call: "Call",
    view: "View"
  ),
  TableData(
      staff_name: "Hello",
      plot_name:  "abc",
      society_short: "BTK",
      block_short: "BTK",
      price:  "1 lakh",
      call: "Call",
      view: "View"
  ),
  TableData(
      staff_name: "Hello",
      plot_name:  "abc",
      society_short: "BTK",
      block_short: "BTK",
      price:  "1 lakh",
      call: "Call",
      view: "View"
  ),
  TableData(
      staff_name: "Hello",
      plot_name:  "abc",
      society_short: "<PERSON><PERSON>",
      block_short: "BT<PERSON>",
      price:  "1 lakh",
      call: "Call",
      view: "View"
  ),
  TableData(
      staff_name: "Hello",
      plot_name:  "abc",
      society_short: "BT<PERSON>",
      block_short: "BTK",
      price:  "1 lakh",
      call: "Call",
      view: "View"
  ),
  TableData(
      staff_name: "Hello",
      plot_name:  "abc",
      society_short: "BTK",
      block_short: "BTK",
      price:  "1 lakh",
      call: "Call",
      view: "View"
  ),
];
