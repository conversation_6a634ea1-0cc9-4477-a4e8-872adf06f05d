import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' hide Response;
import 'package:package_info_plus/package_info_plus.dart';
import 'package:property_dealers_directory/data/read_device_info.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/services/remote_config.dart';

import '../controllers/login_controller.dart';
import '../pages/home/<USER>';
import '../utils/Notify.dart';

abstract class BaseApi {
  static Dio? client;

  static fullUrl(url) {
    return RemoteConfigService.apiUrl + url;
  }

  static Dio getDio() {
    final LoginController controller = Get.find();
    ReadDeviceInfo.readAppInfo();
    ReadDeviceInfo.readDeviceInfo();

    if (client == null) {
      client = Dio();

      client!.interceptors
          .add(InterceptorsWrapper(onRequest: (RequestOptions options, RequestInterceptorHandler handler) async {
        PackageInfo packageInfo = await PackageInfo.fromPlatform();
        var customHeaders = {
          'Authorization': 'Bearer ${controller.token}',
          'Accept': 'application/json',
          'User-Type': obProvider.userType.name,
          'City': obProvider.city.name,
          'City-Id': obProvider.city.id,
          'Society-Id': obProvider.society.id,
        };
        options.headers.addAll(customHeaders);
        var deviceInfo = ReadDeviceInfo.readDeviceInfo();
        if(deviceInfo.isNotEmpty){
          options.headers.addAll(deviceInfo);
        }
        var appInfo = ReadDeviceInfo.readAppInfo();
        if(appInfo.isNotEmpty){
          options.headers.addAll(appInfo);
        }
        return handler.next(options);
      }));
    }
    return client!;
  }

  static Future<APIResponse<T>> get<T>(url, {cache = 0, hourCache = 0}) async {
    try {
      if(kDebugMode) {
        print('GET: $url');
      }
      Response response =
          await getDio().get(fullUrl(url));
      return decode<T>(response);
    } on SocketException {
      return APIResponse<T>(status: ApiStatus(status: ApiStatus.NO_INTERNET));
    } on DioError catch (e) {
      return decode<T>(e.response);
    }
  }

  static Future<APIResponse<T>> post<T>(url, body) async {
    try {
      if(kDebugMode) {
        print('POST: $url,$body');
      }
      dynamic response = await getDio().post(fullUrl(url), data: body);
      return decode<T>(response);
    } on SocketException {
      return APIResponse<T>(status: ApiStatus(status: ApiStatus.NO_INTERNET));
    } on DioError catch (e) {
      return decode<T>(e.response);
    }
  }

  static APIResponse<T> decode<T>(response) {
    APIResponse<T> _response = new APIResponse();
    _response.status = ApiStatus(status: response.statusCode);
    _response.rawResponse = response;
    try {
      if (_response.status!.status == ApiStatus.UNAUTHENTICATED) {
        final LoginController login = Get.find();
        Notify.toast('Your session has been expired,Please login again to continue.');
        login.logout();
        Get.offAll(HomePage());
      }

      if (response.data is List) {
        _response.isList = true;
        if (response.data.length == 0) _response.status!.status = ApiStatus.NOT_FOUND;
        _response.data = response.data;
        return _response;
      }

      Map<String, dynamic> json = response.data;

      if (json.containsKey('message')) _response.status!.errorMessage = json['message'];

      if (json.containsKey('errors')) _response.status!.errors = json['errors'];

      T? _data = json.containsKey('data') ? json['data'] : json as T?;
      _response.data = _data;
      _response.hasMore = json['has_more'] ?? false;

      if (json.containsKey('links')) {
        if (json['links']['next'] is String)
          _response.hasMore = (json['links']['next'] as String).isNotEmpty;
        else
          _response.hasMore = false;
      }

      if (_data is List) {
        _response.isList = true;
        if (_data.length == 0) _response.status!.status = ApiStatus.NOT_FOUND;
      }
    } catch (e) {
      _response.status!.status = ApiStatus.UNKNOWN;
    }
    return _response;
  }
}

class APIResponse<T> {
  T? data;
  bool? hasMore = false;
  bool? isList = false;
  ApiStatus? status;
  dynamic rawResponse;
  bool get isOK => status?.status == ApiStatus.OK;
  APIResponse({this.data, this.status, this.hasMore, this.isList,this.rawResponse});
}

class ApiStatus {
  static const int OK = 200;
  static const int NO_INTERNET = 410;
  static const int LOGIN_ERROR = 400;
  static const int UNAUTHENTICATED = 401;
  static const int FORBIDDEN = 403;
  static const int NOT_FOUND = 404;
  static const int UNKNOWN = 409;

  int status;
  String errorMessage;
  Map<String, dynamic>? errors;

  String get message {
    if (errorMessage.isNotEmpty) return errorMessage;
    switch (status) {
      case UNAUTHENTICATED:
        return 'Unauthorized';
      case FORBIDDEN:
        return 'Not Allowed';
      case NOT_FOUND:
        return 'Not Found';
      case LOGIN_ERROR:
        return 'Username or Password is incorrect. Please try again';
      case NO_INTERNET:
        return 'Internet is not available, Please turn on Wifi or Mobile Data & Try again';
      case UNKNOWN:
      default:
        return 'Communication Error, Please try again';
    }
  }

  ApiStatus({this.status = OK, this.errorMessage = ''});
}
