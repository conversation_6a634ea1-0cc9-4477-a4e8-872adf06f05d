import 'dart:convert';

import 'package:shared_preferences/shared_preferences.dart';

import '../models/login_entity.dart';

class Prefs {
  static Future saveLogin(LoginEntity userEntity) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('USER_IS_LOGGED', true);
    await prefs.setString('USER_LOGIN_KEY', userEntity.token);
    await prefs.setString('USER_OBJECT', userEntity.toJson().toString());
  }

  static Future<bool> isLoggedIn() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getBool('USER_IS_LOGGED') ?? false;
  }

  static Future<String> getToken() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs.getString('USER_LOGIN_KEY') ?? '';
  }

  static Future<LoginEntity?> getLogin() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return LoginEntity.fromJson(jsonDecode(prefs.getString('USER_OBJECT')!));
  }

  static Future logout() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setBool('USER_IS_LOGGED', false);
    await prefs.setString('USER_LOGIN_KEY', '');
    await prefs.setString('USER_OBJECT', '');
  }
}
