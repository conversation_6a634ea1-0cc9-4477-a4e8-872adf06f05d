import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import '../services/remote_config.dart';

class AdMobService {
  static const String LOCATION_MAP = 'map';
  static const String LOCATION_INVENTORY_TOP = 'inventory_top';
  static const String LOCATION_INVENTORY_LIST = 'inventory_list';
  static const String LOCATION_CLASSIFIED_LIST = 'classified_list';

  static String testAdId = 'ca-app-pub-3940256099942544/6300978111';

  static String getAdId(String location) {
    // if (kDebugMode) return AdMobService.testAdId;

    switch (location) {
      case LOCATION_MAP:
        return Platform.isAndroid ? 'ca-app-pub-8557684672668225/7929321948' : 'ca-app-pub-8557684672668225/9789198522';
      case LOCATION_INVENTORY_TOP:
        return Platform.isAndroid ? 'ca-app-pub-8557684672668225/7961698269' : 'ca-app-pub-8557684672668225/1943084828';
      case LOCATION_INVENTORY_LIST:
        return Platform.isAndroid ? 'ca-app-pub-8557684672668225/7228779058' : 'ca-app-pub-8557684672668225/9471799015';
      case LOCATION_CLASSIFIED_LIST:
        return Platform.isAndroid ? 'ca-app-pub-8557684672668225/6390340101' : 'ca-app-pub-8557684672668225/6835292739';
      default:
        return AdMobService.testAdId;
    }
  }

  static bool canDisplay(String location) {
    if (kIsWeb) return false;
    switch (location) {
      case LOCATION_MAP:
        return RemoteConfigService.adBannerMap;
      case LOCATION_INVENTORY_TOP:
        return RemoteConfigService.adBannerInventoryTop;
      case LOCATION_INVENTORY_LIST:
        return RemoteConfigService.adBannerInventoryList;
      case LOCATION_CLASSIFIED_LIST:
        return RemoteConfigService.adBannerClassifiedList;
      default:
        return false;
    }
  }

  static initialize() {
    if (kIsWeb) {
      return;
    }
    if (MobileAds.instance == null) {
      MobileAds.instance.initialize();
      MobileAds.instance
          .updateRequestConfiguration(RequestConfiguration(testDeviceIds: ['20AC3954AEF4CB3EF4E827934E1BBE68']));
    }
  }

  static BannerAd createBannerAd(String location) {
    BannerAd ad = new BannerAd(
      adUnitId: getAdId(location),
      size: AdSize.largeBanner,
      request: AdRequest(),
      listener: BannerAdListener(
          onAdLoaded: (Ad ad) => print('Ad loaded'),
          onAdFailedToLoad: (Ad ad, LoadAdError error) {
            ad.dispose();
          },
          onAdOpened: (Ad ad) => print('Ad opened'),
          onAdClosed: (Ad ad) => print('On Ad Closed')), // AdListener
    ); // BannerAd
    return ad;
  }
}
