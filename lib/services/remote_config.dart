import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:property_dealers_directory/config.dart';

class RemoteConfigService {
  static int versionAndroid = getInt('latest_version_android');
  static int versionIos = getInt('latest_version_ios');
  static bool isFindPlotEnabled = getBool('find_plot_enabled');
  static bool featureProjectVisibility = getBool('feature_project_visibility');
  static bool rateListVisibility = getBool('rate_list_visibility');
  static bool isPlotHot = getBool('is_plot_hot');
  static bool isHowToEnabled = getBool('is_how_to_enabled');
  static bool showImpressions = getBool('show_impressions');
  static bool showClicks = getBool('show_clicks');
  static bool showClassifiedImpressions = getBool('show_classified_impressions');
  static bool showClassifiedViews = getBool('show_classified_views');
  static bool showClassifiedClicks = getBool('show_classified_clicks');
  static int adsPopupAfter = getInt('ads_popup_after');
  static bool showMapsV2 = getBool('show_maps_v2');
  ///my changes
  static String apiUrl = getString('api_url');
  static String webUrl = getString('web_url');
  static String storageUrl = getString('storage_url');
  static String contactPhone = getString('contact_phone');

  static bool isSatelliteEnabled = getBool('is_satellite_enabled');
  static String satelliteUrl = getString('satellite_url');

  static bool adBannerMap = getBool('ads_banner_map');
  static bool adBannerInventoryTop = getBool('ads_banner_inventory_top');
  static bool adBannerInventoryList = getBool('ads_banner_inventory_list');
  static bool adBannerClassifiedList = getBool('ads_banner_classified_list');

  static bool isInventoryBehindLogin = getBool('inventory_behind_login');

  static bool enableMarketInventory = getBool('enable_market_inventory');
  static bool mapMultiForSociety = getBool('map_multi_for_society');


  static FirebaseRemoteConfig config = setupRemoteConfig();

  static Map<String, dynamic> defaults = {
    'latest_version_android': 0,
    'latest_version_ios': 0,
    'find_plot_enabled': true,
    'feature_project_visibility': true,
    'rate_list_visibility': true,
    'is_plot_hot': true,
    'is_how_to_enabled': false,
    'show_impressions': true,
    'show_clicks': false,
    'show_classified_impressions': true,
    'show_classified_views': false,
    'show_classified_clicks': false,
    'ads_popup_after': 3,
    'show_maps_v2': true,
    'ads_banner_map': true,
    'ads_banner_inventory_top': true,
    'ads_banner_inventory_list': true,
    'ads_banner_classified_list': true,
    'enable_market_inventory': true,
    'is_satellite_enabled' : true,
    'satellite_url': 'https://services.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}',

    ///my changes
    'api_url': Config.API_URL,
    'web_url': Config.WEB_URL,
    'storage_url': Config.STORAGE_URL,
    'contact_phone': '+923218041703',
    'inventory_behind_login': false,
    'map_multi_for_society': true,
  };

  static bool getBool(String key) {
    return config.getBool(key);
  }

  static int getInt(String key) {
    return config.getInt(key);
  }

  static String getString(String key) {
    return config.getString(key);
  }

  static Future<void> fetch() async {
    await activate();
  }

  static Future<void> activate() async {
    try {
      await config.fetch();
      await config.fetchAndActivate();
    } on Exception catch (exception) {
      print(exception);
    } catch (exception) {
      print('Unable to fetch remote config. Cached or default values will be used');
    }
  }

  static FirebaseRemoteConfig setupRemoteConfig() {
    final FirebaseRemoteConfig remoteConfig = FirebaseRemoteConfig.instance;
    remoteConfig.setConfigSettings(
      RemoteConfigSettings(
        fetchTimeout: Duration(milliseconds: 1000 * 60 * 5),
        minimumFetchInterval: Duration(milliseconds: 1000 * 60 * 5),
      ),
    );
    remoteConfig.setDefaults(defaults);
    return remoteConfig;
  }
}
