import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../config.dart';
import '../services/remote_config.dart';
import '../utils/helpers.dart';

class UpdateChecker {
  Future<bool> isAvailable() async {
    if (kIsWeb) return false;
    final PackageInfo info = await PackageInfo.fromPlatform();

    int latestVersion = Platform.isIOS ? RemoteConfigService.versionIos : RemoteConfigService.versionAndroid;

    return latestVersion > int.tryParse(info.buildNumber)!;
  }

  void check() {
    isAvailable().then((value) {
      if (value) {
        Get.dialog(
          AlertDialog(
            title: Text("Update is Available"),
            content: Text(
                'A new version of the app is available, Please download latest version from store to use new exciting features.'),
            actions: <Widget>[
              TextButton(
                child: Text('Update Now'),
                onPressed: () {
                  Get.back();
                  String url = Platform.isAndroid
                      ? 'market://details?id=${Config.PLAY_STORE_PACKAGE}'
                      : 'https://apps.apple.com/us/plus/app/id${Config.APPLE_ID}';

                  openLink(url);
                },
              ),
              TextButton(
                child: Text('Later'),
                onPressed: () {
                  Get.back();
                },
              )
            ],
          ),
        );
      }
    });
  }
}
