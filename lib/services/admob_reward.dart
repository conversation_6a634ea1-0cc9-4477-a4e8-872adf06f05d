import 'dart:io';

import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:property_dealers_directory/data/read_device_info.dart';
import 'package:property_dealers_directory/services/addmob_services.dart';

class AdmobRewardService {
  static RewardedAd? rewardedAd;

  static initialize() {
    AdMobService.initialize();
    // MobileAds.instance.
  }

  static String getId() {
    // return 'ca-app-pub-3940256099942544/5224354917';
    return Platform.isAndroid
        ? 'ca-app-pub-8557684672668225/7510214921'
        : 'ca-app-pub-8557684672668225/5048575258';
  }

  static loadAd(String token, VoidCallback? onAdLoaded) {
    return RewardedAd.load(
      adUnitId: getId(),
      request: const AdRequest(),
      rewardedAdLoadCallback: RewardedAdLoadCallback(
        onAdLoaded: (RewardedAd ad) {
          print('$ad loaded.');
          rewardedAd = ad;
          rewardedAd?.setServerSideOptions(
            ServerSideVerificationOptions(
              customData: token,
            ));
          onAdLoaded?.call();
          ad.fullScreenContentCallback = FullScreenContentCallback(
            // Called when the ad failed to show full screen content.
            onAdFailedToShowFullScreenContent: (ad, err) {
              // Dispose the ad here to free resources.
              ad.dispose();
              rewardedAd = null;
            },
            // Called when the ad dismissed full screen content.
            onAdDismissedFullScreenContent: (ad) {
              // Dispose the ad here to free resources.
              ad.dispose();
              rewardedAd = null;
            },
          );
        },
        onAdFailedToLoad: (LoadAdError error) {
          print('RewardedAd failed to load: $error');
          rewardedAd = null;
          onAdLoaded?.call();
        },
      ),
    );
  }
}
