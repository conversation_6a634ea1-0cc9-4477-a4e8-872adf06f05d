import 'package:auto_size_text/auto_size_text.dart';
import 'package:flutter/material.dart';

import '../models/agency_short_entity.dart';
import '../nav.dart';
import '../utils/functions.dart';
import '../utils/widgets.dart';

class AgencyListItem extends StatelessWidget {
  final AgencyShortEntity agency;

  AgencyListItem(this.agency);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        Nav.agency(context, agency.id);
        sendAnalyticsEvent(eventName: 'list_agency_item_click', parameters: {'agency_id': agency.id});
      },
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: TileCard(
          horizontal: 0,
          vertical: 0,
          elevation: 5,
          child: Container(
            height: 100,
            decoration: agency.isFeatured
                ? BoxDecoration(border: Border(bottom: BorderSide(color: Theme.of(context).primaryColor, width: 3)))
                : null,
            child: Row(
              children: <Widget>[
                NetworkImageWithPlaceholder(agency.image, width: 100, text: agency.name),
                SizedBox(width: 5),
                Flexible(
                  child: Container(
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 1.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 1.0),
                            child: Text(
                              agency.name,
                              maxLines: 2,
                              textAlign: TextAlign.left,
                              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                          ),
                          SizedBox(height: 2.0),
                          Row(
                            children: <Widget>[
                              Icon(Icons.person, size: 18.0),
                              Text(agency.ceo),
                            ],
                          ),
                          SizedBox(height: 2.0),
                          Row(
                            children: <Widget>[
                              Icon(Icons.location_on, size: 18.0),
                              Expanded(child: AutoSizeText(agency.societyName,maxLines: 1, overflow: TextOverflow.ellipsis, )),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
