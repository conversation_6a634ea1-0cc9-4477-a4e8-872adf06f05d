import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/utils/functions.dart';
import 'package:readmore/readmore.dart';

import '../models/agency_entity.dart';
import '../nav.dart';
import '../utils/helpers.dart';
import '../utils/widgets.dart';
import '../widgets/property_selection_dialog.dart';
import '../widgets/social.dart';

class AgencyDetails extends StatelessWidget {
  final AgencyEntity agency;

  AgencyDetails(this.agency);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(5),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 10.0),
            child: Container(
              height: 100,
              width: 100,
              child: NetworkImageWithPlaceholder(agency.image, cover: false, radius: 10.0, width: 100, height: 100),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(agency.name, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 22)),
                Text('${agency.city!.name} - ${agency.society!.name}'),
                if (agency.description != null)
                  ReadMoreText(
                    agency.description!,
                    trimLines: 1,
                    trimMode: TrimMode.Line,
                    style: TextStyle(color: Colors.black),
                  ),
                SizedBox(height: 5),
                CallButtonsRow(agency: agency)
              ],
            ),
          )
        ],
      ),
    );
  }
}

class CallButtonsRow extends StatelessWidget {
  final AgencyEntity agency;

  const CallButtonsRow({Key? key, required this.agency}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        SocialIcon.type(
          'call',
          SocialIcon.PHONE,
          onPressed: () {
            _showDialog(context);
            sendAnalyticsEvent(eventName: 'detail_agency_call_button_click', parameters: {'agency_id': agency.id});
          },
        ),
        SocialIcon.type(
          '',
          SocialIcon.WHATSAPP,
          onPressed: () {
            sendAnalyticsEvent(eventName: 'detail_agency_whatsApp_click', parameters: {'agency_id': agency.id});
            openLink('https://wa.me/+92${agency.whatsapp!.substring(1)}?text=I have seen your agency in ilaaqa.com app');
          },
        ),
        PrimaryButton(
          text: 'Properties',
          onPressed: () {
            showDialog(
              context: context,
              builder: (BuildContext context) {
                return PropertySelectionDialog(
                  fTap: () {
                    Nav.back(context);
                    Nav.propertiesPage(context, agencyId: agency.id);
                    sendAnalyticsEvent(
                        eventName: 'detail_agency_inventoryAds_click', parameters: {'agency_id': agency.id});
                  },
                  sTap: () {
                    Nav.back(context);
                    Nav.classifiedPropertyListPage(context, agencyId: agency.id);
                    sendAnalyticsEvent(
                        eventName: 'detail_agency_classifiedAds_click', parameters: {'agency_id': agency.id});
                  },
                );
              },
            );
          },
        ),
      ],
    );
  }

  void _showDialog(context) {
    showDialog<void>(
      context: context,
      barrierDismissible: true, // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        return SimpleDialog(
          title: Text('Contact'),
          children: [
            if (agency.mobile1.isNotEmpty)
              ListTile(
                title: Text(agency.mobile1),
                subtitle: Text('Mobile 1'),
                leading: Icon(Icons.phone_iphone),
                onTap: () {
                  Get.back();
                  openLink('tel: +92${agency.mobile1.substring(1)}');
                },
              ),
            if (agency.mobile2!.isNotEmpty)
              ListTile(
                title: Text(agency.mobile2!),
                subtitle: Text('Mobile 2'),
                leading: Icon(Icons.phone_iphone),
                onTap: () {
                  Get.back();
                  openLink('tel: +92${agency.mobile2!.substring(1)}');
                },
              ),
            if (agency.landline1!.isNotEmpty)
              ListTile(
                title: Text(agency.landline1!),
                subtitle: Text('Landline 1'),
                leading: Icon(Icons.phone),
                onTap: () {
                  Get.back();
                  openLink('tel: +92${agency.landline1!.substring(1)}');
                },
              ),
            if (agency.landline2!.isNotEmpty)
              ListTile(
                title: Text(agency.landline2!),
                subtitle: Text('Landline 2'),
                leading: Icon(Icons.phone),
                onTap: () {
                  Get.back();
                  openLink('tel: +92${agency.landline2!.substring(1)}');
                },
              ),
          ],
        );
      },
    );
  }
}
