import 'package:flutter/material.dart';
import 'package:find_dropdown/find_dropdown.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
export 'package:dropdown_search/dropdown_search.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:provider/provider.dart';

import '../../controllers/city_selected.dart';
import '../../data/api.dart';
import '../../models/block_entity.dart';
import '../../models/city_entity.dart';
import '../../utils/base_state.dart';
import '../customized_lib/form_builder/find_dropdown_field.dart';
import '../utils/Notify.dart';

class FilterProperty extends StatefulWidget {
  final VoidCallback callback;
  FilterProperty({Key? key, required this.callback}) : super(key: key);

  @override
  State<FilterProperty> createState() => _FilterPropertyState();
}

class _FilterPropertyState extends BaseState<FilterProperty, List<CityEntity>> {
  final GlobalKey<FindDropdownState> _dpKeySociety = GlobalKey<FindDropdownState>();
  final GlobalKey<FindDropdownState> _dpKeyBlock = GlobalKey<FindDropdownState>();
  final _formKey = GlobalKey<FormBuilderState>();

  List categoryList = ['Residential', 'Commercial'];
  String category = 'Residential';

  CityEntity city = CityEntity();
  CityEntity society = CityEntity();
  BlockEntity block = BlockEntity();

  @override
  bool get pullToRefresh => false;

  @override
  bool get withScaffold => false;

  @override
  bool get showAd => false;

  @override
  Widget get pageLoader => SizedBox.shrink();

  @override
  void getData() {
    setState(() {
      data = [];
      loading = false;
    });
  }

  List<BlockEntity> blocks = [];



  @override
  Widget body() {
    final classifiedProvider = context.watch<FiltersProvider>();
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
      child: FormBuilder(
        key: _formKey,
        child: Row(
          children: [
            Expanded(
              child: FindDropDownField(
                // key: UniqueKey(),
                name: 'city_id',
                // label: 'City',
                hint: 'Search City',
                initialValue: classifiedProvider.city.isEmpty ? null : classifiedProvider.city,
                context: context,
                items: obProvider.cities,
                resetField: 'society_id',
                onChanged: (item) {
                  classifiedProvider.citySelected(obProvider.cities, item!.selectedLabel);
                  widget.callback();
                  setState(() {
                    // _formKey.currentState!.fields['society_id']!.didChange(null);
                    _dpKeySociety.currentState!.setSelectedItem(null);
                    // _dpKeyBlock.currentState!.setSelectedItem(null);
                    var city = item as CityEntity;
                     if( city.societies?.length == 1){
                       _formKey.currentState!.fields["society_id"]!.didChange(city.societies!.first);
                       _dpKeySociety.currentState!.setSelectedItem(city.societies!.first);
                     }
                  });
                },
              ),
            ),
            SizedBox(width: 10),
            Expanded(
              child: FindDropDownField(
                // key: UniqueKey(),
                key: _dpKeySociety,
                initialValue: classifiedProvider.society.isEmpty ? null : classifiedProvider.society,
                // selectedItem: null,
                name: 'society_id',
                // label: 'Society',
                hint: 'Search Society',
                context: context,
                items: classifiedProvider.getSocieties,
                resetField: 'block_id',
                onChanged: (item) {
                  classifiedProvider.societySelected(item?.selectedLabel, category);
                  setState(() {
                    _dpKeyBlock.currentState!.setSelectedItem(null);
                  });
                  widget.callback();
                  print('Society changed');
                },
              ),
            ),
            SizedBox(width: 10),
            Expanded(
              child: IgnorePointer(
                ignoring:classifiedProvider.blocks.isEmpty,
                child: FindDropDownField(
                  key: _dpKeyBlock,
                  name: 'block_id',
                  // label: 'Block',
                  hint: 'Search Block',
                  context: context,
                  items: classifiedProvider.blocks,
                  onChanged: (item) {
                    classifiedProvider.blockSelected(item?.dropDownText);
                    widget.callback();
                  },
                ),
              ),
            ),

            // ElevatedButton(onPressed: (){
            //   _formKey.currentState!.saveAndValidate();
            //   print(_formKey.currentState!.value);
            // }, child: Text('Click'))
          ],
          // SizedBox(height: 100,),
        ),
      ),
    );
  }
}
