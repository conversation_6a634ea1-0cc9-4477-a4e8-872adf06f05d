import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/nav.dart';
import 'package:provider/provider.dart';

import '../pages/classified_property/filters_provider.dart';
import '../pages/properties_list/properties_list_page.dart';
import '../utils/helpers.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class NotificationPopup extends StatelessWidget {
  const NotificationPopup({
    Key? key,
    required this.context,
    required this.message,
  }) : super(key: key);

  final BuildContext context;
  final Map<String, dynamic> message;

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    return Dialog(
        shape: RoundedRectangleBorder(
            borderRadius:
            BorderRadius.circular(8.0)), //this right here
        child: Container(
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                 AspectRatio(
                   aspectRatio: 2/1,
                   child: Container(
                     decoration: BoxDecoration(
                       borderRadius: BorderRadius.only(
                         topLeft: Radius.circular(8.0),
                         topRight: Radius.circular(8.0),
                       ),
                       image: DecorationImage(
                         image: NetworkImage(message['image'],),
                         fit: BoxFit.cover,
                       ),
                     ),
                   ),
                 ),
                  Padding(
                    padding: const EdgeInsets.all(6.0),
                    child: Heading(
                      message['title'],
                      font: 20,
                      color: Theme.of(context).primaryColor,
                      weight: FontWeight.bold,
                    ),
                  ),
                  // SizedBox(height: 10),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 6.0),
                    child: Text(message['body'], style: TextStyle(fontSize: 15),),
                  ),
                  SizedBox(height: 12),
                  if (message['action_type'] != 'dismiss')
                    Center(
                      child: ElevatedButton(
                        child: Text(message['action_title'], style: TextStyle(fontSize: 18),),
                        onPressed: () {
                          Get.back();
                          Nav.internalLink(context, message['action_type'], message['action_value']);
                        },
                      ),
                    ),
                  SizedBox(height: 12),
                ],
              ),
              Positioned(
                right: 5,
                top: 5,
                child:  Container(
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: InkWell(
                      onTap: (){
                        Nav.back(context);
                      },
                      child: Icon(Icons.cancel_rounded, color: Theme.of(context).primaryColor,)),
                ),
              ),
            ],
          ),
        )
    );
  }
}
