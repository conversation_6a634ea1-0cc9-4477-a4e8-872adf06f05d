import 'package:flutter/material.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';

import '../services/addmob_services.dart';

class AdmobBanner extends StatefulWidget {
  const AdmobBanner({super.key, required this.size, required this.location});

  final AdSize size;
  final String location;

  @override
  State<AdmobBanner> createState() => _AdmobBannerState();
}

class _AdmobBannerState extends State<AdmobBanner> {
  bool show = false;
  BannerAd? _ad;

  @override
  void initState() {
    _ad = new BannerAd(
      adUnitId: AdMobService.getAdId(widget.location),
      size: widget.size,
      request: AdRequest(),
      listener: BannerAdListener(
          onAdLoaded: (Ad ad) {
            print('Ad loaded');
            setState(() {
              show = true;
            });
          },
          onAdFailedToLoad: (Ad ad, LoadAdError error) {
            print('Ad failed to load: $error');
            ad.dispose();
          },
          onAdOpened: (Ad ad) => print('Ad opened'),
          onAdClosed: (Ad ad) => print('On Ad Closed')), // AdListener
    )..load(); // BannerAd
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return show
        ? Container(
            alignment: Alignment.center,
            margin: EdgeInsets.symmetric(vertical: 5),
            child: AdWidget(ad: _ad!, key: widget.key),
            width: widget.size.width.toDouble(),
            height: widget.size.height.toDouble(),
          )
        : Container();
  }
}
