import 'package:flutter/material.dart';
import 'package:property_dealers_directory/models/classified_entity.dart';

import '../../utils/widgets.dart';

class GalleryView extends StatefulWidget {
  final List<ClassifiedMedia> media;

  GalleryView(this.media);

  @override
  State<GalleryView> createState() => _GalleryViewState();
}

class _GalleryViewState extends State<GalleryView> {
  final _controller = ScrollController();
  bool isSelected = false;
  int changeImage = 0;

  void _goToNext(int index) {
    _controller.animateTo((140.0 * index), duration: const Duration(milliseconds: 200), curve: Curves.bounceIn);
  }

  void _goToBack(int index) {
    _controller.animateTo((100.0 * index), duration: const Duration(milliseconds: 200), curve: Curves.bounceIn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text("Gallery")),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Stack(
              children: [
                InteractiveViewer(
                  child: Hero(
                    tag: widget.media[changeImage],
                    child: NetworkImageWithPlaceholder(
                      widget.media[changeImage].medium,
                      height: MediaQuery.of(context).size.height * .7,
                      width: MediaQuery.of(context).size.width,
                      cover: false,
                    ),
                  ),
                ),
                Positioned(
                  right: 10,
                  top: MediaQuery.of(context).size.height * .3,
                  child: widget.media.length == 1 || changeImage == widget.media.length - 1
                      ? Container()
                      : Container(
                          height: 40,
                          width: 40,
                          child: FloatingActionButton(
                              backgroundColor: Colors.white,
                              onPressed: () {
                                if (changeImage < (widget.media.length - 1))
                                  setState(() {
                                    _goToNext(changeImage + 1);
                                    changeImage++;
                                  });
                              },
                              child: Icon(Icons.navigate_next, color: Colors.black)),
                        ),
                ),
                Positioned(
                  left: 10,
                  top: MediaQuery.of(context).size.height * .3,
                  child: widget.media.length == 1 || changeImage == 0
                      ? Container()
                      : Container(
                          height: 40,
                          width: 40,
                          child: FloatingActionButton(
                              backgroundColor: Colors.white,
                              onPressed: () {
                                if (changeImage > 0)
                                  setState(() {
                                    _goToBack(changeImage - 1);
                                    changeImage--;
                                  });
                              },
                              child: Icon(Icons.navigate_before, color: Colors.black)),
                        ),
                ),
              ],
            ),
            Container(
              height: 140,
              child: ListView.builder(
                shrinkWrap: true,
                controller: _controller,
                scrollDirection: Axis.horizontal,
                itemCount: widget.media.length,
                itemBuilder: (context, i) {
                  return Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      decoration: BoxDecoration(
                          border: changeImage == i ? Border.all(color: Theme.of(context).primaryColor, width: 3) : null,
                          borderRadius: BorderRadius.circular(17)),
                      child: GestureDetector(
                          onTap: () {
                            setState(() {
                              changeImage = i;
                              print('.......$i');
                            });
                          },
                          child: NetworkImageWithPlaceholder(widget.media[i].thumb, width: 140, radius: 14)),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
