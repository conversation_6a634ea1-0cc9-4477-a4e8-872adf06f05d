import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class NotAvailable extends StatelessWidget {
  final String text;
  final double size;
  final String title;
  final String? buttonText;
  final Function? callback;

  NotAvailable(this.text, {this.size = 150.0, this.title = 'ERROR', this.buttonText, this.callback});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                  height: size,
                  child: Lottie.asset("animations/sad-search.json",
                      alignment: Alignment.center, fit: BoxFit.contain, repeat: false)),
              Text(title, style: TextStyle(fontSize: 16.0, fontWeight: FontWeight.w900, color: Colors.black54)),
              SizedBox(height: 10.0),
              Text(text,
                  textAlign: TextAlign.center,
                  style: TextStyle(fontSize: 14.0, fontWeight: FontWeight.w600, color: Colors.black45)),
              SizedBox(height: 20.0),
              if (buttonText != null) _submitButton(context)
            ],
          ),
        ),
      ),
    );
  }

  Widget _submitButton(context) {
    return ElevatedButton(
      style: ButtonStyle(
        elevation: MaterialStateProperty.all(0),
        shape: MaterialStateProperty.all(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
            side: BorderSide(color: Colors.blue, width: 2),
          ),
        ),
        backgroundColor: MaterialStateProperty.all(Colors.transparent),
      ),
      onPressed: () {
        callback!(context);
      },
      child: Text(buttonText!, style: TextStyle(fontWeight: FontWeight.w600, color: Colors.blue, fontSize: 14)),
    );
  }
}
