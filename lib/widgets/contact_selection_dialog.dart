import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:property_dealers_directory/widgets/social.dart';

import '../utils/text.dart';

class ContactSelectionDialog extends StatelessWidget {
  const ContactSelectionDialog({Key? key, this.callTap, this.wTap}) : super(key: key);

  final callTap;
  final wTap;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      insetPadding: EdgeInsets.symmetric(vertical: MediaQuery.of(context).size.height * .34),
      title: Heading.big('Select to Contact', font: 20),
      content: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          <PERSON>umn(
            children: [
              SocialIcon.type(
                'Call',
                SocialIcon.PHONE,
                onPressed: callTap,
              ),
              <PERSON><PERSON><PERSON><PERSON>(height: 10),
              Heading.big('Call', font: 16, ),
            ],
          ),

          <PERSON><PERSON><PERSON>(
            children: [
              SocialIcon.type(
                'WhatsApp',
                SocialIcon.WHATSAPP,
                onPressed: wTap,
              ),
              Sized<PERSON>ox(height: 10),
              Heading.big('WhatsApp', font: 16, ),
            ],
          ),
        ],
      ),
    );
  }
}
