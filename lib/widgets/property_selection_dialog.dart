import 'package:flutter/material.dart';
import 'package:property_dealers_directory/services/remote_config.dart';

import '../utils/text.dart';

class PropertySelectionDialog extends StatelessWidget {
  const PropertySelectionDialog({Key? key, this.fTap, this.sTap, this.mTap}) : super(key: key);

  final fTap;
  final sTap;
  final mTap;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: RichHeading.auto(text: 'Select Property Type', fontSize: 20),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: InkWell(
                  onTap: fTap,
                  child: Column(
                    children: [
                      Image.asset('images/inventory.png', height: 50, width: 50),
                      SizedBox(height: 10),
                      Heading('Inventories', font: 14, color: Colors.black),
                    ],
                  ),
                ),
              ),

              Padding(
                padding: const EdgeInsets.all(8.0),
                child: InkWell(
                  onTap: sTap,
                  child: Column(
                    children: [
                      Image.asset('images/classified.png', height: 50, width: 50),
                      SizedBox(height: 10),
                      Heading('Classifieds', font: 14, color: Colors.black),
                    ],
                  ),
                ),
              ),

              if(mTap != null && RemoteConfigService.enableMarketInventory)
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: InkWell(
                    onTap: mTap,
                    child: Column(
                      children: [
                        Image.asset('images/market-inventory.png', height: 50, width: 50),
                        SizedBox(height: 10),
                        Heading('Market Inventory', font: 14, color: Colors.black),
                      ],
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }
}
