import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:property_dealers_directory/models/ad_banner_entity.dart';
import 'package:property_dealers_directory/providers/adbanner_provider.dart';
import 'package:property_dealers_directory/utils/helpers.dart';
import 'package:property_dealers_directory/utils/widgets.dart';

class CustomAdBanner extends StatelessWidget {
  CustomAdBanner({Key? key, required this.position}) : super(key: key) {
    if (adProvider.hasBanner(position)) {
      ad = adProvider.getNextBanner(position);
      // adProvider.view(ad);
    } else {
      ad = AdBannerEntity();
    }
  }

  final AdBannerPosition position;
  late AdBannerEntity ad;

  @override
  Widget build(BuildContext context) {
    return ad.isValid
        ? InkWell(
            onTap: () {
              adProvider.click(ad);
            },
            child: Container(
              alignment: Alignment.center,
              margin: const EdgeInsets.symmetric(vertical: 3, horizontal: 8),
              child: CachedNetworkImage(
                width: double.infinity,
                fit: BoxFit.contain,
                imageUrl: ad.image,
                errorWidget: (_, __, ___) => Container(),
              ),
            ),
          )
        : Container();
  }
}

class AdBannerPopup extends StatelessWidget {
  AdBannerPopup({Key? key, required this.position}) : super(key: key) {
    if (adProvider.hasBanner(position)) {
      ad = adProvider.getNextBanner(position);
      adProvider.view(ad);
    } else {
      ad = AdBannerEntity();
    }
  }

  final AdBannerPosition position;
  late AdBannerEntity ad;

  static void show(BuildContext context) {
    if (adProvider.hasBanner(AdBannerPosition.popup)) {
      showDialog(
        context: context,
        builder: (context) {
          return AdBannerPopup(position: AdBannerPosition.popup);
        },
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: InkWell(
        onTap: () {
          Navigator.pop(context);
        },
        child: Container(
          color: Theme.of(context).primaryColor.withOpacity(0.1),
          // color: Colors.transparent,
          child: Stack(
            children: [
              Center(
                child: Container(
                  margin: const EdgeInsets.symmetric(horizontal: 8),
                  child: InkWell(
                    onTap: () {
                      Navigator.pop(context);
                      adProvider.click(ad);
                    },
                    child: CachedNetworkImage(
                      width: double.infinity,
                      fit: BoxFit.contain,
                      imageUrl: ad.image,
                      errorWidget: (_, __, ___) => Container(),
                    ),
                  ),
                ),
              ),
              Positioned(
                top: 50,
                right: 8,
                child: IconButton(
                  icon: Icon(Icons.close, color: Colors.white),
                  onPressed: () {
                    Navigator.pop(context);
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
