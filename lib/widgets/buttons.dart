import 'package:flutter/material.dart';

class GradientButton extends StatelessWidget {
  final String text;
  final Function? onTap;
  final Color colorEnd;
  final Color colorStart;
  final Widget? child;
  final double radius;
  final bool shadow;

  GradientButton({
    this.text = '',
    this.onTap,
    this.colorEnd = const Color(0xFFfbab66),
    this.colorStart = const Color(0xFFf7418c),
    this.child,
    this.radius = 5.0,
    this.shadow = true,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: new BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(radius)),
        boxShadow: shadow
            ? <BoxShadow>[
                BoxShadow(
                  color: colorEnd,
                  offset: Offset(1.0, 6.0),
                  blurRadius: 20.0,
                ),
                BoxShadow(
                  color: colorStart,
                  offset: Offset(1.0, 6.0),
                  blurRadius: 20.0,
                ),
              ]
            : null,
        gradient: new LinearGradient(
          colors: [colorStart, colorEnd],
          begin: const FractionalOffset(0.2, 0.2),
          end: const FractionalOffset(1.0, 1.0),
          stops: [0.0, 1.0],
          tileMode: TileMode.clamp,
        ),
      ),
      child: MaterialButton(
          highlightColor: Colors.transparent,
          splashColor: colorStart,
          //shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(5.0))),
          child: this.child != null
              ? this.child
              : Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10.0, horizontal: 42.0),
                  child: Text(
                    text,
                    style: TextStyle(color: Colors.white, fontSize: 25.0, fontFamily: "WorkSansBold"),
                  ),
                ),
          onPressed: onTap as void Function()?),
    );
  }
}
