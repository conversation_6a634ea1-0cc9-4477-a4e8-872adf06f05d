import 'package:flutter/material.dart';

import '../models/city_entity.dart';
import '../nav.dart';
import '../utils/widgets.dart';

class FeaturedCitiesList extends StatelessWidget {
  final List<CityEntity> cities;

  FeaturedCitiesList(this.cities);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Wrap(
        children: cities.map((item) => FeaturedCityItem(item)).toList(),
      ),
    );
  }
}

class FeaturedCityItem extends StatelessWidget {
  final CityEntity city;

  FeaturedCityItem(this.city);

  @override
  Widget build(BuildContext context) {
    double _width = MediaQuery.of(context).size.width;
    double width = (_width - 130) / (_width > 700 ? 6 : 3);
    double imageWidth = width - 20;
    return GestureDetector(
      onTap: () {
        Nav.societyList(context);
      },
      child: Padding(
        padding: const EdgeInsets.all(5.0),
        child: TileCard(
          elevation: 2,
          child: Container(
            width: width,
            child: Column(
              children: <Widget>[
                Container(
                  height: imageWidth,
                  width: imageWidth,
                  child: NetworkImageWithPlaceholder(
                    city.image,
                    height: imageWidth,
                    width: imageWidth,
                    circular: true,
                    text: city.name,
                  ),
                ),
                SizedBox(height: 5.0),
                Center(
                  child: Text(
                    city.name,
                    maxLines: 2,
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
