import 'package:flutter/material.dart';
import 'package:property_dealers_directory/widgets/filters_property.dart';

class TestFilters extends StatefulWidget {
  const TestFilters({Key? key}) : super(key: key);

  @override
  State<TestFilters> createState() => _TestFiltersState();
}

class _TestFiltersState extends State<TestFilters> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(130),
        child: AppBar(
          title: Text("Filters"),
          flexibleSpace: Padding(
            padding: const EdgeInsets.only(top: 70.0),
            child: Column(
              children: [
                // FilterProperty(),
              ],
            ),
          ),
        ),
      ),

    );
  }
}
