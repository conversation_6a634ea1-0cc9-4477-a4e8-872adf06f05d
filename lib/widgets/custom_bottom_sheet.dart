import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';

import '../controllers/city_selected.dart';
import '../data/api.dart';
import '../models/block_entity.dart';
import '../models/city_entity.dart';
import '../nav.dart';
import '../pages/properties_list/custom_expansion_tile.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class CustomBottomSheet extends StatefulWidget {
  final controller;
  final Function? callBack;

  CustomBottomSheet({Key? key, this.controller, this.callBack}) : super(key: key);

  @override
  _CustomBottomSheetState createState() => _CustomBottomSheetState();
}

class _CustomBottomSheetState extends State<CustomBottomSheet> {
  TextEditingController queryC = TextEditingController();
  GlobalKey<CustomExpansionTileState> _key = GlobalKey();

  int? cityId;
  String query = '';
  bool isSearch = true;
  bool isLoading = false;
  bool showBlockSection = false;
  CityEntity? currentSociety;
  BlockEntity? currentBlock;

  List<CityEntity?> cities = [];
  List<CityEntity?>? societies = [];
  List<BlockEntity?> blocks = [];

  @override
  void initState() {
    getCities();
    super.initState();
  }

  getCities() async {
    APIResponse<List<CityEntity>> result = await API.getCitiesList();
    if (result.status!.status == ApiStatus.OK) {
      CityEntity all = CityEntity();
      all.id = 0;
      all.name = 'All';
      result.data?.forEach((e) {
        e.societies?.insert(0, all);
      });
      setState(() {
        cities = result.data!;
        cityId = cities.firstWhere((e) => e?.id == obProvider.city.id)?.id;
        if (widget.controller.cityId == null) widget.controller.cityId = cityId;
        if (widget.controller.cityId != null)
          societies = cities.firstWhere((e) => e?.id == widget.controller.cityId)?.societies;
        else
          societies = cities.firstWhere((e) => e?.id == obProvider.city.id)?.societies;
        if (widget.controller.societyId != null) {
          getBlocks(cityId: widget.controller.cityId, societyId: widget.controller.societyId);
          showBlockSection = true;
        }
      });
    }
  }

  getBlocks({cityId = 0, societyId = 0}) {
    API.blocksList(societyId, cityId: cityId, category: "", inActive: "&inactive=1").then((value) {
      if (value.status!.status == ApiStatus.OK) {
        setState(() {
          blocks = value.data!;
          BlockEntity all = BlockEntity();
          all.id = 0;
          all.name = 'All';
          all.type = 'All';
          blocks.insert(0, all);
          isLoading = false;
        });
      } else {
        setState(() {
          isLoading = false;
        });
      }
    });
  }

  void searchPressed() {
    query = queryC.text.toString().toLowerCase();
    query = query.trim();
    if (isSearch)
      setState(() {
        if (query.isEmpty) {
          societies = cities.firstWhere((e) => e?.id == (cityId != null ? cityId : obProvider.city.id))?.societies;
        } else {
          var list = societies;
          societies = list?.where((text) => text!.name.toLowerCase().contains(query)).toList();
        }
      });
    if (blocks.isNotEmpty || currentSociety != null)
      setState(() {
        if (query.isEmpty) {
          getBlocks(societyId: currentSociety?.id);
        } else {
          blocks = blocks.where((text) => text!.name!.toLowerCase().contains(query)).toList();
        }
      });
  }

  @override
  Widget build(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    double screenHeight = MediaQuery.of(context).size.height;
    return Stack(
      children: [
        Container(
            height: screenHeight * 0.9,
            child: Padding(
              padding: const EdgeInsets.all(20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: 20),
                  Heading.big('Cities', font: 20),
                  SizedBox(height: 10),
                  Container(
                    height: screenHeight * .12,
                    width: screenWidth * .9,
                    child: ListView.builder(
                        shrinkWrap: true,
                        scrollDirection: Axis.horizontal,
                        itemCount: cities.length,
                        itemBuilder: (BuildContext context, i) {
                          CityEntity cityObj = cities[i]!;
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  showBlockSection = false;
                                  isLoading = false;
                                  cityId = cityObj.id;
                                  widget.controller.cityId = cityObj.id;
                                  currentSociety = null;
                                  societies = cities.firstWhere((e) => e?.id == cityId)?.societies;
                                });
                              },
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    decoration: BoxDecoration(
                                      borderRadius: BorderRadius.all(Radius.circular(30)),
                                      color: (widget.controller.cityId == null ? cityId : widget.controller.cityId) ==
                                              cityObj.id
                                          ? Colors.green
                                          : Colors.transparent,
                                    ),
                                    child: Padding(
                                      padding: const EdgeInsets.all(2.0),
                                      child: NetworkImageWithPlaceholder(
                                        cityObj.image,
                                        height: 52.0,
                                        width: 52.0,
                                        circular: true,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    cityObj.name,
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: (widget.controller.cityId == null ? cityId : widget.controller.cityId) ==
                                              cityObj.id
                                          ? Colors.green
                                          : Theme.of(context).primaryColor,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        }),
                  ),
                  SizedBox(height: 10),
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 5.0),
                    height: 42.0,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.grey[400]!,
                          width: 0.8,
                        ),
                        borderRadius: BorderRadius.circular(5.0)),
                    child: Row(
                      children: [
                        Expanded(
                          child: FormBuilderTextField(
                            textInputAction: TextInputAction.search,
                            name: 'query',
                            controller: queryC,
                            decoration: InputDecoration(hintText: 'Search society or block', border: InputBorder.none),
                            onChanged: (_) {
                              setState(() {
                                searchPressed();
                              });
                            },
                          ),
                        ),
                        InkWell(
                          splashColor: Colors.transparent,
                          highlightColor: Colors.transparent,
                          onTap: searchPressed,
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 8.0, horizontal: 8.0),
                            decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor, borderRadius: BorderRadius.circular(5.0)),
                            child: Icon(Icons.search, size: 18.0, color: Colors.white),
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(height: 10),
                  Expanded(
                    child: Scrollbar(
                      thumbVisibility: blocks.isEmpty ? false : true,
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Heading.big('Societies', font: 20),
                            societies!.isNotEmpty
                                ? CustomExpansionTile(
                                    initiallyExpanded: true,
                                    onExpansionChanged: (v) {
                                      if (v) {
                                        setState(() {
                                          isSearch = true;
                                          societies = cities
                                              .firstWhere(
                                                  (e) => e?.id == (cityId != null ? cityId : obProvider.city.id))
                                              ?.societies;
                                        });
                                      }
                                    },
                                    key: _key,
                                    textColor: Colors.green,
                                    iconColor: Colors.green,
                                    title:
                                        Text((currentSociety?.name != null ? currentSociety?.name : 'Select Society')!),
                                    children: [
                                      ListView.builder(
                                          physics: NeverScrollableScrollPhysics(),
                                          shrinkWrap: true,
                                          itemCount: societies!.length,
                                          itemBuilder: (BuildContext context, i) {
                                            CityEntity society = societies![i]!;
                                            return GestureDetector(
                                              onTap: () {
                                                setState(() {
                                                  isSearch = false;
                                                  showBlockSection = true;
                                                  isLoading = true;
                                                  blocks = [];
                                                  currentSociety = society;
                                                  widget.controller.societyId = society.id;
                                                  if (widget.controller.societyId != null)
                                                    getBlocks(
                                                        cityId: widget.controller.cityId,
                                                        societyId: widget.controller.societyId);
                                                  else
                                                    getBlocks(cityId: cityId, societyId: currentSociety?.id);
                                                  _key.currentState!.toggle();
                                                });
                                              },
                                              child: Container(
                                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 7.5),
                                                child: Text(
                                                  society.name,
                                                  style: TextStyle(
                                                    fontSize: 18,
                                                    color: (widget.controller.societyId != null
                                                                ? widget.controller.societyId
                                                                : currentSociety?.id) ==
                                                            society.id
                                                        ? Colors.green
                                                        : Colors.black,
                                                  ),
                                                ),
                                              ),
                                            );
                                          })
                                    ],
                                  )
                                : Center(
                                    child: Padding(
                                      padding: const EdgeInsets.all(20),
                                      child: Text('Society not found.'),
                                    ),
                                  ),
                            SizedBox(height: 10),
                            showBlockSection
                                ? Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Heading.big('Blocks', font: 20),
                                      isLoading
                                          ? Center(child: FullLoader())
                                          : blocks.isNotEmpty
                                              ? ListView.builder(
                                                  physics: NeverScrollableScrollPhysics(),
                                                  shrinkWrap: true,
                                                  itemCount: blocks.length,
                                                  itemBuilder: (BuildContext context, i) {
                                                    BlockEntity block = blocks[i]!;
                                                    return GestureDetector(
                                                      onTap: () {
                                                        setState(() {
                                                          currentBlock = block;
                                                          widget.controller.blockId = block.id;
                                                        });
                                                      },
                                                      child: Container(
                                                        padding:
                                                            const EdgeInsets.symmetric(horizontal: 10, vertical: 7.5),
                                                        child: Text(
                                                          block.name!,
                                                          style: TextStyle(
                                                              fontSize: 18,
                                                              color: (widget.controller.blockId != null
                                                                          ? widget.controller.blockId
                                                                          : currentBlock?.id) ==
                                                                      block.id
                                                                  ? Colors.green
                                                                  : Colors.black),
                                                        ),
                                                      ),
                                                    );
                                                  },
                                                )
                                              : Center(
                                                  child: Padding(
                                                    padding: const EdgeInsets.all(20),
                                                    child: Text('Block not found.'),
                                                  ),
                                                )
                                    ],
                                  )
                                : Container(),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            )),
        Positioned(
          right: 10,
          bottom: 10,
          child: PrimaryButton(
            text: 'Apply Filter',
            onPressed: () {
              widget.controller.locationFilter = true;
              Nav.back(context);
              widget.callBack!();
            },
          ),
        )
      ],
    );
  }
}
