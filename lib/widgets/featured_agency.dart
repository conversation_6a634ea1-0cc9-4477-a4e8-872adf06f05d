import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';

import '../models/agency_short_entity.dart';
import '../nav.dart';
import '../pages/home/<USER>';
import '../utils/functions.dart';
import '../utils/height_fraction_page.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';

class FeaturedAgencyList extends StatelessWidget {
  final List<AgencyShortEntity?>? agencies;

  FeaturedAgencyList(this.agencies);

  @override
  Widget build(BuildContext context) {
    agencies!.shuffle();
    final double w = MediaQuery.of(context).size.width;
    final double fraction = w < 600 ? 1 / 5.8 : 0.08;
    return Column(
      children: <Widget>[
        HomeHeadingWidget(
          title: 'Titanium Agencies',
          onPress: (){
            Nav.agencyList(context);
            sendAnalyticsEvent(eventName: 'home_agency_all_button_click');
          },
        ),
        CarouselSlider(
          options: CarouselOptions(
            height: 85,
            viewportFraction: fraction,
            autoPlay: true,
            autoPlayInterval: Duration(seconds: 3),
            autoPlayAnimationDuration: Duration(milliseconds: 800),
            autoPlayCurve: Curves.fastOutSlowIn,
            enlargeCenterPage: false,
            scrollDirection: Axis.horizontal,
          ),
          items: agencies!.map((i) {
            return Builder(
              builder: (BuildContext context) {
                return FeaturedAgencyItem(i);
              },
            );
          }).toList(),
        ),
      ],
    );
  }
}

class FeaturedAgencyItem extends StatelessWidget {
  final AgencyShortEntity? agency;

  FeaturedAgencyItem(this.agency);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        sendAnalyticsEvent(eventName: 'home_agency_item_click', parameters: {'agency_Id': agency!.id});
        Nav.agency(context, agency!.id);
      },
      child: Padding(
        padding: const EdgeInsets.only(top: 6),
        child: Container(
          width: 50,
          child: Column(
            children: <Widget>[
              Container(
                height: 50,
                child: NetworkImageWithPlaceholder(agency!.image, height: 115.0, width: 140.0, circular: true),
              ),
              Padding(
                padding: const EdgeInsets.only(top: 2),
                child: Text(agency!.name, maxLines: 2, textAlign: TextAlign.center, style: TextStyle(fontSize: 10)),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
