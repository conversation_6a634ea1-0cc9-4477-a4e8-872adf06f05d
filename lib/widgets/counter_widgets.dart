import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../models/classified_short_entity.dart';
import '../models/property_entity.dart';

class ImpressionWidget extends StatelessWidget {
  const ImpressionWidget({Key? key, required this.property}) : super(key: key);

  final property;

  @override
  Widget build(BuildContext context) {
    return LimitedBox(
      maxWidth: MediaQuery.of(context).size.width * 0.12,
      child: FittedBox(
        child: Row(
          children: [
            Icon(Icons.remove_red_eye, size: 15, color: Theme.of(context).primaryColor),
            SizedBox(width: 2),
            Text('${property.impressions}', style: TextStyle(fontSize: 15.0)),
            SizedBox(width: 5),
          ],
        ),
      ),
    );
  }
}

class ViewWidget extends StatelessWidget {
  const ViewWidget({Key? key, required this.property}) : super(key: key);

  final ClassifiedShortEntity property;

  @override
  Widget build(BuildContext context) {
    return LimitedBox(
      maxWidth: MediaQuery.of(context).size.width * 0.12,
      child: FittedBox(
        child: Row(
          children: [
            Icon(FontAwesomeIcons.solidHandPointUp, size: 15, color: Theme.of(context).primaryColor),
            SizedBox(width: 2),
            Text('${property.views}', style: TextStyle(fontSize: 15.0)),
            SizedBox(width: 5),
          ],
        ),
      ),
    );
  }
}

class ClickWidget extends StatelessWidget {
  const ClickWidget({Key? key, required this.property}) : super(key: key);

  final property;

  @override
  Widget build(BuildContext context) {
    return LimitedBox(
      maxWidth: MediaQuery.of(context).size.width * 0.12,
      child: FittedBox(
        child: Row(
          children: [
            Icon(FontAwesomeIcons.arrowPointer, size: 15, color: Theme.of(context).primaryColor),
            SizedBox(width: 2),
            Text('${property.clicks}', style: TextStyle(fontSize: 15.0)),
            SizedBox(width: 5),
          ],
        ),
      ),
    );
  }
}

class PhoneClicks extends StatelessWidget {
  const PhoneClicks({Key? key, required this.property}) : super(key: key);

  final PropertyEntity property;

  @override
  Widget build(BuildContext context) {
    return LimitedBox(
      maxWidth: MediaQuery.of(context).size.width * 0.12,
      child: FittedBox(
        child: Row(
          children: [
            Icon(Icons.phone, size: 15, color: Theme.of(context).primaryColor),
            SizedBox(width: 2),
            Text('${property.phoneClicks}', style: TextStyle(fontSize: 15.0)),
            SizedBox(width: 5),
          ],
        ),
      ),
    );
  }
}

class WhatsAppClicks extends StatelessWidget {
  const WhatsAppClicks({Key? key, required this.property}) : super(key: key);

  final PropertyEntity property;

  @override
  Widget build(BuildContext context) {
    return LimitedBox(
      maxWidth: MediaQuery.of(context).size.width * 0.12,
      child: FittedBox(
        child: Row(
          children: [
            Icon(FontAwesomeIcons.whatsapp, size: 15, color: Theme.of(context).primaryColor),
            SizedBox(width: 2),
            Text('${property.whatsappClicks}', style: TextStyle(fontSize: 15.0)),
            SizedBox(width: 5),
          ],
        ),
      ),
    );
  }
}
