import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:property_dealers_directory/theme.dart';

import '../utils/helpers.dart';

class SocialIcon extends StatelessWidget {
  final String? text;
  IconData? icon;
  Color? color;
  int? type;
  Function? onPressed;

  static const int OTHER = -1;
  static const int PHONE = 0;
  static const int FACEBOOK = 1;
  static const int TWITTER = 2;
  static const int INSTAGRAM = 3;
  static const int YOUTUBE = 4;
  static const int EMAIL = 5;
  static const int WHATSAPP = 6;
  static const int MOBILE = 7;
  static const int NAME = 8;
  static const int ADDRESS = 9;
  static const int FAX = 10;
  static const int WEB = 11;

  SocialIcon(this.text, this.icon, this.color);

  SocialIcon.type(this.text, this.type, {Function? onPressed}) {
    this.onPressed = onPressed;
    switch (this.type) {
      case PHONE:
        this.icon = FontAwesomeIcons.phone;
        this.color = MyColors.primary;
        break;
      case FACEBOOK:
        this.icon = FontAwesomeIcons.facebookF;
        this.color = Color(0xff3b5998);
        break;
      case TWITTER:
        this.icon = FontAwesomeIcons.twitter;
        this.color = Color(0xff1da1f2);
        break;
      case INSTAGRAM:
        this.icon = FontAwesomeIcons.instagram;
        this.color = Color(0xffc32aa3);
        break;
      case YOUTUBE:
        this.icon = FontAwesomeIcons.youtube;
        this.color = Color(0xffff0000);
        break;
      case EMAIL:
        this.icon = FontAwesomeIcons.envelope;
        this.color = Color(0xffC6C6C6);
        break;
      case WHATSAPP:
        this.icon = FontAwesomeIcons.whatsapp;
        this.color = Color(0xff25d366);
        break;
      case NAME:
        this.icon = FontAwesomeIcons.user;
        this.color = Color(0xff25d366);
        break;
      case ADDRESS:
        this.icon = FontAwesomeIcons.addressCard;
        this.color = Color(0xff25d366);
        break;
      case MOBILE:
        this.icon = FontAwesomeIcons.mobile;
        this.color = Colors.blue;
        break;
      case FAX:
        this.icon = FontAwesomeIcons.fax;
        this.color = Color(0xff0088cc);
        break;
      case WEB:
        this.icon = FontAwesomeIcons.globe;
        this.color = Color(0xff0088cc);
        break;

      default:
        this.icon = FontAwesomeIcons.question;
        this.color = Colors.blue;
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onPressed as void Function()? ?? onTap,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(6.0),
          color: color,
        ),
        child: Padding(
          padding: const EdgeInsets.all(6.0),
          child: Icon(
            icon,
            size: 25.0,
            color: Colors.white,
          ),
        ),
      ),
    );
  }

  void onTap() {
    String url = '';
    switch (this.type) {
      case PHONE:
      case MOBILE:
        url = 'tel:+92${text!.substring(1)}';
        break;
      case FACEBOOK:
        url = 'https://m.facebook.com/$text';
        break;
      case TWITTER:
        url = 'http://www.twitter.com/$text';
        break;
      case INSTAGRAM:
        url = 'https://instagram.com/$text';
        break;
      case YOUTUBE:
        url = 'https://www.youtube.com/$text';
        break;
      case WHATSAPP:
        url = 'https://wa.me/92${text!.substring(1)}';
        break;
      case EMAIL:
        url = 'mailto:$text';
        break;
      case WEB:
        url = text!;
        break;
    }
    openLink(url);
  }
}
