import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../pages/classified_property/filters_provider.dart';

class BadgeIcon extends StatelessWidget {
  final IconData? iconData;
  final String text;
  final VoidCallback? onTap;
  final int notificationCount;

  const BadgeIcon({
    Key? key,
    this.onTap,
    required this.text,
    this.iconData,
    this.notificationCount = 0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    FiltersProvider _provider = context.watch<FiltersProvider>();
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 72,
        height: 45,
        padding: const EdgeInsets.symmetric(horizontal: 15),
        child: Stack(
          alignment: Alignment.center,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Icon(iconData, size: 20,),
                Text(text, overflow: TextOverflow.ellipsis, style: TextStyle(fontSize: 12),),
              ],
            ),
            _provider.filterCounter == 0 ? Container():
            Positioned(
              top: -5,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 3, vertical: 5),
                decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.red),
                alignment: Alignment.center,
                child: Text('$notificationCount', style: TextStyle(fontSize: 11),),
              ),
            ),
          ],
        ),
      ),
    );
  }
}