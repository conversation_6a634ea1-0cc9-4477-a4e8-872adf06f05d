import 'package:awesome_dialog/awesome_dialog.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_validators/form_builder_validators.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:property_dealers_directory/controllers/user_plan_controller.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/pages/invite_user.dart';
import 'package:property_dealers_directory/widgets/filters_property.dart';
import 'package:property_dealers_directory/widgets/notification_popup.dart';
import 'package:property_dealers_directory/widgets/test.dart';
import 'package:provider/provider.dart';
import 'package:rate_my_app/rate_my_app.dart';
import 'package:share/share.dart';

import '../common/main_textField.dart';
import '../config.dart';
import '../controllers/feedback_controller.dart';
import '../controllers/login_controller.dart';
import '../data/api.dart';
import '../models/user_plan_entity.dart';
import '../nav.dart';
import '../services/remote_config.dart';
import '../utils/Notify.dart';
import '../utils/functions.dart';
import '../utils/helpers.dart';
import '../utils/text.dart';
import '../utils/widgets.dart';
import '../widgets/property_selection_dialog.dart';

class DrawerNavigation extends StatefulWidget {
  @override
  _DrawerNavigationState createState() => _DrawerNavigationState();
}

class _DrawerNavigationState extends State<DrawerNavigation> {
  final LoginController controller = Get.find();
  final GlobalKey<FormBuilderState> _fbKey = GlobalKey<FormBuilderState>();
  GetStorage storage = GetStorage();
  final LoginController login = Get.find();
  final UserPlanController userPlan = Get.find();


  UserPlanEntity? plan;
  var isPlanLoaded = false;

  int click = 0;
  String showDialogDateTime = DateTime.now().toString();



  @override
  void initState() {
  userPlan.refresh();
    // if (controller.isLoggedIn)
      // API.getUserPlan().then((value) {
      //   setState(() {
      //     plan = value.data;
      //     isPlanLoaded = true;
      //   });
      // });
    setState((){
      click = storage.read('click') ?? 0;
    });
    super.initState();
  }

  showFeedbackDialog() {
    FeedbackController feedback = Get.find();
    if(feedback.canShowFeedback){
      // _showDialog(context);
      feedback.showDialog();
    }
  }

  @override
  Widget build(BuildContext context) {
    Color iconClr = Theme.of(context).primaryColor;
    TextStyle style = TextStyle(color: Theme.of(context).primaryColor, fontSize: 16.0);
    return Drawer(
      child: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            controller.user != null
                ? Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                        child: Row(
                          children: [
                            Container(
                              height: 100,
                              width: 100,
                              child: NetworkImageWithPlaceholder(controller.user!.staff!.imageUrl, circular: true),
                            ),
                            SizedBox(width: 10),
                            LimitedBox(
                              maxWidth: 170,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(controller.user!.staff!.name, style: style.copyWith(fontSize: 22)),
                                  SizedBox(height: 5),
                                  Text(controller.user!.agency!.name, style: TextStyle(color: iconClr, fontSize: 18)),
                                  SizedBox(height: 5),
                                  Row(
                                    children: [
                                      Text('Package ', style: TextStyle(color: iconClr, fontSize: 16)),
                                      controller.isLoggedIn
                                          ? Container(
                                              height: 20,
                                              width: 60,
                                              decoration: BoxDecoration(
                                                  color: !userPlan.isValid ? Colors.red : Colors.green,
                                                  borderRadius: BorderRadius.circular(10)),
                                              child: Center(
                                                child: Text(!userPlan.isValid ? 'Expired' : 'Active',
                                                    style: TextStyle(color: Colors.white)),
                                              ),
                                            )
                                          : Container(
                                              height: 5,
                                              width: 50,
                                              child: LinearProgressIndicator(
                                                color: Theme.of(context).primaryColor,
                                                backgroundColor: Colors.grey[200],
                                              ),
                                            )
                                    ],
                                  ),
                                ],
                              ),
                            )
                          ],
                        ),
                      ),
                      Divider(color: iconClr, thickness: 2)
                    ],
                  )
                : Column(
                    children: [
                      SizedBox(height: 10),
                      Container(
                        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(20)),
                        child: Image.asset('images/logo.png', height: 120),
                      ),
                      SizedBox(height: 10),
                      Divider(color: iconClr, thickness: 2)
                    ],
                  ),
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    if (controller.isLoggedIn)
                      Padding(
                        padding: const EdgeInsets.only(left: 20),
                        child: Column(
                          children: [
                            if (controller.isLoggedIn)
                              GestureDetector(
                                onTap: () {
                                  Nav.back(context);
                                  Nav.updateProfile(context);
                                  sendAnalyticsEvent(eventName: 'drawer_my_profile_click');
                                },
                                child: ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  leading: Icon(Icons.person, color: iconClr),
                                  title: Text('My Profile', style: style),
                                ),
                              ),
                            if (controller.isLoggedIn)
                              GestureDetector(
                                onTap: () {
                                  Nav.back(context);
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return PropertySelectionDialog(
                                        fTap: () {
                                          sendAnalyticsEvent(eventName: 'drawer_my_inventory_ad_click');
                                          Nav.back(context);
                                          Nav.propertiesPage(context, id: controller.staff!.id);
                                        },
                                        sTap: () {
                                          sendAnalyticsEvent(eventName: 'drawer_my_classified_ad_click');
                                          Nav.back(context);
                                          Nav.classifiedPropertyListPage(context, staffId: controller.staff!.id);
                                        },
                                      );
                                    },
                                  );
                                },
                                child: ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  leading: Icon(Icons.list, color: iconClr),
                                  title: Text('My Ads', style: style),
                                ),
                              ),
                            if (controller.isLoggedIn)
                              GestureDetector(
                                onTap: () {
                                  Nav.back(context);
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return PropertySelectionDialog(
                                        fTap: () {
                                          sendAnalyticsEvent(eventName: 'drawer_inventory_post_click');
                                          Nav.back(context);
                                          Nav.addPropertyPage(context);
                                        },
                                        sTap: () {
                                          sendAnalyticsEvent(eventName: 'drawer_classified_post_click');
                                          Nav.back(context);
                                          Nav.addClassifiedProperty(context);
                                        },
                                      );
                                    },
                                  );
                                },
                                child: ListTile(
                                  contentPadding: EdgeInsets.zero,
                                  leading: Icon(Icons.post_add, color: iconClr),
                                  title: Text('Post Ad', style: style),
                                ),
                              ),
                            if (controller.isLoggedIn)
                            GestureDetector(
                              onTap: (){
                                Nav.back(context);
                                InviteUser.openDialog(context);
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.send, color: iconClr),
                                title: Text('Invite User', style: style),
                                subtitle: Text('Create instant account')
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (controller.user != null) Divider(color: iconClr, thickness: 1),
                    Padding(
                      padding: const EdgeInsets.only(left: 20),
                      child: Column(
                        children: [
                          if(RemoteConfigService.enableMarketInventory)
                            GestureDetector(
                              onTap: () {
                                Nav.back(context);
                               Nav.marketInventoryList(context);
                               //  Navigator.push(context, MaterialPageRoute(builder: (context) => MarketInventoryListPage2()));
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.list, color: iconClr),
                                title: Text('Market Inventory', style: style),
                              ),
                            ),
                          GestureDetector(
                            onTap: () {
                              sendAnalyticsEvent(eventName: 'drawer_favourite_ads_click');
                              Nav.back(context);
                              Nav.favPropertyListPage(context);
                            },
                            child: ListTile(
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(Icons.favorite, color: iconClr),
                              title: Text('Favourite Ads', style: style),
                            ),
                          ),
                          // GestureDetector(
                          //   onTap: () {
                          // //     // showDialog(
                          // //     //     context: context,
                          // //     //     builder: (BuildContext context) {
                          // //     //       return Dialog(
                          // //     //         shape: RoundedRectangleBorder(
                          // //     //             borderRadius:
                          // //     //             BorderRadius.circular(15.0)), //this right here
                          // //     //         child: Container(
                          // //     //           height: 400,
                          // //     //           child: Stack(
                          // //     //             children: [
                          // //     //               Column(
                          // //     //                 crossAxisAlignment: CrossAxisAlignment.start,
                          // //     //                 children: [
                          // //     //                   Container(
                          // //     //                     height: 200,
                          // //     //                     decoration: BoxDecoration(
                          // //     //                       color: Colors.red,
                          // //     //                       borderRadius: BorderRadius.only(
                          // //     //                         topLeft: Radius.circular(15.0),
                          // //     //                         topRight: Radius.circular(15.0),
                          // //     //                       )
                          // //     //                     ),
                          // //     //                   ),
                          // //     //                   SizedBox(height: 10),
                          // //     //                   Padding(
                          // //     //                     padding: const EdgeInsets.all(10.0),
                          // //     //                     child: Heading(
                          // //     //                       'Update Bahria Plus',
                          // //     //                       font: 22,
                          // //     //                       color: Colors.black,
                          // //     //                       weight: FontWeight.bold,
                          // //     //                     ),
                          // //     //                   ),
                          // //     //                   // SizedBox(height: 10),
                          // //     //                   Padding(
                          // //     //                     padding: const EdgeInsets.all(10.0),
                          // //     //                     child: Text('Update Bahria Plus Update Bahria Plus Update Bahria Plus Update Bahria Plus Update Bahria Plus Update Bahria Plus Update Bahria Plus Update Bahria Plus'),
                          // //     //                   ),
                          // //     //                   Align(
                          // //     //                     alignment: Alignment.bottomRight,
                          // //     //                       child: TextButton(onPressed: (){}, child: Text("View"))),
                          // //     //                 ],
                          // //     //               ),
                          // //     //               Positioned(
                          // //     //                 right: 10,
                          // //     //                   top: 10,
                          // //     //                   child:  Icon(Icons.cancel, color: Colors.grey,),
                          // //     //               )
                          // //     //             ],
                          // //     //           ),
                          // //     //         )
                          // //     //       );
                          // //     //     });
                          //     Get.dialog(
                          //         NotificationPopup(
                          //           context: context,
                          //           message: {
                          //             "body": "Update the BahriaPlus app to get new features and enhancements.",
                          //             "title": "New version is available",
                          //             "type": "popup",
                          //             "image": "https://images.mirror-media.xyz/nft/twWxVXl-HPMLzJ4QceJXD.png",
                          //             "action_title": "Call",
                          //             "action_type": "url",
                          //             "action_value": "tel:+923344634444"
                          //           },
                          //         ),
                          //       barrierDismissible: false,
                          //     );
                          //   },
                          //   child: ListTile(
                          //     contentPadding: EdgeInsets.zero,
                          //     leading: Icon(Icons.favorite, color: iconClr),
                          //     title: Text('Notification Popup', style: style),
                          //   ),
                          // ),
                          if (RemoteConfigService.rateListVisibility)
                            GestureDetector(
                              onTap: () {
                                sendAnalyticsEvent(eventName: 'drawer_rates_list_click');
                                Nav.back(context);
                                Nav.societyList(context);
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.list_alt, color: iconClr),
                                title: Text('Rates List', style: style),
                              ),
                            ),

                          if (!controller.isLoggedIn)
                            GestureDetector(
                              onTap: () {
                                Nav.back(context);
                                Nav.submitAgency(context);
                                sendAnalyticsEvent(eventName: 'drawer_submit_agency_click');
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.real_estate_agent, color: iconClr),
                                title: Text('Submit Your Agency', style: style),
                              ),
                            ),
                          if (RemoteConfigService.isHowToEnabled)
                            GestureDetector(
                              onTap: () {
                                Nav.videoListPage(context);
                                sendAnalyticsEvent(eventName: 'drawer_how_to_click');
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.ondemand_video, color: iconClr),
                                title: Text('How to', style: style),
                              ),
                            ),
                          GestureDetector(
                            onTap: () {
                              Nav.back(context);
                              Nav.settingsPage(context);
                              sendAnalyticsEvent(eventName: 'drawer_settings_page_click');
                            },
                            child: ListTile(
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(Icons.settings, color: iconClr),
                              title: Text('Settings', style: style),
                            ),
                          ),
                          if (!kIsWeb)
                            GestureDetector(
                              onTap: () {
                                Nav.back(context);
                                openLink('tel:${RemoteConfigService.contactPhone}');
                                sendAnalyticsEvent(eventName: 'drawer_call_us_click');
                                  showFeedbackDialog();
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.phone, color: iconClr),
                                title: Text('Call Us for help', style: style),
                              ),
                            ),
                          if (!kIsWeb)
                            GestureDetector(
                              onTap: () {
                                Nav.back(context);
                                RateMyApp rateMyApp = RateMyApp(
                                  googlePlayIdentifier: Config.PLAY_STORE_PACKAGE,
                                  appStoreIdentifier: Config.APPLE_ID,
                                );
                                rateMyApp.init().then((_) {
                                  rateMyApp.showRateDialog(context);
                                });
                                sendAnalyticsEvent(eventName: 'drawer_rate_our_app_click');
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.star_rate, color: iconClr),
                                title: Text('Rate our App', style: style),
                              ),
                            ),
                          if (!kIsWeb)
                            GestureDetector(
                              onTap: () {
                                Nav.back(context);
                                Share.share(Config.SHARE_TEXT);
                                sendAnalyticsEvent(eventName: 'drawer_share_app_click');
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.share, color: iconClr),
                                title: Text('Share App', style: style),
                              ),
                            ),
                          GestureDetector(
                            onTap: () {
                              Nav.back(context);
                              Nav.about(context);
                              sendAnalyticsEvent(eventName: 'drawer_about_click');
                            },
                            child: ListTile(
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(Icons.info, color: iconClr),
                              title: Text('About', style: style),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              // _showDialog(context);
                              Nav.feedbackPage(context);

                            },
                            child: ListTile(
                              contentPadding: EdgeInsets.zero,
                              leading: Icon(Icons.note_add, color: iconClr),
                              title: Text('Feedback', style: style),
                            ),
                          ),
                          if (controller.isLoggedIn)
                            GestureDetector(
                              onTap: () {
                                Nav.back(context);
                                controller.logout();
                                sendAnalyticsEvent(eventName: 'drawer_logout_click');
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.logout, color: iconClr),
                                title: Text('Logout', style: style),
                              ),
                            ),
                          if (!controller.isLoggedIn)
                            GestureDetector(
                              onTap: () {
                                Nav.back(context);
                                Nav.loginPage(context);
                                sendAnalyticsEvent(eventName: 'drawer_login_click');
                              },
                              child: ListTile(
                                contentPadding: EdgeInsets.zero,
                                leading: Icon(Icons.login, color: iconClr),
                                title: Text('Login', style: style),
                              ),
                            ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
 void _showDialog(context) {
    showDialog<void>(
      context: context,
      // barrierDismissible: true, // false = user must tap button, true = tap outside dialog
      builder: (BuildContext dialogContext) {
        return SimpleDialog(
          title: Text('Feedback'),
          children: [
            Container(
              height: MediaQuery.of(context).size.height * .7,
              child: SingleChildScrollView(
                child: FormBuilder(
                  key: _fbKey,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 20),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Name")),
                        FormBuilderTextField(
                          name: 'name',
                          keyboardType: TextInputType.name,
                          decoration: InputStyle(hint: "Enter name", icon: Icons.person),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Name is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Mobile #")),
                        FormBuilderTextField(
                          name: 'phone',
                          keyboardType: TextInputType.number,
                          decoration: InputStyle(hint: "Enter mobile", icon: Icons.phone),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Mobile is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Current City")),
                        FormBuilderTextField(
                          name: 'city',
                          keyboardType: TextInputType.text,
                          decoration: InputStyle(hint: "Enter current city", icon: Icons.location_city),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'City is required'),
                          ]),
                        ),
                        const Padding(padding: EdgeInsets.only(top: 10, bottom: 5), child: Heading.input("Feedback")),
                        FormBuilderTextField(
                          name: 'feedback',
                          maxLines: 4,
                          keyboardType: TextInputType.text,
                          decoration: InputStyle(hint: "Enter message", icon: Icons.message),
                          autovalidateMode: AutovalidateMode.onUserInteraction,
                          validator: FormBuilderValidators.compose([
                            FormBuilderValidators.required(errorText: 'Message is required'),
                          ]),
                        ),
                        const SizedBox(height: 20),
                        Center(child: PrimaryButton(text: "Submit", onPressed: () async{
                          if (_fbKey.currentState!.saveAndValidate()) {
                            Notify.progress(context, 'Please wait...');
                            APIResponse<dynamic> result = await API.feedbackFormSubmit(_fbKey.currentState!.value);
                            if (result.isOK) {
                              print(result);
                              Notify.hide();
                              Notify.toast('Request Submit Successfully', type: 1);
                              Nav.back(context);
                            } else {
                              Notify.hide();
                              Notify.customDialog(context, desc: result.status!.message, type: DialogType.error);
                            }
                          }
                        })),
                        Align(
                          alignment: Alignment.bottomRight,
                          child: GestureDetector(
                              onTap: (){
                                Nav.back(context);

                              },
                              child: Text("Skip")),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}
