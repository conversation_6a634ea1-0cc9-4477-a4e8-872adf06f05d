import 'package:flutter/material.dart';
import 'package:flutter_progress_hud/flutter_progress_hud.dart';
import 'package:get/get.dart';
import 'package:property_dealers_directory/models/classified_entity.dart';
import 'package:property_dealers_directory/models/market_inventory_entity.dart';
import 'package:property_dealers_directory/pages/classified_property/calssified_select_filter.dart';
import 'package:property_dealers_directory/pages/classified_property/filters_provider.dart';
import 'package:property_dealers_directory/pages/feedback.dart';
import 'package:property_dealers_directory/pages/maps_v2/MapProvider.dart';
import 'package:property_dealers_directory/pages/market_inventory/add_market_inventory.dart';
import 'package:property_dealers_directory/pages/market_inventory/market_inventory_detail_page.dart';
import 'package:property_dealers_directory/pages/market_inventory/market_inventory_list_page.dart';
import 'package:property_dealers_directory/pages/onboarding/user_location_selection.dart';
import 'package:property_dealers_directory/providers/LocationSelectionProvider.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/utils/helpers.dart';
import 'package:provider/provider.dart';

import 'controllers/explore_controller.dart';
import 'controllers/login_controller.dart';
import 'models/explore_short_entity.dart';
import 'models/market_inventory_long_entity.dart';
import 'models/project_entity.dart';
import 'models/video_entity.dart';
import 'pages/about_page.dart';
import 'pages/add_property_page_v2.dart';
import 'pages/agency_detail_page.dart';
import 'pages/agency_list_page.dart';
import 'pages/agency_request/agency_request_page.dart';
import 'pages/classified_property/add_classified_property.dart';
import 'pages/classified_property/classified_property_detail_page.dart';
import 'pages/classified_property/classified_property_list_page.dart';
import 'pages/dashboard.dart';
import 'pages/explore_feature/category_item_detail_page.dart';
import 'pages/explore_feature/category_item_list_page.dart';
import 'pages/explore_feature/category_list_page.dart';
import 'pages/explore_feature/categrory_search_page.dart';
import 'pages/explore_feature/explore_main_page.dart';
import 'pages/login_page.dart';
import 'pages/maps_v2/maps_v2.dart';
import 'pages/news_detail_page.dart';
import 'pages/news_list_page.dart';
import 'pages/plot_finder/plot_locator_page.dart';
import 'pages/plot_finder/plot_selection.dart';
import 'pages/project_feature/item_detail_page.dart';
import 'pages/project_feature/project_details.dart';
import 'pages/project_feature/project_list_page.dart';
import 'pages/properties_list/favrouite_properties_list_page.dart';
import 'pages/properties_list/properties_list_page.dart';
import 'pages/rate_list.dart';
import 'pages/reset_password_page.dart';
import 'pages/settings_page.dart';
import 'pages/society_list_page.dart';
import 'pages/update_profile_page.dart';
import 'pages/video_list_page.dart';
import 'pages/video_play_page.dart';
import 'widgets/gallery_view.dart';

class Nav {
  static push(context, Widget widget) {
    Navigator.push(context, MaterialPageRoute(builder: (context) => widget));
  }

  static void pushReplacement(context, Widget widget) {
    Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => widget));
  }

  static void back(context) {
    Navigator.pop(context);
  }

  static void internalLink(BuildContext context,String type, String value){

    switch (type) {
      case 'url':
        openLink(value);
        break;
      case 'agency':
        Nav.agency(context, int.tryParse(value));
        break;
      case 'project':
        Nav.projectDetailPage(context, int.tryParse(value), null);
        break;
      case 'classified':
        Nav.classifiedPropertyDetailPage(context, int.tryParse(value), null);
        break;
      case 'map':
      // Examples 120 , 120_220 : first is society id, second is block id
        var parts = value.toString().split('_');
        if (parts.length == 1) {
          Nav.mapsV2Page(context, societyId: int.tryParse(parts[0])); // society id
        } else if (parts.length == 2) {
          Nav.mapsV2Page(context, societyId: int.tryParse(parts[0]), blockId: int.tryParse(parts[1])); // society id , block id
        }
        break;

    }
  }

  static societyList(context) {
    Nav.push(context, SocietyListPage());
  }

  // static feedbackDialogPage(context) {
  //   Nav.push(context, ShowFeedbackDialog());
  // }

  static updateProfile(context) {
    Nav.push(context, UpdateProfilePage());
  }

  static agencyList(context) {
    Nav.push(context, AgencyListPage());
  }

  static agencyListWithQuery(context, query) {
    Nav.push(context, AgencyListPage(query: query));
  }

  static agency(context, id) {
    Nav.push(context, AgencyDetailPage(id));
  }

  static newsListPage(context) {
    Nav.push(context, NewsListPage());
  }

  static newsDetailPage(context, {String? id, String? banner}) {
    Nav.push(context, NewsDetailPage(id: id!, banner: banner!));
  }
  static marketInventoryDetailPage(context, int id) {
    Nav.push(context, MarketInventoryDetails( id: id,));
  }

  static about(context) {
    Nav.push(context, AboutPage());
  }
  static feedbackPage(context) {
    Nav.pushReplacement(context, FeedbackPage());
  }

  static submitAgency(context) {
    Nav.push(context, AgencyRequestPage());
  }

  static homePage(context) {
    Nav.pushReplacement(context, DashBoardPage());
  }

  static void userLocationPage(BuildContext context) {
    Nav.pushReplacement(context, UserLocationSelection());
  }

  static void plotSelectionPage(BuildContext context) {
    Nav.push(context, ProgressHUD(barrierEnabled: false, child: PlotSelectionPage()));
  }

  static void mapsV2Page(BuildContext context, {int? societyId, int? blockId}) {
    Nav.push(context, ProgressHUD(barrierEnabled: false, child: ChangeNotifierProvider(create: (_) => MapProvider(), child: MapsV2(societyId: societyId, blockId: blockId,))));
  }

  static propertiesPage(context, {id = 0, agencyId = 0, bool isFav = false}) {
    Nav.push(context, ChangeNotifierProvider(
        create: (_) => FiltersProvider(isDeepLink: id != 0 || agencyId != 0),
        child: PropertiesListPage(id: id, agencyId: agencyId, isFav: isFav)));
  }

  static loginPage(context) {
    Nav.push(context, LoginPage());
  }

  static resetPasswordPage(context) {
    Nav.push(context, ResetPasswordPage());
  }

  static addPropertyPage(context, {id}) {
    final LoginController login = Get.find();
    if (login.isLoggedIn)
      Nav.push(context, ProgressHUD(barrierEnabled: false, child: AddPropertyPageV2(propertyId: id)));
    else
      Nav.loginPage(context);
  }

  static addClassifiedProperty(context, {id, logo}) {
    final LoginController login = Get.find();
    if (login.isLoggedIn)
      Nav.push(
        context,
        ProgressHUD(
          barrierEnabled: false,
          child: ChangeNotifierProvider(
            create: (_) => LocationSelectionProvider(plotsNeeded: true),
            child: AddClassifiedProperty(propertyId: id, logo: logo),
          ),
        ),
      );
    else
      Nav.loginPage(context);
  }
  static addMarketInventory(context, {id}) {
    final LoginController login = Get.find();
    if (login.isLoggedIn)
      Nav.push(context, ProgressHUD(barrierEnabled: false, child: AddMarketInventory(propertyId: id)));
    else
      Nav.loginPage(context);
  }
  static marketInventoryList(context) {
    final LoginController login = Get.find();
    if (login.isLoggedIn)
      Nav.push(context, ProgressHUD(barrierEnabled: false, child: ChangeNotifierProvider(
          create: (_) => FiltersProvider(),
          child: MarketInventoryList())));
    else
      Nav.loginPage(context);
  }

  static void singlePlotMap(context, block, plot) {
    Nav.push(context, PlotLocatorPage(block, plot));
  }

  static void projectListPage(context) {
    Nav.push(context, ChangeNotifierProvider(
        create: (_) => FiltersProvider(),
        child: ProjectListPage()));
  }

  static void projectDetailPage(context, int? id, String? logo) {
    Nav.push(context, ProjectDetailPage(id, logo));
  }

  static void itemDetailPage(context, ProjectItems item, List<String>? photos) {
    Nav.push(context, ItemDetailPage(item, photos));
  }

  static void classifiedPropertyListPage(context, {staffId = null, agencyId = null}) {
    Nav.push(context, ChangeNotifierProvider(
        create: (_) => FiltersProvider(isDeepLink: staffId != null || agencyId != null),
        child: ClassifiedPropertyListPage(staffId: staffId, agencyId: agencyId)));
  }

  static void classifiedPropertyDetailPage(context, int? id, String? logo, {int? isHomeClick}) {
    Nav.push(context, ClassifiedPropertyDetailPage(id!, logo!, isHomeClick: isHomeClick));
  }

  static void galleryView(BuildContext context, List<ClassifiedMedia> media) {
    Nav.push(context, GalleryView(media));
  }

  static void videoListPage(context) {
    Nav.push(context, VideoListPage());
  }

  static void selectClassifiedFilters(context) {
    Nav.push(context, SelectClassifiedFilters());
  }

  static void videoPlayPage(context, VideoEntity? data) {
    Nav.push(context, VideoPlayPage(data!));
  }

  static void rateList(context, id, title) {
    Nav.push(context, RateList(id, title));
  }

  static void settingsPage(context) {
    Nav.push(context, SettingsPage());
  }

  static void favPropertyListPage(context) {
    Nav.push(context, FavouritePropertiesListPage());
  }

  static void exploreMainPage(context) {
    Nav.push(context, ExploreMainPage());
  }

  static void categoryItemDetailPage(context, int? id, String? logo, {int? isHomeClick}) {
    Nav.push(context, CategoryItemDetailPage(id!, logo!, isHomeClick: isHomeClick));
  }

  static void categoryListPage(context) {
    Nav.push(context, CategoryListPage());
  }

  static void categoryItemListPage(context, {ExploreShortCategory? category}) {
    Get.delete<ExploreController>();
    Get.put(ExploreController());
    Nav.push(context, CategoryItemListPage(category: category));
  }

  static void categorySearchPage(context, {required Function callBack}) {
    Nav.push(context, CategorySearchPage(callBack: callBack));
  }
}
