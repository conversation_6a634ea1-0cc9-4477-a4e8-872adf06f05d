import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:property_dealers_directory/theme.dart';

class MainTextFieldPadding extends StatelessWidget {
  const MainTextFieldPadding({
    Key? key,
    required this.screenWidth,
    required this.screenHeight,
    required this.textField,
  }) : super(key: key);

  final double screenWidth;
  final double screenHeight;
  final FormBuilderTextField textField;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: screenWidth * 0.08, right: screenWidth * 0.07, top: screenHeight * 0.015),
      child: textField,
    );
  }
}

class LabelWithText extends StatelessWidget {
  const LabelWithText({
    Key? key,
    this.txt1,
    this.txt2,
    this.color,
  }) : super(key: key);

  final String? txt1;
  final String? txt2;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return RichText(
      text: TextSpan(
        text: txt1,
        style: TextStyle(color: Colors.black54),
        children: [
          TextSpan(text: txt2, style: TextStyle(color: color == null ? Colors.black : color)),
        ],
      ),
    );
  }
}

class InputStyle extends InputDecoration {
  @override
  bool get filled => true;

  // @override
  // EdgeInsetsGeometry get contentPadding => const EdgeInsets.all(15.0);

  @override
  InputBorder get focusedBorder => OutlineInputBorder(borderSide: BorderSide.none);

  @override
  InputBorder get enabledBorder => OutlineInputBorder(borderSide: BorderSide.none);

  @override
  InputStyle({String? hint, IconData? icon, Widget? suffixIcon})
      : super(
          hintText: hint,
          hintStyle: TextStyle(color: Colors.grey),
          prefixIcon: icon == null ? null : Icon(icon, size: 20),
          suffixIcon: suffixIcon == null ? null : suffixIcon,
          contentPadding: const EdgeInsets.all(15.0),
        );

  InputStyle.tight({String? hint, IconData? icon, double height = 35})
      : super(
          hintText: hint,
          hintStyle: TextStyle(color: MyColors.primary, fontSize: 14),
          fillColor: Colors.white,
          suffixIconColor: MyColors.primary,
          prefixIcon: icon == null ? null : Icon(icon, size: 20),
          contentPadding: const EdgeInsets.symmetric(horizontal: 10),
          constraints: BoxConstraints.tight(Size(200, height)),
        );
}
