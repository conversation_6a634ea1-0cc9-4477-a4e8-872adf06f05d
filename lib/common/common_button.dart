import 'package:flutter/material.dart';

class CommonButton extends StatelessWidget {
  const CommonButton({required this.screenHeight, required this.screenWidth, required this.txt, this.color});

  final double screenHeight;
  final double screenWidth;
  final String txt;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: screenHeight * 0.06,
      width: screenWidth,
      decoration:
          BoxDecoration(borderRadius: BorderRadius.circular(3.0), color: color ?? Theme.of(context).primaryColor),
      alignment: Alignment.center,
      child: Text(
        txt,
        style: TextStyle(
          fontSize: 16,
          color: Colors.white,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }
}
