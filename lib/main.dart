import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'package:google_mobile_ads/google_mobile_ads.dart';
import 'package:property_dealers_directory/firebase_options.dart';
import 'package:property_dealers_directory/providers/onboarding_provider.dart';
import 'package:property_dealers_directory/theme.dart';
import 'package:provider/provider.dart';
import 'config.dart';
import 'my_app.dart';
import 'services/remote_config.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform
  );
  await GetStorage.init();
  FirebaseAnalytics analytics;
  FirebaseAnalyticsObserver observer;
  analytics = FirebaseAnalytics.instance;
  observer = FirebaseAnalyticsObserver(analytics: analytics);
  await RemoteConfigService.fetch();

  if (!kIsWeb) {
    MobileAds.instance.initialize();
  }
  obProvider = OnboardingProvider();

  runApp(
    ChangeNotifierProvider.value(
      value: obProvider,
      child: GetMaterialApp(
        title: Config.HOME_TITLE,
        debugShowCheckedModeBanner: false,
        theme: theme,
        navigatorObservers: kIsWeb ? [] : <NavigatorObserver>[observer],
        home: MyApp(),
      ),
    ),
  );
}

