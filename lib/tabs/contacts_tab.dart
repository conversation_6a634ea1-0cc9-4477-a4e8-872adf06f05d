import 'package:flutter/material.dart';

import '../models/agency_entity.dart';
import '../widgets/social.dart';

class ContactsTab extends StatelessWidget {
  final AgencyEntity agency;

  ContactsTab(this.agency);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(10.0),
      child: ListView(
        children: <Widget>[
          if (agency.name.isNotEmpty) SocialIcon.type(agency.ceo, SocialIcon.NAME),
          if (agency.mobile1.isNotEmpty) SocialIcon.type(agency.mobile1, SocialIcon.MOBILE),
          if (agency.mobile2!.isNotEmpty) SocialIcon.type(agency.mobile2, SocialIcon.MOBILE),
          if (agency.landline1!.isNotEmpty) SocialIcon.type(agency.landline1, SocialIcon.PHONE),
          if (agency.landline2!.isNotEmpty) SocialIcon.type(agency.landline2, SocialIcon.PHONE),
          if (agency.fax!.isNotEmpty) SocialIcon.type(agency.fax, SocialIcon.FAX),
        ],
      ),
    );
  }
}
