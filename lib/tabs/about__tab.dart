import 'package:flutter/material.dart';

import '../models/agency_entity.dart';

class AboutDetailTab extends StatelessWidget {
  final AgencyEntity agency;

  AboutDetailTab(this.agency);

  @override
  Widget build(BuildContext context) {
    if (agency.description == null) {
      return Text("No Agency Detail");
    } else {
      return Container(
        child: ListView(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(agency.description!),
            ),
          ],
        ),
      );
    }
  }
}
