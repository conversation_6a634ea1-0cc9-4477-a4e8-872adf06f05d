import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:latlong2/latlong.dart';
import 'package:maps_launcher/maps_launcher.dart';
import 'package:property_dealers_directory/pages/maps_v2/maps_launcher_custom.dart';

import '../models/agency_entity.dart';
import '../pages/maps_v2/cached_network_tile_provider.dart';
import '../utils/functions.dart';

class LocationDetailTab extends StatelessWidget {
  final AgencyEntity agency;

  LocationDetailTab(this.agency);

  @override
  Widget build(BuildContext context) {
    final LatLng _center = LatLng(double.parse(agency.latitude!), double.parse(agency.longitude!));

    return Stack(
      children: <Widget>[
        Container(
          child: FlutterMap(
            options: MapOptions(
                // center: _center,
                initialCenter: _center,
                // zoom: 15.0,
                initialZoom: 15.0,
                maxZoom: 18.0),
            children: [
              TileLayer(
                urlTemplate: "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
                subdomains: ['a', 'b', 'c'],
                tileProvider: CachedNetworkTileProvider(),
              ),
              MarkerLayer(
                markers: [
                  Marker(
                    point: _center,
                    // anchorPos: AnchorPos.align(AnchorAlign.top),
                    child: Icon(Icons.location_on, color: Colors.blue, size: 30.0),
                  ),
                ],
              )
            ],
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          child: Center(
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                    begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [Colors.white, Color(0x00FFFFFF)]),
              ),
              child: Text(agency.address, textAlign: TextAlign.center, style: TextStyle(fontSize: 18)),
            ),
          ),
        ),
        Positioned(
          right: 0,
          bottom: 0,
          child: Container(
            padding: EdgeInsets.all(8.0),
            child: FloatingActionButton(
              mini: true,
              backgroundColor: Theme.of(context).primaryColor,
              child: Icon(FontAwesomeIcons.diamondTurnRight),
              onPressed: () {
                String? mapLabel;
                if (!kIsWeb) {
                  mapLabel = Platform.isIOS
                      ? "${_center.latitude}, ${_center.longitude}"
                      : "  ${agency.name}, ${agency.society}, ${agency.city} ";
                }
                // MapsLauncher.launchCoordinates(_center.latitude, _center.longitude, mapLabel);
                MapsLauncherCustom.launchCoordinates(_center.latitude, _center.longitude, mapLabel);
                sendAnalyticsEvent(
                    eventName: 'detail_agency_google_map_button_click', parameters: {'agency_id': agency.id});
              },
            ),
          ),
        )
      ],
    );
  }
}
