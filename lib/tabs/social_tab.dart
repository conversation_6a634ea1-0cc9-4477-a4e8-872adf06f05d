import 'package:flutter/material.dart';

import '../models/agency_entity.dart';
import '../widgets/social.dart';

class SocialDetailTab extends StatelessWidget {
  final AgencyEntity agency;

  SocialDetailTab(this.agency);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: getList(),
      ),
    );
  }

  getList() {
    List<Widget> list = [];
    if (agency.facebook != null && agency.facebook!.isNotEmpty)
      list.add(SocialIcon.type(agency.facebook, SocialIcon.FACEBOOK));
    if (agency.twitter != null && agency.twitter!.isNotEmpty)
      list.add(SocialIcon.type(agency.twitter, SocialIcon.TWITTER));
    if (agency.instagram != null && agency.instagram!.isNotEmpty)
      list.add(SocialIcon.type(agency.instagram, SocialIcon.INSTAGRAM));
    if (agency.youtube != null && agency.youtube!.isNotEmpty)
      list.add(SocialIcon.type(agency.youtube, SocialIcon.YOUTUBE));
    if (agency.email != null && agency.email!.isNotEmpty) list.add(SocialIcon.type(agency.email, SocialIcon.EMAIL));
    if (agency.website != null && agency.website!.isNotEmpty) list.add(SocialIcon.type(agency.website, SocialIcon.WEB));

    return list;
  }
}
