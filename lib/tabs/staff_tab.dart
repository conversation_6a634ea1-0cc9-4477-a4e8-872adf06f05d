import 'package:flutter/material.dart';

import '../models/staff_entity.dart';
import '../utils/widgets.dart';
import '../widgets/social.dart';

class StaffTab extends StatelessWidget {
  final List<StaffEntity?>? staff;

  StaffTab(this.staff);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      physics: ClampingScrollPhysics(),
      itemCount: staff!.length,
      itemBuilder: (context, index) {
        StaffEntity current = staff![index]!;
        return ListTile(
          title: Text(current.name),
          subtitle: Text(current.designation),
          leading: NetworkImageWithPlaceholder(current.imageUrl, height: 50, width: 50, circular: true),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              SocialIcon.type(current.phone, SocialIcon.PHONE),
              SizedBox(width: 10.0),
              SocialIcon.type(current.phone, SocialIcon.WHATSAPP),
            ],
          ),
        );
      },
    );
  }
}
