import 'package:find_dropdown/find_dropdown.dart';
import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';

import '../../common/main_textField.dart';
import '../../pages/maps_v2/drop_down_container.dart';
import '../../utils/widgets.dart';
import 'dropdown_able.dart';

class FindDropDownField extends FormBuilderField<DropDownAble?> {


  FindDropDownField({
    Key? key,
    required String name,
    required String hint,
    required BuildContext context,
    String? label,
    List<DropDownAble>? items,
    DropDownAble? selectedItem,
    String? resetField,
    AutovalidateMode autovalidateMode = AutovalidateMode.disabled,
    bool enabled = true,
    FocusNode? focusNode,
    FormFieldSetter<DropDownAble>? onSaved,
    FormFieldValidator<DropDownAble>? validator,
    InputDecoration decoration = const InputDecoration(),
    DropDownAble? initialValue,
    ValueChanged<DropDownAble?>? onChanged,
    ValueTransformer<DropDownAble?>? valueTransformer,
    VoidCallback? onReset,
  }) : super(
            // key: key,
            initialValue: initialValue,
            name: name,
            validator: validator,
            valueTransformer: valueTransformer ?? (value) => value?.itemId,
            onChanged: onChanged,
            autovalidateMode: autovalidateMode,
            onSaved: onSaved,
            enabled: enabled,
            onReset: onReset,
            // decoration: decoration,
            focusNode: focusNode,
            builder: (FormFieldState<DropDownAble?> field) {
              final state = field as _FindDropDownFieldState;

              return FindDropdown<DropDownAble?>(
                key: key,
                selectedItem: selectedItem ?? state.value,
                constraints: BoxConstraints.tight(MediaQuery.of(context).size * .5),
                label: label,
                labelStyle: TextStyle(fontSize: 12, color: Colors.white),
                labelVisible: label != null,
                searchBoxDecoration: InputStyle(hint: 'Search Here'),
                items: items,
                onFind: (value) async {
                  if (items == null) return [];
                  if (value.isEmpty) return items;
                  value = value.toLowerCase();
                  return items.where((item) {
                    String name = item.searchBy.toLowerCase();
                    return name.contains(value);
                  }).toList();
                },
                emptyBuilder: (context) {
                  return FullLoader();
                },
                dropdownItemBuilder: (BuildContext context, DropDownAble? item, bool isSelected) {
                  return ListTile(selected: isSelected, title: Text(item!.listLabel));
                },
                dropdownBuilder: (BuildContext context, item) {
                  return DropDownContainer(item: item?.selectedLabel, hint: hint);
                },
                onChanged: (value) {
                  if(resetField != null){
                    state.setState(() {
                      state.formState?.fields[resetField]?.didChange(null);
                    });
                  }
                  state.didChange(value);
                },
              );
            });

  @override
  _FindDropDownFieldState createState() => _FindDropDownFieldState();
}

class _FindDropDownFieldState extends FormBuilderFieldState<FindDropDownField, DropDownAble?> {}
