// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCbBNCrH2U2acxdBtfsJsQJVTCHeRnSIMc',
    appId: '1:975249424645:web:aff155026b762fbe87776c',
    messagingSenderId: '975249424645',
    projectId: 'bahria-plus-directory',
    authDomain: 'bahria-plus-directory.firebaseapp.com',
    databaseURL: 'https://bahria-plus-directory.firebaseio.com',
    storageBucket: 'bahria-plus-directory.appspot.com',
    measurementId: 'G-8W84ZTLTG8',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyAomyPUf6SK73sJ1kvbFtJPJR9ntyj2Fmc',
    appId: '1:975249424645:android:8c6af8852a7f6f3787776c',
    messagingSenderId: '975249424645',
    projectId: 'bahria-plus-directory',
    databaseURL: 'https://bahria-plus-directory.firebaseio.com',
    storageBucket: 'bahria-plus-directory.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyCbMpc5WCXUADhrpwnqxTukeIQJManZwjg',
    appId: '1:975249424645:ios:a668f6f0bda480eb87776c',
    messagingSenderId: '975249424645',
    projectId: 'bahria-plus-directory',
    databaseURL: 'https://bahria-plus-directory.firebaseio.com',
    storageBucket: 'bahria-plus-directory.appspot.com',
    iosClientId: '975249424645-skq0gpoj93dims17fdkb5p03stqrqfvj.apps.googleusercontent.com',
    iosBundleId: 'com.ioi.directory.bahria-plus',
  );
}
