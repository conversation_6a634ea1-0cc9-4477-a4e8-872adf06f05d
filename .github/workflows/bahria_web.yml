name: BAHRIA Deploy to firebase web release
on:
  # Enable manual run
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to (staging OR prod)'
        required: false
        default: 'prod'
  # Refs/tags push events to matching v*, i.e. v1.0, v20.15.10
  push:
    tags:
      - 'bahria_v*'

jobs:
  build-and-deploy:
    runs-on: ubuntu-22.04
    steps:
      # Set up Flutter.
      - name: Clone Flutter repository with master channel
        uses: subosito/flutter-action@v2
        with:
          channel: stable
      - run: flutter doctor -v

      - name: Install web dependencies
        uses: actions/setup-node@v1
        with:
          node-version: '18'
      - run: npm install -g firebase-tools

      # Checkout gallery code and get packages.
      - name: Checkout code
        uses: actions/checkout@v2
      - run: flutter pub get

      # Build and deploy (by default, to staging).
      - run: flutter build web --web-renderer canvaskit --release
      - run: firebase deploy --only hosting --token "${FIREBASE_TOKEN}"
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}