name: <PERSON><PERSON> Deploy to Play Store ACTION
on:
  # Enable manual run
  workflow_dispatch:
    inputs:
      lane:
        description: 'Fastlane lane to use (beta OR promote_to_production OR production)'
        required: true
        default: 'production'
  # Refs/tags push events to matching v*, i.e. v1.0, v20.15.10
  push:
    tags:
      - 'dha_v*'

jobs:
  fastlane-deploy:
    runs-on: ubuntu-22.04
    steps:
      - name: Setup Java 11
        uses: actions/setup-java@v3
        with:
          distribution: 'zulu'
          java-version: '11'
      # Set up Flutter.
      - name: Clone Flutter repository with stable channel
        uses: subosito/flutter-action@v2
        with:
          channel: stable
          flutter-version: 3.16.2
      - run: flutter doctor -v

      # Checkout gallery code and get packages.
      - name: Checkout code
        uses: actions/checkout@v3
        with:
            ref: dha_branch
      - run: flutter pub get

      # Setup Ruby, Bundler, and Gemfile dependencies
      - name: Setup Ruby
        uses: ruby/setup-ruby@v1
        with:
          ruby-version: '2.7.2'
          working-directory: android
      # Build and deploy with Fastlane (by default, to beta track) 🚀.
      # Naturally, promote_to_production only deploys.
      - name: Run FastLane
        uses: maierj/fastlane-action@v2.2.1
        with:
          lane: ${{ github.event.inputs.lane || 'production' }}
          subdirectory: android
        env:
          PLAY_STORE_CONFIG_JSON: ${{ secrets.PLAY_STORE_CONFIG_JSON_DHA }}
