name: il<PERSON><PERSON> Deploy to firebase web release
on:
  # Enable manual run
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to (staging OR prod)'
        required: false
        default: 'prod'
  # Refs/tags push events to matching v*, i.e. v1.0, v20.15.10
  push:
    tags:
      - 'ila_v*'

jobs:
  build-and-deploy:
    runs-on: ubuntu-24.04
    steps:
      # Set up Flutter.
      - uses: actions/checkout@v4
        with:
          ref: ilaqa-changes-first
      - uses: kuhnroyal/flutter-fvm-config-action/setup@v3
      - run: flutter doctor -v

      - name: Install web dependencies
        uses: actions/setup-node@v1
        with:
          node-version: '20'
      - run: npm install -g firebase-tools

      # Checkout gallery code and get packages.
      - name: Start Flutter Build
        run: flutter pub get

      # Build and deploy (by default, to staging).
      - run: flutter build web --release
      - run: firebase deploy --only hosting:ilaaqa --token "${FIREBASE_TOKEN}"
        env:
          FIREBASE_TOKEN: ${{ secrets.FIREBASE_TOKEN }}
