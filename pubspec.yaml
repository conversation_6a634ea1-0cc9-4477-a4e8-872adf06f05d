name: property_dealers_directory
description: A new Flutter project.
version: 8.3.2+98

# derry buildweb
scripts:
  build64: flutter build apk --release --split-per-abi --target-platform android-arm64
  buildweb: flutter build web --web-renderer canvaskit --release

environment:
  sdk: ">=3.7.0 <4.0.0"

dependencies:
  flutter:
    sdk: flutter

  auto_size_text: ^3.0.0
  font_awesome_flutter: ^10.8.0
  cached_network_image: ^3.4.1
  url_launcher: ^6.3.2
  fluttertoast: ^8.2.12
  awesome_dialog: ^3.2.1
  pull_to_refresh: ^2.0.0
  flutter_map: ^8.2.1
  maps_launcher: ^3.0.0+1
#  share: ^2.0.4
  share_plus: ^11.0.0
  rate_my_app: ^2.3.2
  flutter_form_builder: ^10.1.0
  form_builder_validators: ^11.2.0
  image_picker: ^1.1.2
  firebase_analytics: ^11.6.0
  lottie: ^3.3.1
  sized_context: ^1.0.0+4
  get: ^4.7.2
  find_dropdown:
    git: https://github.com/beerpcc/find_dropdown.git
  get_storage: ^2.1.1
  firebase_messaging: ^15.2.10
  dio: ^5.8.0+1
#  dio_http_cache: ^0.3.0
  substring_highlight: ^1.0.33
  readmore: ^3.0.0
  firebase_remote_config: ^5.5.0
  google_mobile_ads: ^6.0.0
  latlong2: ^0.9.1
  dropdown_search: ^6.0.2
#  image_gallery_saver: ^2.0.3
  image_gallery_saver_plus: ^4.0.1
  permission_handler: ^12.0.1
  carousel_slider: ^5.1.1
  flutter_multi_select_items: ^0.4.3
  percent_indicator: ^4.2.5
  flutter_progress_hud: ^2.0.2
#  facebook_app_events: ^0.19.0
  youtube_player_flutter: ^9.1.1
  cropperx: ^1.1.1
  flutter_widget_from_html_core: ^0.17.0
  device_info_plus: ^11.5.0
  unique_identifier: ^0.4.0
  encrypt: ^5.0.3
  path_provider: ^2.1.5
  package_info_plus: ^8.3.0
  double_back_to_close_app: ^2.1.0
  im_stepper: ^1.0.1+1
  uuid: ^4.5.1
  provider: ^6.1.5
  highlight_text: ^1.8.0
  form_builder_extra_fields: ^12.1.0
  screenshot: ^3.0.0
#  gallery_saver: ^2.3.2
#  http: ^0.13.3

dependency_overrides:
  flare_flutter:
    git:
      url: https://github.com/tsinis/Flare-Flutter
      path: flare_flutter/
  pull_to_refresh:
    git:
      url: https://github.com/miquelbeltran/flutter_pulltorefresh
      ref: 6d7fbdd
  select_dialog: ^2.0.2
  intl: ^0.20.2
  win32: ^5.14.0
  archive: ^4.0.7
  flutter_inappwebview: ^6.1.5
#  flutter_inappwebview:
#    git: https://github.com/CodeEagle/flutter_inappwebview

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_launcher_icons: ^0.13.1


# flutter pub run flutter_launcher_icons:main
flutter_icons:
  android: true
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "images/logo.png"
  adaptive_icon_foreground_inset: 32
  ios: true
  remove_alpha_ios: true
  image_path: "images/logo.png"

flutter:
  uses-material-design: true

  assets:
    - images/
    - images/icons/
    - images/plot-icons/
    - animations/

  fonts:
    - family: Comfortaa
      fonts:
        - asset: fonts/Comfortaa-Bold.ttf
          weight: 700
        - asset: fonts/Comfortaa-Regular.ttf
        - asset: fonts/Comfortaa-Medium.ttf
          weight: 500
        - asset: fonts/Comfortaa-SemiBold.ttf
          weight: 600
        - asset: fonts/Comfortaa-Light.ttf
          weight: 300