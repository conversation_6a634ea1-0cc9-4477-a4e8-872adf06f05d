importScripts('https://www.gstatic.com/firebasejs/8.4.1/firebase-app.js');
importScripts('https://www.gstatic.com/firebasejs/8.4.1/firebase-messaging.js');

   /*Update with yours config*/
  const firebaseConfig = {
            apiKey: "AIzaSyCbBNCrH2U2acxdBtfsJsQJVTCHeRnSIMc",
            authDomain: "bahria-plus-directory.firebaseapp.com",
            databaseURL: "https://bahria-plus-directory.firebaseio.com",
            projectId: "bahria-plus-directory",
            storageBucket: "bahria-plus-directory.appspot.com",
            messagingSenderId: "975249424645",
            appId: "1:975249424645:web:aff155026b762fbe87776c",
            measurementId: "G-8W84ZTLTG8"
        };
  firebase.initializeApp(firebaseConfig);
  const messaging = firebase.messaging();

  /*messaging.onMessage((payload) => {
  console.log('Message received. ', payload);*/
  messaging.onBackgroundMessage(function(payload) {
    console.log('Received background message ', payload);

    const notificationTitle = payload.notification.title;
    const notificationOptions = {
      body: payload.notification.body,
    };

    self.registration.showNotification(notificationTitle,
      notificationOptions);
  });