# Uncomment this line to define a global platform for your project
# platform :ios, '12.0'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

platform :ios, '13.0'

def flutter_root
  generated_xcode_build_settings_path = File.expand_path(File.join('..', 'Flutter', 'Generated.xcconfig'), __FILE__)
  unless File.exist?(generated_xcode_build_settings_path)
    raise "#{generated_xcode_build_settings_path} must exist. If you're running pod install manually, make sure flutter pub get is executed first"
  end

  File.foreach(generated_xcode_build_settings_path) do |line|
    matches = line.match(/FLUTTER_ROOT\=(.*)/)
    return matches[1].strip if matches
  end
  raise "FLUTTER_ROOT not found in #{generated_xcode_build_settings_path}. Try deleting Generated.xcconfig, then run flutter pub get"
end

require File.expand_path(File.join('packages', 'flutter_tools', 'bin', 'podhelper'), flutter_root)

flutter_ios_podfile_setup

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  flutter_install_all_ios_pods File.dirname(File.realpath(__FILE__))
end

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      # Here are some configurations automatically generated by flutter
      flutter_additional_ios_build_settings(target)
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.3'
      end
      # You can remove unused permissions here
      # for more infomation: https://github.com/BaseflowIT/flutter-permission-handler/blob/develop/permission_handler/ios/Classes/PermissionHandlerEnums.h
      # e.g. when you don't need camera permission, just add 'PERMISSION_CAMERA=0'
      config.build_settings['GCC_PREPROCESSOR_DEFINITIONS'] ||= [
        '$(inherited)',

        ## dart: PermissionGroup.calendar
         'PERMISSION_EVENTS=0',

        ## dart: PermissionGroup.reminders
         'PERMISSION_REMINDERS=0',

        ## dart: PermissionGroup.contacts
         'PERMISSION_CONTACTS=0',

        ## dart: PermissionGroup.camera
         'PERMISSION_CAMERA=0',

        ## dart: PermissionGroup.microphone
         'PERMISSION_MICROPHONE=0',

        ## dart: PermissionGroup.speech
         'PERMISSION_SPEECH_RECOGNIZER=0',

        ## dart: PermissionGroup.photos
         'PERMISSION_PHOTOS=0',

        ## dart: [PermissionGroup.location, PermissionGroup.locationAlways, PermissionGroup.locationWhenInUse]
         'PERMISSION_LOCATION=0',

        ## dart: PermissionGroup.notification
         'PERMISSION_NOTIFICATIONS=0',

        ## dart: PermissionGroup.mediaLibrary
         'PERMISSION_MEDIA_LIBRARY=0',

        ## dart: PermissionGroup.sensors
         'PERMISSION_SENSORS=0'
      ]

    end
  end
end

post_integrate do |installer|
  compiler_flags_key = 'COMPILER_FLAGS'
  project_path = 'Pods/Pods.xcodeproj'

  project = Xcodeproj::Project.open(project_path)
  project.targets.each do |target|
    target.build_phases.each do |build_phase|
      if build_phase.is_a?(Xcodeproj::Project::Object::PBXSourcesBuildPhase)
        build_phase.files.each do |file|
          if !file.settings.nil? && file.settings.key?(compiler_flags_key)
            compiler_flags = file.settings[compiler_flags_key]
            file.settings[compiler_flags_key] = compiler_flags.gsub(/-DOS_OBJECT_USE_OBJC=0\s*/, '')
          end
        end
      end
    end
  end
  project.save()
end
